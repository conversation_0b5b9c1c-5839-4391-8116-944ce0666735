{"cells": [{"cell_type": "markdown", "id": "b2aaa7db", "metadata": {}, "source": ["## New paid user by month"]}, {"cell_type": "code", "execution_count": 11, "id": "b7c6f4d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EPPI new paid users (2408-2506):\n", "  date_period           type  cv_user source\n", "0  2024-08-01            Web     7188   EPPI\n", "1  2024-08-01  Pre-installed     1720   EPPI\n", "2  2024-08-01        Prepaid     3805   EPPI\n", "3  2024-09-01        Prepaid    11006   EPPI\n", "4  2024-09-01            Web    25530   EPPI\n", "5  2024-09-01  Pre-installed     7189   EPPI\n", "6  2024-10-01            <PERSON>    33542   EPPI\n", "7  2024-10-01  Pre-installed     7312   EPPI\n", "8  2024-10-01        Prepaid    12811   EPPI\n", "9  2024-11-01            Web    29197   EPPI\n", "\n", "MFC new paid users (2301-2407):\n", "  date_period           type  cv_user     source\n", "0  2023-01-01  Pre-installed    20600  MFC_Early\n", "1  2023-01-01        Prepaid    17795  MFC_Early\n", "2  2023-01-01            Web    35639  MFC_Early\n", "3  2023-02-01        Prepaid    15783  MFC_Early\n", "4  2023-02-01  Pre-installed    13306  MFC_Early\n", "5  2023-02-01            Web    27687  MFC_Early\n", "6  2023-03-01  Pre-installed    12865  MFC_Early\n", "7  2023-03-01        Prepaid    16295  MFC_Early\n", "8  2023-03-01            Web    27280  MFC_Early\n", "9  2023-04-01  Pre-installed    17605  MFC_Early\n", "\n", "MFC Not EPPI new paid users (2408-2506):\n", "  date_period           type  cv_user        source\n", "0  2024-08-01        Prepaid     1491  MFC_Not_EPPI\n", "1  2024-08-01  Pre-installed     1699  MFC_Not_EPPI\n", "2  2024-08-01            Web    10333  MFC_Not_EPPI\n", "3  2024-09-01  Pre-installed     1330  MFC_Not_EPPI\n", "4  2024-09-01        Prepaid      733  MFC_Not_EPPI\n", "5  2024-09-01            Web     7140  MFC_Not_EPPI\n", "6  2024-10-01  Pre-installed     1202  MFC_Not_EPPI\n", "7  2024-10-01        Prepaid      703  MFC_Not_EPPI\n", "8  2024-10-01            Web     6256  MFC_Not_EPPI\n", "9  2024-11-01  Pre-installed     1231  MFC_Not_EPPI\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# Read new paid user data from the three CSV files\n", "eppi_df = pd.read_csv('New paid user EPPI 2408-2506.csv')\n", "mfc_early_df = pd.read_csv('New paid user MFC 2301-2407.csv')\n", "mfc_not_eppi_df = pd.read_csv('New paid user MFC Not EPPI 2408-2506.csv')\n", "\n", "# eppi_df = pd.read_csv('New paid user online offline EPPI 2408-2506.csv')\n", "# mfc_early_df = pd.read_csv('New paid user online offline MFC 2301-2407.csv')\n", "# mfc_not_eppi_df = pd.read_csv('New paid user online offline MFC Not EPPI 2408-2506.csv')\n", "\n", "# Add source identifier to each dataset\n", "eppi_df['source'] = 'EPPI'\n", "mfc_early_df['source'] = 'MFC_Early'\n", "mfc_not_eppi_df['source'] = 'MFC_Not_EPPI'\n", "\n", "# Preview the data\n", "print(\"EPPI new paid users (2408-2506):\")\n", "print(eppi_df.head(10))\n", "\n", "print(\"\\nMFC new paid users (2301-2407):\")\n", "print(mfc_early_df.head(10))\n", "\n", "print(\"\\nMFC Not EPPI new paid users (2408-2506):\")\n", "print(mfc_not_eppi_df.head(10))\n"]}, {"cell_type": "code", "execution_count": 12, "id": "2c9de9e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   date_period           type  cv_user source\n", "28  2025-05-01        Prepaid     7852   EPPI\n", "29  2025-05-01            Web    18452   EPPI\n", "30  2025-06-01  Pre-installed     5966   EPPI\n", "31  2025-06-01            Web    16297   EPPI\n", "32  2025-06-01        Prepaid     6870   EPPI\n", "   date_period           type  cv_user        source\n", "28  2025-05-01        Prepaid      471  MFC_Not_EPPI\n", "29  2025-05-01            Web     5844  MFC_Not_EPPI\n", "30  2025-06-01        Prepaid      494  MFC_Not_EPPI\n", "31  2025-06-01  Pre-installed      423  MFC_Not_EPPI\n", "32  2025-06-01            Web     5478  MFC_Not_EPPI\n"]}], "source": ["print(eppi_df.tail())\n", "\n", "print(mfc_not_eppi_df.tail())"]}, {"cell_type": "code", "execution_count": 13, "id": "2600f0d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data combination summary:\n", "MFC Early (2301-2407): 57 records\n", "MFC Not EPPI (2408-2506): 33 records\n", "EPPI (2408-2506): 33 records\n", "Total combined: 123 records\n", "\n", "Combined monthly data:\n", "   date_period           type  cv_user\n", "0   2023-01-01  Pre-installed    20600\n", "1   2023-01-01        Prepaid    17795\n", "2   2023-01-01            Web    35639\n", "3   2023-02-01  Pre-installed    13306\n", "4   2023-02-01        Prepaid    15783\n", "5   2023-02-01            Web    27687\n", "6   2023-03-01  Pre-installed    12865\n", "7   2023-03-01        Prepaid    16295\n", "8   2023-03-01            <PERSON>    27280\n", "11  2023-04-01            Web    30641\n", "10  2023-04-01        Prepaid    18610\n", "9   2023-04-01  Pre-installed    17605\n", "12  2023-05-01  Pre-installed    21383\n", "13  2023-05-01        Prepaid    20201\n", "14  2023-05-01            <PERSON>    34086\n", "\n", "Date range: 2023-01-01 00:00:00 to 2025-06-01 00:00:00\n", "Channel types: ['Pre-installed' 'Prepaid' 'Web']\n", "\n", "Pivot data structure:\n", "type         Pre-installed  Prepaid    Web date_label\n", "date_period                                          \n", "2023-01-01           20600    17795  35639    2023-01\n", "2023-02-01           13306    15783  27687    2023-02\n", "2023-03-01           12865    16295  27280    2023-03\n", "2023-04-01           17605    18610  30641    2023-04\n", "2023-05-01           21383    20201  34086    2023-05\n", "\n", "Data shape: (30, 4)\n", "Date range: 2023-01-01 00:00:00 to 2025-06-01 00:00:00\n"]}], "source": ["\n", "# Convert date_period to datetime for all datasets\n", "eppi_df['date_period'] = pd.to_datetime(eppi_df['date_period'])\n", "mfc_early_df['date_period'] = pd.to_datetime(mfc_early_df['date_period'])\n", "mfc_not_eppi_df['date_period'] = pd.to_datetime(mfc_not_eppi_df['date_period'])\n", "\n", "# Combine datasets according to time periods:\n", "# 2301-2407: Use MFC early data only\n", "# 2408-2506: Use MFC Not EPPI + EPPI combined\n", "combined_df = pd.concat([\n", "    mfc_early_df,      # 2301-2407 period\n", "    mfc_not_eppi_df,   # 2408-2506 period (MFC Not EPPI)\n", "    eppi_df            # 2408-2506 period (EPPI)\n", "], ignore_index=True)\n", "\n", "print(\"Data combination summary:\")\n", "print(f\"MFC Early (2301-2407): {len(mfc_early_df)} records\")\n", "print(f\"MFC Not EPPI (2408-2506): {len(mfc_not_eppi_df)} records\")\n", "print(f\"EPPI (2408-2506): {len(eppi_df)} records\")\n", "print(f\"Total combined: {len(combined_df)} records\")\n", "\n", "# Group by date_period and type, sum the cv_user values\n", "monthly_data = combined_df.groupby(['date_period', 'type'])['cv_user'].sum().reset_index()\n", "\n", "# Sort by date\n", "monthly_data = monthly_data.sort_values('date_period')\n", "\n", "print(\"\\nCombined monthly data:\")\n", "print(monthly_data.head(15))\n", "print(f\"\\nDate range: {monthly_data['date_period'].min()} to {monthly_data['date_period'].max()}\")\n", "print(f\"Channel types: {monthly_data['type'].unique()}\")\n", "\n", "# Pivot the data to create columns for each channel type\n", "pivot_data = monthly_data.pivot(index='date_period', columns='type', values='cv_user').fillna(0)\n", "\n", "# Ensure all required columns exist\n", "required_columns = ['Pre-installed', 'Prepaid', 'Web']\n", "# required_columns = ['Online', 'Offline']\n", "\n", "for col in required_columns:\n", "    if col not in pivot_data.columns:\n", "        pivot_data[col] = 0\n", "\n", "# Reorder columns\n", "pivot_data = pivot_data[required_columns]\n", "\n", "# Create date labels for x-axis\n", "pivot_data['date_label'] = pivot_data.index.strftime('%Y-%m')\n", "\n", "print(\"\\nPivot data structure:\")\n", "print(pivot_data.head())\n", "print(f\"\\nData shape: {pivot_data.shape}\")\n", "print(f\"Date range: {pivot_data.index.min()} to {pivot_data.index.max()}\")"]}, {"cell_type": "code", "execution_count": 14, "id": "95175132", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date_period</th>\n", "      <th>type</th>\n", "      <th>cv_user</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-01</td>\n", "      <td>Pre-installed</td>\n", "      <td>20600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-01</td>\n", "      <td>Prepaid</td>\n", "      <td>17795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-01</td>\n", "      <td>Web</td>\n", "      <td>35639</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-02-01</td>\n", "      <td>Pre-installed</td>\n", "      <td>13306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-02-01</td>\n", "      <td>Prepaid</td>\n", "      <td>15783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>2025-05-01</td>\n", "      <td>Pre-installed</td>\n", "      <td>8752</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>2025-05-01</td>\n", "      <td>Web</td>\n", "      <td>24296</td>\n", "    </tr>\n", "    <tr>\n", "      <th>88</th>\n", "      <td>2025-06-01</td>\n", "      <td>Prepaid</td>\n", "      <td>7364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>2025-06-01</td>\n", "      <td>Pre-installed</td>\n", "      <td>6389</td>\n", "    </tr>\n", "    <tr>\n", "      <th>89</th>\n", "      <td>2025-06-01</td>\n", "      <td>Web</td>\n", "      <td>21775</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>90 rows × 3 columns</p>\n", "</div>"], "text/plain": ["   date_period           type  cv_user\n", "0   2023-01-01  Pre-installed    20600\n", "1   2023-01-01        Prepaid    17795\n", "2   2023-01-01            Web    35639\n", "3   2023-02-01  Pre-installed    13306\n", "4   2023-02-01        Prepaid    15783\n", "..         ...            ...      ...\n", "84  2025-05-01  Pre-installed     8752\n", "86  2025-05-01            Web    24296\n", "88  2025-06-01        Prepaid     7364\n", "87  2025-06-01  Pre-installed     6389\n", "89  2025-06-01            Web    21775\n", "\n", "[90 rows x 3 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["monthly_data"]}, {"cell_type": "code", "execution_count": 15, "id": "88607289", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Monthly data by source:\n", "source date_period   EPPI  MFC_Early  MFC_Not_EPPI  total\n", "0       2023-01-01      0      74034             0  74034\n", "1       2023-02-01      0      56776             0  56776\n", "2       2023-03-01      0      56440             0  56440\n", "3       2023-04-01      0      66856             0  66856\n", "4       2023-05-01      0      75670             0  75670\n", "5       2023-06-01      0      82523             0  82523\n", "6       2023-07-01      0      89601             0  89601\n", "7       2023-08-01      0      81440             0  81440\n", "8       2023-09-01      0      70714             0  70714\n", "9       2023-10-01      0      71601             0  71601\n", "10      2023-11-01      0      65755             0  65755\n", "11      2023-12-01      0      67117             0  67117\n", "12      2024-01-01      0      68982             0  68982\n", "13      2024-02-01      0      58414             0  58414\n", "14      2024-03-01      0      64636             0  64636\n", "15      2024-04-01      0      61722             0  61722\n", "16      2024-05-01      0      62080             0  62080\n", "17      2024-06-01      0      65685             0  65685\n", "18      2024-07-01      0      66574             0  66574\n", "19      2024-08-01  12713          0         13523  26236\n", "\n", "Sources available: ['EPPI', 'MFC_Early', 'MFC_Not_EPPI']\n", "Total records: 30\n"]}], "source": ["# Group by source to understand data composition\n", "monthly_source = combined_df.groupby(['date_period', 'source'])['cv_user'].sum().unstack(fill_value=0).reset_index()\n", "\n", "# Calculate total based on available sources\n", "# Fill missing columns with 0 if they don't exist\n", "for col in ['EPPI', 'MFC_Early', 'MFC_Not_EPPI']:\n", "    if col not in monthly_source.columns:\n", "        monthly_source[col] = 0\n", "\n", "# Total = all sources combined\n", "monthly_source['total'] = monthly_source['EPPI'] + monthly_source['MFC_Early'] + monthly_source['MFC_Not_EPPI']\n", "\n", "print(\"Monthly data by source:\")\n", "print(monthly_source.head(20))\n", "print(f\"\\nSources available: {[col for col in monthly_source.columns if col not in ['date_period', 'total']]}\")\n", "print(f\"Total records: {len(monthly_source)}\")"]}, {"cell_type": "code", "execution_count": 16, "id": "828ccadd", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Summary Statistics:\n", "Total new paid users across all months: 1,817,888\n", "\n", "Monthly totals:\n", "2023-01: 74,034\n", "2023-02: 56,776\n", "2023-03: 56,440\n", "2023-04: 66,856\n", "2023-05: 75,670\n", "2023-06: 82,523\n", "2023-07: 89,601\n", "2023-08: 81,440\n", "2023-09: 70,714\n", "2023-10: 71,601\n", "2023-11: 65,755\n", "2023-12: 67,117\n", "2024-01: 68,982\n", "2024-02: 58,414\n", "2024-03: 64,636\n", "2024-04: 61,722\n", "2024-05: 62,080\n", "2024-06: 65,685\n", "2024-07: 66,574\n", "2024-08: 26,236\n", "2024-09: 52,928\n", "2024-10: 61,826\n", "2024-11: 59,942\n", "2024-12: 54,801\n", "2025-01: 51,993\n", "2025-02: 37,987\n", "2025-03: 45,565\n", "2025-04: 43,091\n", "2025-05: 41,371\n", "2025-06: 35,528\n", "\n", "Channel breakdown (total):\n", "Pre-installed: 406,323 (22.4%)\n", "Prepaid: 414,869 (22.8%)\n", "Web: 996,696 (54.8%)\n", "\n", "Monthly Growth Analysis:\n", "==================================================\n", "2023-01: Baseline month\n", "2023-02: -23.3% ↓\n", "2023-03: -0.6% ↓\n", "2023-04: +18.5% ↑\n", "2023-05: +13.2% ↑\n", "2023-06: +9.1% ↑\n", "2023-07: +8.6% ↑\n", "2023-08: -9.1% ↓\n", "2023-09: -13.2% ↓\n", "2023-10: +1.3% ↑\n", "2023-11: -8.2% ↓\n", "2023-12: +2.1% ↑\n", "2024-01: +2.8% ↑\n", "2024-02: -15.3% ↓\n", "2024-03: +10.7% ↑\n", "2024-04: -4.5% ↓\n", "2024-05: +0.6% ↑\n", "2024-06: +5.8% ↑\n", "2024-07: +1.4% ↑\n", "2024-08: -60.6% ↓\n", "2024-09: +101.7% ↑\n", "2024-10: +16.8% ↑\n", "2024-11: -3.0% ↓\n", "2024-12: -8.6% ↓\n", "2025-01: -5.1% ↓\n", "2025-02: -26.9% ↓\n", "2025-03: +19.9% ↑\n", "2025-04: -5.4% ↓\n", "2025-05: -4.0% ↓\n", "2025-06: -14.1% ↓\n", "\n", "Channel Performance Analysis:\n", "==================================================\n", "\n", "Pre-installed:\n", "  Average monthly users: 13,544\n", "  Peak month: 2023-07 (23,437 users)\n", "  Lowest month: 2024-08 (3,419 users)\n", "  Overall growth: -69.0%\n", "\n", "Prepaid:\n", "  Average monthly users: 13,829\n", "  Peak month: 2023-05 (20,201 users)\n", "  Lowest month: 2024-08 (5,296 users)\n", "  Overall growth: -58.6%\n", "\n", "Web:\n", "  Average monthly users: 33,223\n", "  Peak month: 2023-07 (46,689 users)\n", "  Lowest month: 2024-08 (17,521 users)\n", "  Overall growth: -38.9%\n", "\n", "Seasonal Pattern Analysis:\n", "==================================================\n", "Top 3 performing months (by average):\n", "  July: 78,088 users on average\n", "  October: 66,714 users on average\n", "  January: 65,003 users on average\n"]}], "source": ["# Create the stacked bar chart\n", "fig, ax = plt.subplots(figsize=(16, 10))\n", "\n", "# Define colors similar to the reference chart\n", "colors = {\n", "    'Pre-installed': '#4472C4',  # Blue\n", "    'Prepaid': '#70AD47',        # Green  \n", "    'Web': '#FFC000'             # Yellow\n", "}\n", "\n", "# Create the stacked bar chart\n", "width = 0.8\n", "x_pos = np.arange(len(pivot_data))\n", "\n", "# Plot each channel type\n", "bottom = np.zeros(len(pivot_data))\n", "bars = []\n", "\n", "for channel in required_columns:\n", "    values = pivot_data[channel].values\n", "    bar = ax.bar(x_pos, values, width, bottom=bottom, \n", "                label=channel, color=colors[channel], alpha=0.8)\n", "    bars.append(bar)\n", "    bottom += values\n", "\n", "# Customize the chart\n", "ax.set_xlabel('Month', fontsize=14, fontweight='bold')\n", "ax.set_ylabel('New Paid Users', fontsize=14, fontweight='bold')\n", "ax.set_title('New Paid User Monthly Trends by Channel', fontsize=16, fontweight='bold', pad=20)\n", "\n", "# Set x-axis labels\n", "ax.set_xticks(x_pos)\n", "ax.set_xticklabels(pivot_data['date_label'], rotation=45, ha='right')\n", "\n", "# Add legend\n", "ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True)\n", "\n", "# Add grid\n", "ax.grid(True, alpha=0.3, axis='y')\n", "ax.set_axisbelow(True)\n", "\n", "# Format y-axis to show values with commas\n", "ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))\n", "\n", "# Adjust layout to prevent label cutoff\n", "plt.tight_layout()\n", "\n", "# Display the chart\n", "plt.show()\n", "\n", "# Print summary statistics\n", "print(\"\\nSummary Statistics:\")\n", "print(f\"Total new paid users across all months: {pivot_data[required_columns].sum().sum():,.0f}\")\n", "print(\"\\nMonthly totals:\")\n", "monthly_totals = pivot_data[required_columns].sum(axis=1)\n", "for date, total in monthly_totals.items():\n", "    print(f\"{date.strftime('%Y-%m')}: {total:,.0f}\")\n", "    \n", "print(f\"\\nChannel breakdown (total):\")\n", "for channel in required_columns:\n", "    total = pivot_data[channel].sum()\n", "    percentage = (total / pivot_data[required_columns].sum().sum()) * 100\n", "    print(f\"{channel}: {total:,.0f} ({percentage:.1f}%)\")\n", "    \n", "\n", "\n", "# Additional analysis: Monthly growth rates and trends\n", "monthly_totals = pivot_data[required_columns].sum(axis=1)\n", "monthly_growth = monthly_totals.pct_change() * 100\n", "\n", "print(\"\\nMonthly Growth Analysis:\")\n", "print(\"=\" * 50)\n", "for i, (date, growth) in enumerate(monthly_growth.items()):\n", "    if i == 0:\n", "        print(f\"{date.strftime('%Y-%m')}: Baseline month\")\n", "    else:\n", "        trend = \"↑\" if growth > 0 else \"↓\" if growth < 0 else \"→\"\n", "        print(f\"{date.strftime('%Y-%m')}: {growth:+.1f}% {trend}\")\n", "\n", "# Channel performance analysis\n", "print(\"\\nChannel Performance Analysis:\")\n", "print(\"=\" * 50)\n", "for channel in required_columns:\n", "    channel_data = pivot_data[channel]\n", "    max_month = channel_data.idxmax()\n", "    min_month = channel_data.idxmin()\n", "    avg_users = channel_data.mean()\n", "    \n", "    print(f\"\\n{channel}:\")\n", "    print(f\"  Average monthly users: {avg_users:,.0f}\")\n", "    print(f\"  Peak month: {max_month.strftime('%Y-%m')} ({channel_data.max():,.0f} users)\")\n", "    print(f\"  Lowest month: {min_month.strftime('%Y-%m')} ({channel_data.min():,.0f} users)\")\n", "    \n", "    # Calculate channel growth from first to last month\n", "    first_month = channel_data.iloc[0]\n", "    last_month = channel_data.iloc[-1]\n", "    if first_month > 0:\n", "        channel_growth = ((last_month - first_month) / first_month) * 100\n", "        print(f\"  Overall growth: {channel_growth:+.1f}%\")\n", "\n", "# Identify seasonal patterns\n", "print(\"\\nSeasonal Pattern Analysis:\")\n", "print(\"=\" * 50)\n", "pivot_data['month_num'] = pivot_data.index.month\n", "seasonal_avg = pivot_data.groupby('month_num')[required_columns].mean()\n", "\n", "best_months = seasonal_avg.sum(axis=1).sort_values(ascending=False).head(3)\n", "print(\"Top 3 performing months (by average):\")\n", "for month_num, avg_users in best_months.items():\n", "    month_name = pd.Timestamp(2024, month_num, 1).strftime('%B')\n", "    print(f\"  {month_name}: {avg_users:,.0f} users on average\")"]}, {"cell_type": "markdown", "id": "a16f48db", "metadata": {}, "source": ["## Conversion Rate by Month (EPPI+MFC)"]}, {"cell_type": "code", "execution_count": 10, "id": "03a26d86", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MFC Early conversion data (2301-2407):\n", "  date_period           type  Activation     source\n", "0  2023-01-01            Web       77376  MFC_Early\n", "1  2023-01-01        Prepaid       30634  MFC_Early\n", "2  2023-01-01  Pre-installed      172901  MFC_Early\n", "3  2023-02-01  Pre-installed      113754  MFC_Early\n", "4  2023-02-01            Web       60030  MFC_Early\n", "\n", "EPPI conversion data (2408-2506):\n", "  date_period           type  Activation source\n", "0  2024-08-01        Prepaid       33756   EPPI\n", "1  2024-08-01            Web       42233   EPPI\n", "2  2024-08-01  Pre-installed       22297   EPPI\n", "3  2024-09-01        Prepaid       32125   EPPI\n", "4  2024-09-01            Web       58371   EPPI\n", "\n", "MFC Not EPPI conversion data (2408-2506):\n", "  date_period           type  Activation        source\n", "0  2024-08-01        Prepaid       15295  MFC_Not_EPPI\n", "1  2024-08-01  Pre-installed      155276  MFC_Not_EPPI\n", "2  2024-08-01            Web       68981  MFC_Not_EPPI\n", "3  2024-09-01  Pre-installed      137636  MFC_Not_EPPI\n", "4  2024-09-01            Web       24651  MFC_Not_EPPI\n"]}], "source": ["from datetime import datetime\n", "\n", "# Load the three conversion CSV files\n", "conv_mfc_early_df = pd.read_csv('Conversion by Channel 2301-2407 2025-07-17 20_26_30.csv')\n", "conv_eppi_df = pd.read_csv('Conversion by Channel 2408-2506 2025-07-17 20_30_12.csv')\n", "conv_mfc_not_eppi_df = pd.read_csv('Conversion by Channel 2408-2506 NOT EPPI 2025-07-17 20_28_29.csv')\n", "\n", "# Add source identifier to each dataset\n", "conv_mfc_early_df['source'] = 'MFC_Early'\n", "conv_eppi_df['source'] = 'EPPI'\n", "conv_mfc_not_eppi_df['source'] = 'MFC_Not_EPPI'\n", "\n", "print(\"MFC Early conversion data (2301-2407):\")\n", "print(conv_mfc_early_df.head())\n", "\n", "print(\"\\nEPPI conversion data (2408-2506):\")\n", "print(conv_eppi_df.head())\n", "\n", "print(\"\\nMFC Not EPPI conversion data (2408-2506):\")\n", "print(conv_mfc_not_eppi_df.head())"]}, {"cell_type": "code", "execution_count": 18, "id": "8e881d72", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data processed:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date_period</th>\n", "      <th>online_users</th>\n", "      <th>offline_users</th>\n", "      <th>Activation</th>\n", "      <th>source</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-01</td>\n", "      <td>32057.018287</td>\n", "      <td>17601.068387</td>\n", "      <td>253169</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-02-01</td>\n", "      <td>24321.031808</td>\n", "      <td>12730.003846</td>\n", "      <td>178747</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-03-01</td>\n", "      <td>22546.925700</td>\n", "      <td>12162.000552</td>\n", "      <td>173238</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-04-01</td>\n", "      <td>25030.937002</td>\n", "      <td>13251.070748</td>\n", "      <td>217666</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-05-01</td>\n", "      <td>26480.082258</td>\n", "      <td>15787.099888</td>\n", "      <td>260582</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-06-01</td>\n", "      <td>28779.961470</td>\n", "      <td>16046.847635</td>\n", "      <td>305695</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-07-01</td>\n", "      <td>32190.132370</td>\n", "      <td>17040.047990</td>\n", "      <td>303755</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-08-01</td>\n", "      <td>27032.980400</td>\n", "      <td>15991.922974</td>\n", "      <td>277546</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-09-01</td>\n", "      <td>21436.044262</td>\n", "      <td>14078.055231</td>\n", "      <td>241729</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-10-01</td>\n", "      <td>22027.015498</td>\n", "      <td>13326.045716</td>\n", "      <td>252034</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-11-01</td>\n", "      <td>19110.088965</td>\n", "      <td>12718.890371</td>\n", "      <td>227623</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2023-12-01</td>\n", "      <td>20297.011680</td>\n", "      <td>12325.010170</td>\n", "      <td>232210</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2024-01-01</td>\n", "      <td>20829.971792</td>\n", "      <td>12391.011772</td>\n", "      <td>253727</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2024-02-01</td>\n", "      <td>17199.023530</td>\n", "      <td>10516.986272</td>\n", "      <td>222742</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2024-03-01</td>\n", "      <td>18613.024122</td>\n", "      <td>10572.100539</td>\n", "      <td>247863</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2024-04-01</td>\n", "      <td>16890.114731</td>\n", "      <td>9303.048498</td>\n", "      <td>249961</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2024-05-01</td>\n", "      <td>16252.948498</td>\n", "      <td>8607.062719</td>\n", "      <td>251617</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2024-06-01</td>\n", "      <td>16818.924032</td>\n", "      <td>8359.966864</td>\n", "      <td>294614</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2024-07-01</td>\n", "      <td>12646.027740</td>\n", "      <td>8316.032280</td>\n", "      <td>277386</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2024-08-01</td>\n", "      <td>7936.101240</td>\n", "      <td>5473.115420</td>\n", "      <td>239660</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>2024-09-01</td>\n", "      <td>6337.934010</td>\n", "      <td>2165.081880</td>\n", "      <td>174210</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>2024-10-01</td>\n", "      <td>5680.030161</td>\n", "      <td>1787.943235</td>\n", "      <td>133081</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>2024-11-01</td>\n", "      <td>6228.047004</td>\n", "      <td>1977.027121</td>\n", "      <td>211063</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>2024-12-01</td>\n", "      <td>7745.006917</td>\n", "      <td>2213.932015</td>\n", "      <td>159793</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>2025-01-01</td>\n", "      <td>7962.011665</td>\n", "      <td>1975.036094</td>\n", "      <td>181429</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>2025-02-01</td>\n", "      <td>5060.048968</td>\n", "      <td>1351.050160</td>\n", "      <td>143576</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>2025-03-01</td>\n", "      <td>6011.017856</td>\n", "      <td>1521.968526</td>\n", "      <td>119342</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>2025-04-01</td>\n", "      <td>5910.949450</td>\n", "      <td>1290.012500</td>\n", "      <td>112175</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>2025-05-01</td>\n", "      <td>5548.053105</td>\n", "      <td>1262.981349</td>\n", "      <td>110817</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>2025-06-01</td>\n", "      <td>5044.966784</td>\n", "      <td>1207.957933</td>\n", "      <td>115583</td>\n", "      <td>MFC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>2024-08-01</td>\n", "      <td>22871.040066</td>\n", "      <td>20165.011992</td>\n", "      <td>98226</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>2024-09-01</td>\n", "      <td>20918.975546</td>\n", "      <td>24344.043640</td>\n", "      <td>117866</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>2024-10-01</td>\n", "      <td>22217.941590</td>\n", "      <td>31938.038730</td>\n", "      <td>122330</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2024-11-01</td>\n", "      <td>24094.938627</td>\n", "      <td>27513.062208</td>\n", "      <td>144891</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2024-12-01</td>\n", "      <td>20404.005774</td>\n", "      <td>24463.028682</td>\n", "      <td>120189</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2025-01-01</td>\n", "      <td>19263.991074</td>\n", "      <td>22922.967150</td>\n", "      <td>131798</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2025-02-01</td>\n", "      <td>13922.033355</td>\n", "      <td>17641.048110</td>\n", "      <td>103395</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2025-03-01</td>\n", "      <td>17911.961520</td>\n", "      <td>20101.992858</td>\n", "      <td>129534</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2025-04-01</td>\n", "      <td>18055.082500</td>\n", "      <td>17789.082500</td>\n", "      <td>166250</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>2025-05-01</td>\n", "      <td>17645.948855</td>\n", "      <td>16797.068105</td>\n", "      <td>182555</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>2025-06-01</td>\n", "      <td>14521.054296</td>\n", "      <td>14436.077040</td>\n", "      <td>167608</td>\n", "      <td>EPPI</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   date_period  online_users  offline_users  Activation source\n", "0   2023-01-01  32057.018287   17601.068387      253169    MFC\n", "1   2023-02-01  24321.031808   12730.003846      178747    MFC\n", "2   2023-03-01  22546.925700   12162.000552      173238    MFC\n", "3   2023-04-01  25030.937002   13251.070748      217666    MFC\n", "4   2023-05-01  26480.082258   15787.099888      260582    MFC\n", "5   2023-06-01  28779.961470   16046.847635      305695    MFC\n", "6   2023-07-01  32190.132370   17040.047990      303755    MFC\n", "7   2023-08-01  27032.980400   15991.922974      277546    MFC\n", "8   2023-09-01  21436.044262   14078.055231      241729    MFC\n", "9   2023-10-01  22027.015498   13326.045716      252034    MFC\n", "10  2023-11-01  19110.088965   12718.890371      227623    MFC\n", "11  2023-12-01  20297.011680   12325.010170      232210    MFC\n", "12  2024-01-01  20829.971792   12391.011772      253727    MFC\n", "13  2024-02-01  17199.023530   10516.986272      222742    MFC\n", "14  2024-03-01  18613.024122   10572.100539      247863    MFC\n", "15  2024-04-01  16890.114731    9303.048498      249961    MFC\n", "16  2024-05-01  16252.948498    8607.062719      251617    MFC\n", "17  2024-06-01  16818.924032    8359.966864      294614    MFC\n", "18  2024-07-01  12646.027740    8316.032280      277386    MFC\n", "19  2024-08-01   7936.101240    5473.115420      239660    MFC\n", "20  2024-09-01   6337.934010    2165.081880      174210    MFC\n", "21  2024-10-01   5680.030161    1787.943235      133081    MFC\n", "22  2024-11-01   6228.047004    1977.027121      211063    MFC\n", "23  2024-12-01   7745.006917    2213.932015      159793    MFC\n", "24  2025-01-01   7962.011665    1975.036094      181429    MFC\n", "25  2025-02-01   5060.048968    1351.050160      143576    MFC\n", "26  2025-03-01   6011.017856    1521.968526      119342    MFC\n", "27  2025-04-01   5910.949450    1290.012500      112175    MFC\n", "28  2025-05-01   5548.053105    1262.981349      110817    MFC\n", "29  2025-06-01   5044.966784    1207.957933      115583    MFC\n", "30  2024-08-01  22871.040066   20165.011992       98226   EPPI\n", "31  2024-09-01  20918.975546   24344.043640      117866   EPPI\n", "32  2024-10-01  22217.941590   31938.038730      122330   EPPI\n", "33  2024-11-01  24094.938627   27513.062208      144891   EPPI\n", "34  2024-12-01  20404.005774   24463.028682      120189   EPPI\n", "35  2025-01-01  19263.991074   22922.967150      131798   EPPI\n", "36  2025-02-01  13922.033355   17641.048110      103395   EPPI\n", "37  2025-03-01  17911.961520   20101.992858      129534   EPPI\n", "38  2025-04-01  18055.082500   17789.082500      166250   EPPI\n", "39  2025-05-01  17645.948855   16797.068105      182555   EPPI\n", "40  2025-06-01  14521.054296   14436.077040      167608   EPPI"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["def process_conversion_data(df, source_name):\n", "    \"\"\"Convert percentages to actual numbers and add source identifier\"\"\"\n", "    df = df.copy()\n", "    df['date_period'] = pd.to_datetime(df['date_period'])\n", "    \n", "    # Convert percentages to actual numbers\n", "    df['online_users'] = df['Online%'] * df['Activation']\n", "    df['offline_users'] = df['Offline%'] * df['Activation']\n", "    \n", "    # Add source identifier\n", "    df['source'] = source_name\n", "    \n", "    # Keep only needed columns\n", "    result = df[['date_period', 'online_users', 'offline_users', 'Activation', 'source']]\n", "    \n", "    return result\n", "\n", "# Process both datasets\n", "mfc_processed = process_conversion_data(mfc_conversion_df, 'MFC')\n", "eppi_processed = process_conversion_data(eppi_conversion_df, 'EPPI')\n", "\n", "# Combine the datasets\n", "combined_df = pd.concat([mfc_processed, eppi_processed], ignore_index=True)\n", "\n", "print(\"Data processed:\")\n", "combined_df"]}, {"cell_type": "code", "execution_count": null, "id": "1a855145", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Monthly aggregated data:\n", "  date_period  online_users  offline_users  Activation  total  online_rate  \\\n", "0  2023-01-01  32057.018287   17601.068387      253169  49778     0.126623   \n", "1  2023-02-01  24321.031808   12730.003846      178747  37127     0.136064   \n", "2  2023-03-01  22546.925700   12162.000552      173238  34767     0.130150   \n", "3  2023-04-01  25030.937002   13251.070748      217666  38354     0.114997   \n", "4  2023-05-01  26480.082258   15787.099888      260582  42350     0.101619   \n", "\n", "   offline_rate  total_rate  \n", "0      0.069523    0.196620  \n", "1      0.071218    0.207707  \n", "2      0.070204    0.200689  \n", "3      0.060878    0.176206  \n", "4      0.060584    0.162521  \n"]}], "source": ["# Aggregate data by month\n", "monthly_data = combined_df.groupby('date_period').agg({\n", "    'online_users': 'sum',\n", "    'offline_users': 'sum', \n", "    'Activation': 'sum'\n", "}).reset_index()\n", "\n", "# 拼接 monthly_source 的 total 列\n", "monthly_data = monthly_data.merge(\n", "    monthly_source[['date_period', 'total']],  # 其他的total\n", "    on='date_period',\n", "    how='left'\n", ")\n", "\n", "# Calculate combined conversion rates\n", "monthly_data['online_rate'] = monthly_data['online_users'] / monthly_data['Activation']\n", "monthly_data['offline_rate'] = monthly_data['offline_users'] / monthly_data['Activation']\n", "# monthly_data['total_rate'] = monthly_data['total']  / monthly_data['Activation']\n", "monthly_data['total_rate'] = (monthly_data['online_users'] + monthly_data['offline_users']) / monthly_data['Activation']\n", "\n", "# Sort by date\n", "monthly_data = monthly_data.sort_values('date_period')\n", "\n", "print(\"Monthly aggregated data:\")\n", "print(monthly_data.head())"]}, {"cell_type": "code", "execution_count": 24, "id": "0509b419", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABW0AAASmCAYAAABVz4lwAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3Xd4U2Ubx/HvyeoeFFqgUMoqQzYCMmQvwYGADEFAQVRAwYFbGSru/YpbARUFJygoU5ZsRIZQ9ii7zNKVNuN5/6g9NN3QtAnl/lxXL3P275ykwd55ch9NKaUQQgghhBBCCCGEEEII4RUMng4ghBBCCCGEEEIIIYQQ4hIp2gohhBBCCCGEEEIIIYQXkaKtEEIIIYQQQgghhBBCeBEp2gohhBBCCCGEEEIIIYQXkaKtEEIIIYQQQgghhBBCeBEp2gohhBBCCCGEEEIIIYQXkaKtEEIIIYQQQgghhBBCeBEp2gohhBBCCCGEEEIIIYQXkaKtEEIIIYQQQgghhBBCeBEp2gohhBDXiOXLl6Npmv5z6NChQm03ffp0l+3E1etKXwPCe7Rq1QpN0/Dx8eHYsWOejiNEscn6XjV9+vRCbXPo0CGX7ZYvX16sGa9UXue2ceNGff7AgQM9F1AIIYRXkKKtEEII8Z/sBS1N07jttttyXXfhwoU51r377rtLNnAWpa0Yl5CQwNtvv0337t2JjIzEx8cHf39/YmJiGDRoED/88AM2m83TMUUxyP4hQeaPyWSibNmytGzZkpdeeomEhAS3HfNKikOe8Msvv7Bu3ToABg0aRKVKlfRluV23sWPH5rqfTz75JMe6kyZNclknt+cg+0/VqlVz3b9Sit9//50hQ4ZQq1YtgoODMZvNlC9fns6dO/Paa69x4sQJt1yTosjrtZbftcleFMz64+/vT82aNRk+fDhbt27Ncby8tvP19SU6OpqBAweycuXKHNtVrVpVX7dDhw6XfZ5Wq5XPPvuM2267jaioKPz8/PD19aVq1ar06dOHadOmkZKSctn7Fe7XvHlz2rdvD8D333/PP//84+FEQgghPMnk6QBCCCGEN5s/fz4HDhygevXqLvPfe+89DyUq/X7++Wfuvfdezp8/n2PZvn372LdvH9999x3Lli27ogLGtaxGjRq88cYb+nRYWJgH01weh8PBuXPnWL9+PevXr2fmzJls2LCBoKAgT0crMRMnTtQfjxs3rsD1p0+fzpQpU3Jco/fff9/t2TIdOXKEQYMG8ddff+VYFh8fz59//smff/5JbGysVxfIr0Rqair79+9n//79fP3113zxxRcMHTq0wO3S0tKIi4sjLi6O2bNn89JLL/Hss8+6JdPKlSsZPHgwR48ezbHs8OHDHD58mF9++cXjHzy6S1hYmMt7XI0aNTyY5sqMGzeOFStWoJRi4sSJ/Prrr56OJIQQwkOkaCuEEELkw+l08sEHH/D222/r8/bs2cOCBQs8mKr0mj17NnfeeSdKKX1ely5daNWqFT4+Phw6dIglS5Zc9SOJ3eHixYsEBwdf1jZRUVGMHz++mBIVjwceeIAaNWpw9uxZZs2apT/3u3btYtq0aXmOJi1t1qxZw/bt2wGoXbs2jRs3LnCbxMTEHNdoyZIl7Ny587KO3axZMwYMGJBjfkhIiMv0qVOnaN++PQcPHtTnVatWjdtuu43y5ctz/vx51q1bl2tB93IsX76cjh07Ari8VxRV5mstu9atW+e5TdeuXenWrRsOh4OtW7cye/ZsnE4ndrudBx54gK5du1KxYsUc22VeU6fTyd69e/n6669JS0sD4Pnnn6dnz540adKkSOezatUqunXrpu8XoGXLlnTs2JHAwECOHz+uF9BLi+Dg4KvuPS67nj17EhwczMWLF/n99985evQolStX9nQsIYQQnqCEEEIIoZRSatmyZQrQfwwGgwJUSEiISkpK0td78MEH9XWMRqP+eNiwYTn2efToUTV+/HhVv359FRAQoHx8fFR0dLQaPHiwWr9+fY71J06cqO8vOjpaXbhwQY0fP15VqVJFmc1mVa1aNTVlyhTldDr1bbJmzu0nM1f28ztw4ID67LPPVKNGjZSPj48KDw9XI0aMUOfOnXPJNG3aNJftlFLK4XCoatWq6fOefvrpHOcyfvx4fXndunULvP7x8fEqODhY38bf318tWrQox3pOp1P98MMP6t9//3WZf+7cOTV58mR1/fXXq+DgYGU2m1VkZKTq3bt3rvvJfl5Wq1W99NJLKiYmRlksFlWpUiX12GOPKavVqm9z11136eu3b98+xz5///13l9dPXFycvsxqtar//e9/qm3btqpMmTLKbDarChUqqDvuuEOtWbOmwHzJycnqmWeeUdWqVVMmk0mNGzdOKaXU6dOn1WOPPaauu+465e/vr8xmsypfvrxq3ry5GjNmjFq7dq2+z+yvgYMHD7oc0263qy+++EJ16tRJlS1bVplMJhUWFqY6dOigPv30U2Wz2VzWP3jwoMv+li1bpr777jvVokUL5efnp0JDQ9Udd9zhch0Kkv28ly1bpi+LjY11WXb//fe7bHvgwAE1btw4deONN6rKlSsrf39/ZbFYVGRkpLrlllvUr7/+6rJ++/bt8/3diY6Odln/5MmT6umnn1aNGjVSgYGBysfHR9WoUUONHj1aHT58OMe5JCUlqcmTJ6smTZqowMBAZTKZVHh4uGrUqJG699571R9//FHo63LvvffquZ555pkCr1vm+1dMTIzL+8Utt9yS470LUBMnTnTZX27vIQUZOHCgy3ajRo3K8ZpRSqk9e/aob775ptDnnl3W13FR5Pday0v213z26/bss8+6LP/iiy/0Zfld088++8xl+fPPP68vi46Ozvd9JzdWq1VVrVrV5fXw1Vdf5brukiVL1MqVK13mpaSkqLffflu1bt1ahYaGKrPZrCIiIlSPHj3U7Nmzc+wj+3vLrl271IQJE1SVKlWUn5+fat68uf56j4+PV8OHD1flypVTvr6+qk2bNjmOn/16TZs2TS1evFi1bdtWBQQEqNDQUNW3b1+1d+9el21ye0/KdCX/vmZyOBzqq6++Ul27dlXh4eHKbDarcuXKqZ49e6r58+fnel1tNpt65ZVXVM2aNZXFYlHVq1dXL774okpPT89xbtkNGjRIX/7SSy/lun8hhBClnxRthRBCiP9k/6Pz9ttv1x9PnTpVKaVUQkKCCgoKUoBq0qSJyx/T2f8IX7FihSpTpkyeBSGDwaDeeustl22y/lFZtmxZVbdu3Vy3zfoH/ZUWbbt3757r+u3atXPJlFvRViml3njjDX1eZGSkstvtLttlvTavv/56gdf/1VdfdTlO9muTn507d6rKlSvnex0yi5x5ndeNN96Y63ZDhgzRt1m6dKnL83f06FGXfQ4ZMkRf3q1bN31+fHy8aty4cb6vhXfffTfffG3bts1xPqmpqap27dr5nveTTz6p7zO/om1SUpJq165dvvu68cYbVWJior5N9gJJXtcwJiZGpaamFuq5zK+QdvHiRZdlzz77rMu2v/32W4G/D5MnT9bXv5yi7Zo1a1S5cuXyXDckJCRH4alDhw757n/AgAGFuiZKKVWlShV9u3nz5hV43bK+f2UWlfbt26cXc3v37u2yflGLtsePH1eapunbNG7cWDkcjkKf3+Xw5qLtvHnzXJZPmTJFX5bfNf33339dlo8cOVJfdiVF21mzZrns76GHHirUdkopdeLECVWvXr18X7t9+/Z1Kchnf2+5/vrrc32fmzVrlssHfpk/Pj4+aufOnS45si7v0aOHy+sr86ds2bJq9+7d+jaFLdoW9t9XpTIK2F26dMn3ejz66KM5rmP2DzEyf26++WaX6dyKtv/73/8u+zkXQghR+kh7BCGEECIPgwcP5q+//uLMmTN88MEHjB49mmnTppGYmAjA2LFjc9y8J9OFCxfo06eP3pfVz8+Pe+65h+DgYL777jsOHz6M0+lk/PjxXH/99fqNR7I6e/Ys58+fZ+jQoURGRvL5559z5swZIKOn7nPPPYfFYuGNN95g//79fPzxx/q2zzzzDGXKlAGgfv36uWZcuHAhnTt3pnXr1syZM0f/6vXKlStZt24dLVu2zPf6jBgxgokTJ5KSksLx48eZP3++fuO2DRs2cPjwYQBMJhNDhgzJd18AS5cu1R9fTn9Fu91O79699Z6NRqORIUOGULlyZebMmcO///4LZFyzpk2b5tlj8q+//qJ3795cd911zJw5U/8a/syZM3n11VeJjIykY8eOVK1alUOHDuF0Opk1axaPPfYYkNHPcs6cOfr+7rnnHv3xkCFD2LJlCwBBQUEMGjSIypUrs3r1ahYsWIDT6eSRRx6hWbNmtGnTJtd8q1at4oYbbqBr164kJydTpUoVli1bxu7duwHw9fVlxIgRVKpUiZMnT7Jv3z5WrFhRqGsIGa/nrDdB6tatG61atWLdunUsXLhQv0Zjx47lyy+/zPMaNm/enO7du7Ns2TJWr14NwN69e5kzZ06R7oZ+7tw5XnvtNX1a0zT69evnso7JZKJx48Y0a9aM8PBwgoODSU5OZvXq1SxbtgyAF198Ub9Oo0aN4pZbbuHxxx/X9zFgwACaNWsGXPr6/8WLF7n99tv137/o6GgGDBiAn58fP/74Izt27CAhIYG+ffuyd+9eQkJCiI2N1e9cbzAYGDp0KLVq1eLMmTMcPHjwsu5qn9nvNFNmvvyMGjWK+fPnY7PZeP/99+nZsycffPABTqcTyHi+f/nll0Idf8eOHbz55ps55rdu3VpvHbBs2TKXVgXDhg3DYLi67nk8e/ZsNm3alGP+fffdV+hWJGvXrnWZrlChQrFul5es76cAw4cPL/S2gwcPZseOHfr0HXfcwXXXXcfixYv1nD/99BMvv/wyEyZMyHUff//9NwMGDKB69ep88MEHJCYm4nQ69feAIUOGUK5cOf73v/9ht9tJS0vjvffec/l3LKs//viD66+/np49e/Lvv//qr92zZ8/ywAMP8Oeffxb6/DK3K8y/rwCPPPIIS5YsAcBisTBw4EBiYmLYvn07P/zwA0op3n77ba6//noGDRoEwI8//sisWbP049WsWZP+/ftz7Ngxvv766wLzNW/eXH+8fv160tPT9TxCCCGuIZ6uGgshhBDeIvtIod9++00988wz+vSCBQtUzZo1FaDCw8OV1WrNc6TtO++847Kv33//XV926tQpFRgYqC/r1auXvizrSCDAZfTlnDlzXJZt27Ytz+zZv/ae2zq9e/fWvwZ69uxZl69Lv//++/p2eY20VUqpkSNH6vNvvfVWff5jjz2W6/z8XHfddfo25cuXL9Q2Sin1yy+/uOT78MMP9WUpKSkuz1GjRo3yPK+HH35YX7ZlyxaXZVm/Vj9p0iSX0WSZvv/+e31+mTJl9LYKW7duddnXn3/+6ZK/Z8+eLs9JXvn69OmTY+Tizz//rC/v3r17jmtjtVpdRgPn9To5c+aMy/Pfv39/l/30799fX2Y0GtWZM2eUUjlHtbVo0UKlp6crpZRKT09XERER+Y5Ey032887tp0yZMvl+vX737t1q1qxZ6n//+59688031RtvvKH8/f317bN/TTzrvnMb9fbee++5HPvs2bP6sqSkJBUeHq4vf++995RSSm3evFmfV7du3Rxfubbb7erQoUOFuiZ//vmnvi+LxVKo67Z9+3b9K9aapqlNmzbp7UcaNmyY47zzG2mb10/WbV5//XWXZZfT+iE/cXFx6o033nD5eeCBB/TjZF/2xhtvFHrfhXmtZX8/zf6a79q1q3rjjTfUq6++qgYNGqSPZAaUn5+fOn78uL5t1u2aNWum3njjDfXaa6+pe++9V/n4+OjLNE1Tmzdv1re7kpG2Wd9XgEKPdP/nn39ctnviiSf0ZXa7XbVq1UpfFhYWpr8nZX9vuffee/Xtnn76aZdlY8aM0ZdlHY3atGlTlyxZt6lXr55KS0vTl2X9twfQ2yQUdqQtFO7f17NnzyqTyaTP//LLL10yjh49Wl/WpEkTfX7Wb7KEhIS4vGdMmTKlwPeco0eP5vkaFEIIce2QkbZCCCFEPkaPHs3rr7+O3W5nxIgRHDt2DMgYeeXj45PndllHTYWHh9OjRw99OiIigh49evDDDz/kWDcro9HI/fffr0/Xrl3bZXnmKN4rNWrUKDRNAzLuuF2uXDlOnTp1Wft+6KGH+OyzzwD4/fffOX78OJGRkfz444/6OllHnBaH7Ncv60haPz8/+vfvr99NfNu2baSkpODv759jP6NHj9Yf53et7777biZPnoxSir///pu9e/cSExPDd999p69z55136q+PzNGmmTp16pTnuaxZsybPZc8880yOkYvNmzfHx8eHtLQ0Fi5cSL169WjYsCG1atWiSZMmdO7cmUqVKuW5z0wbNmzA4XDo08OGDXNZPmzYML7//nsAHA4HGzZscHlNZ7r33nsxm80AmM1mqlWrRnx8PFD012tWw4cPp3///jnmHzp0iMGDB+d7HQF9VHZhZX0Oz58/T9myZfNcd82aNYwdO5a6detStmxZzp49S2xsLDVr1qRJkybUqlWLhg0b0qVLF6Kjowt1/NOnT+uPM0fQF8a4ceP49ttvUUrRq1cvLl68CGT83l4t9u/f7zISOrvclpXkjagWL17M4sWLc8w3Go1MnTo115uQAWzatCnXUb0AkyZNKvJNyK5U9vfTrO8FRqORu+66S1/n3Llz7N69m7p16+bYz1133aU/rlq1qsuyrL+7WW/8lt97xIABA1xGmt511136vz2QMbK3Zs2aeW6fXWH/fV2/fj12u12fP3z48DxHLW/ZskX/9yXrc3vTTTcRFhbmkv3ZZ5/NN1/295jTp0/nuI5CCCFKv6vrO0tCCCFECatUqRJ9+/YF0Au2ZrPZpcCXm3PnzumPy5cvn2N51nl5/aFavnx5fH199ensReLMrzlfqex/AGbdf2H33aBBAzp06ABkFPOmTZvG+vXr9dYI4eHh3HLLLYXaV9biYnx8fKGLfFmvdWBgIAEBAS7Ls15rpRQXLlzIdT9Zr0d+1zo6Otql8Prtt9+SkJDA77//rs/L+kd91nwFyVqcy65OnTo55lWuXJnp06dTrlw5AHbu3MmsWbN44YUX6N27N5GRkS5f0c1L9ozZX7PZp/N6btzxmsrugQce4MUXX6Rt27b6vLfeeov77rsvx7q33357gQVbgLS0tMvKcCXPoa+vL99//z1VqlQB4MCBA/z000+88sor3HnnnVSqVIm33377snJcrhYtWnDDDTcAl96/ypYty+DBgy9rP8OGDUNl3AvD5Sdre5jsHw7s2rWraOE9ILPFQ/afwhbLfHx8qF69OsOGDWPjxo2F/sDKYrEQFRVFv379WLZsWZ4tBy7HlT4f7noviIyM1B9n/1p/1mUm06UxRPm9R0REROSbI6/39bwU9t/Xy/ndV0px9uzZHHkKyp7XvoQQQggZaSuEEEIUYNy4ccyePVuf7tu3r8sfnbnJOqomc/RqVlnn5TVyLnPEYqbMUbHu4q79P/TQQ3p/zi+//FL/oxUyRhRlP05eOnfurI9YU0oxY8YMHn744QK3y3qtk5KSSE5OdincZr3WmqYRGhqa636y5izoWtxzzz16z8jvvvuOKlWq6IXAhg0bcv311+eaD+CFF17Az8+vgLPKKXsxOtPAgQPp27cvGzZsYPv27ezdu5dly5bxzz//kJSUxIgRI7jlllsIDAzMc9/ZM2Z/zWafLsnX7IABA+jQoQPPPPMMt9xyC3/88QcA06dPZ/jw4Xoxd/fu3WzdulXfbtCgQbz++utERkaiaRoRERH5FsXzk/X6VKxYkUcffTTPdaOiovTHnTp14uDBg2zevJktW7awb98+1qxZw6pVq0hPT+fxxx/ntttuK3CEYGZRHi5/xPK4ceP0PpsAI0eOvKLXX0E6duyIpml6semrr75i7NixRe5r26FDhxwFrOXLl9OxY0fA88WtiRMn5tnbPD/Dhg1j+vTpbs+TqXPnzi4jUadPn867775b4Ha5vRdkHfV5pe8FWWUt1BZW5oj9vHLk9b6el8K+V2W/Ho888ki+//5n9sEODQ3V/y0sKHtusheLw8PDC9xGCCFE6SNFWyGEEKIArVq1onnz5mzcuBHIuIFPQVq3bq1/nfz06dP88ccf+tfJ4+Pj9cJT5rpFlf0P0JSUlCLvs7B69epFlSpViIuL48CBA3z00Uf6ssu5+c3w4cOZMmWKfqO35557joYNG+ZoJ6CU4ueff6ZOnTrUq1cvx/X76quvGDVqFJBxc7DM5wGgUaNGubZGuFx9+vQhJCSEhIQEdu/ezYsvvqgvyz66Lnu+cuXK6fmy2rFjx2UX5M6dO0diYiLR0dG0adNGv4nZ+fPn9WJDSkoKu3fvdikkZ9eiRQuMRqPeImHGjBn07NlTXz5jxgz9sdFopEWLFpeV0x0MBgPvv/8+derU0XNOmDBBv8FY1g8LIOPmSZmjDZcvX55vwdZkMulfgc7tdyf773O3bt1o2LChyzpKKZYuXap/3dtqtXLw4EHq1q1Ls2bN9JuHKaUoU6YMCQkJOJ1Otm7dWmDRtnr16vrj9PR04uPjc4zey8sdd9zB+PHjOX78OCaTqcBvCVypihUr0r9/f/0Drn/++Ydx48bx7rvvYjQaXdbdu3cvGzZsuOwRv6Lwbr/9dqKjo/VvPXzwwQe0aNHCpYCfaenSpVgsFtq2bZvj/WrGjBn6DQAdDgfffPONviwsLCxHW4HiMnv2bJ566in937qsOYB839+K4oYbbnB5bzSbzbm23zh06BC7d+/Wb1jXrFkz/QaOCxYs4Ny5c/p7cvbsuTly5Ij+2NfXt8APioUQQpROUrQVQgghCuGrr75i165dmM1mWrVqVeD6w4YN48UXX9QLSX379mX48OEEBwfz7bffkpSUBGSM7inMaNKCZP8q7JgxY+jevTsmk4nbbruNWrVqFfkYeTEajYwaNYqnn34ayChWQcYfrfXr1y/0fsLDw/n444+56667UEqRnJxMly5d6NKlC61atcJisXD48GEWL17MoUOH9GLdzTffTO3atdm9ezeQMfJ348aNVKpUiTlz5uhFC8gYJeUOfn5+DBw4kE8++QSAgwcPAhl/0GcvRDVq1IiuXbvqo4gffPBB/U7oBoOBw4cPs2bNGmJjY5k4cSI33nhjoXPs2bNH/1ChUaNGREZGYjKZWLBggct6BY1CK1u2LHfffTdffPEFAN9//z0XLlygVatWrFu3Ti8+QEbP4Px6uhanmjVrMmDAAL799lsgoxi7Zs0aWrduTc2aNTEYDPrXmseNG8eWLVs4e/Ys06ZNy3e/lSpV0l8nb731FmfPnsXPz0/vC3z33Xfz0ksvcebMGex2O23atKFfv37UrFmTtLQ0du/ezfLlyzl16hTLli2jWrVqXLhwgeuuu4569erRokULIiMj8fPz46+//iIhIUE/dmFGCFatWpVKlSrpLQ42b97MTTfdVKhrZjab+e2334iLiyMkJMRlJLC7vfPOO6xbt86lUPjHH39w6623Ur58ec6dO8f69etZtWoVQ4cO9bqi7ezZs3PtMxsVFcWAAQM8kOjK+fj4MH36dLp37056ejoOh4PBgwfzwQcf0LFjRwIDAzl27Bh//vknsbGxTJs2jbZt29KoUSM6d+6sf5Pg9ddf58CBA9SrV49Fixa59LwdN25ckUdSF9aOHTto1aoVN998M//++y8///yzvqxDhw6X1c/2coSFhTF8+HB91PLrr7/Opk2baN26Nb6+vhw7dox169bxzz//MGzYMLp37w7AiBEj9PfNhIQEbrjhBgYMGMDRo0f5+uuvCzxu1tdhixYtcrSYEEIIcY0owZueCSGEEF4t+92vf/vttwK3yXpX72HDhrksW7FihQoNDc3zjuQGg0G9+eabLttkvbt1dHS0y7L87oqtlFJNmjTJ9Tg//PBDrueX/W7UWc8l613hs99hPTdnzpxRvr6+LutNnTq1wOuXm9mzZ6uQkJAC7+ie9fx37typKleunO/6Y8eOdTlOQeeVdVlud/dev359jmP06dMn13M6deqUaty4cYHndLnXfe3atQXuM2um/F4DSUlJql27dvnuq02bNioxMVHfpqDXZPv27fP8/chL9vPOvs/t27crTdP05T169NCXPfDAA7nm7ty5s6pUqVKu11kppR555JFct8t6l/vVq1ercuXKFfp1eeLEiQLXbdGihbLZbIW6LsOGDdO3mzBhQoHXbfv27QXuM6/XXvZlhX3ulFLq0KFDqlWrVgWe++XsM7usr+OiyH7N8vpp3769vk3213z265afKz3/rO/NWbMUxp9//qkiIyMLPMes73EnTpxQ1113Xb7r9+3b1+W1m997S/brnHVZfv/mZX8OcssRFhamYmNj9W3ye0+60n9fk5OTVZcuXS77Nd2vX79c1+vQoUOe1z7ToEGD9OUvvvhiXk+vEEKIUk5uRCaEEEIUk3bt2vHvv//y2GOPUa9ePfz9/bFYLFSpUkW/w/1jjz3mtuP9/PPP9O7dm7CwMLf3vy1I2bJlXb526+vrm+vXcAujf//+HDx4kDfffJMuXbpQvnx5LBYLvr6+1KxZk2HDhjF//nyXEal169Zl69atTJo0iaZNmxIYGIjJZKJixYr07t2bhQsX8t577xX5PLNq0aIF9erVc5mX142HIiIiWL9+PR999BGdOnWiXLlyGI1GAgICqFOnDnfddRczZ87k8ccfv6wMtWvX5q233qJPnz7UqlWLkJAQjEYjZcqUoU2bNrz33nuFuhEZZPTMXbp0KZ9//jkdO3YkLCwMk8lEmTJlaN++PZ988gnLly/PtzduSahfvz633nqrPv3HH3+wefNmAP73v//xwgsvEB0djdlspkqVKjz++OP89ttv+fbRnDJlCuPGjaNy5co5vsqfqXXr1uzYsYPnn3+e66+/nuDgYIxGI6GhoVx//fU8+OCDLF68mHbt2gEZvT4/+OAD7rzzTq677jrCwsIwGo0EBwfTrFkzXnzxRZYuXVro/p5ZW438+OOPhdrGE6Kjo1m9ejW//fYbgwcPpmbNmgQEBGAymYiIiKBLly5MnTqV119/3dNRrwkdO3Zk7969fPzxx9x8881UqlQJX19fLBYL0dHR9OvXjx9++MFlJHGFChXYuHEjb731Fq1atSIkJASTyUR4eDg33XQTs2bN4scff7yi3rRX6u6772b+/Pm0adMGf39/QkJC6NOnD2vXrs31Jo3u5O/vz8KFC/n222/p2bMn5cuXx2Qy4efnR40aNbjjjjv49NNPc9xYcObMmUyZMoXq1atjNpupWrUqzz77rEt7pNykpaUxb948IKMtzLBhw4rt3IQQQng3TSm5NaUQQgghiu7VV1/VWyQMHDiQ7777zsOJhChd6tevz44dOwDYtm0bDRo08HAiIYS7/fLLL/Tp0weAW265hd9++83DiYQQQniKjLQVQgghxBU7efIky5YtY/r06bz55pv6/AcffNCDqYQonSZPnqw/dvfIcSGEd8j83dY0zeV3XgghxLVHRtoKIYQQ4opNnz49R0uAfv368f3333sokRClW8uWLVm/fj0Wi4WDBw/KXeWFKEU2btxIixYtABgwYECh29sIIYQonUquEZEQQgghSi2DwUDlypW58847mThxoqfjCFFqrVu3ztMRhBDFpHnz5siYKiGEEJlkpK0QQgghhBBCCCGEEEJ4EelpK4QQQgghhBBCCCGEEF5EirZCCCGEEEIIIYQQQgjhRaSn7RVyOp0cP36coKAgNE3zdBwhhBBCCCGEEEIIIYSXU0qRmJhIZGQkBkPe42mlaHuFjh8/TlRUlKdjCCGEEEIIIYQQQgghrjJHjhyhcuXKeS4vdUXbV155hZ9//pldu3bh5+dH69atee2116hdu7a+TocOHVixYoXLdvfffz8ff/xxoY8TFBQEZFzg4OBg94T3Uk6nk9OnTxMeHp7vJwCe5O0ZvT0fSEZ3kYxF5+35QDK6i2QsOm/PB5LRXSRj0Xl7PpCM7uLtGb09H0hGd5GMReft+UAyusvVkNFdLl68SFRUlF5bzEupK9quWLGCMWPG0Lx5c+x2O8888wzdunVj586dBAQE6OuNHDmSF154QZ/29/e/rONktkQIDg6+Joq2VquV4OBgr/3F8faM3p4PJKO7SMai8/Z8IBndRTIWnbfnA8noLpKx6Lw9H0hGd/H2jN6eDySju0jGovP2fCAZ3eVqyOhuBbVbLXVF2wULFrhMT58+nYiICP7++2/atWunz/f396dChQolHU8IIYQQQgghhBBCCCHyVepL1wkJCQCEhYW5zJ85cyblypWjfv36PP3006SkpHginhBCCCGEEEIIIYQQQrgodSNts3I6nTz88MO0adOG+vXr6/MHDRpEdHQ0kZGRbNu2jSeffJLdu3fz888/57mvtLQ00tLS9OmLFy/qx3A6ncV3El7A6XSilPLq8/T2jN6eDySju0jGovP2fCAZ3UUyFp235wPJ6C6Ssei8PR9IRnfx9ozeng8ko7tIxqLz9nwgGd3lasjoLoU9x1JdtB0zZgz//vsvf/31l8v8++67T3/coEEDKlasSOfOndm/fz81atTIdV+vvPIKkydPzjH/9OnTWK1W9wb3Mk6nk4SEBJRSXttXxNszens+kIzuIhmLztvzgWR0F8lYdN6eDySju0jGovP2fCAZ3cXbM3p7PpCM7nK1ZdQ0DYfDgVLK07F0SikSExOx2WwF9v/0FMnoHldDxsIyGo35/s4nJiYWaj+ltmj74IMPMm/ePFauXEnlypXzXfeGG24AYN++fXkWbZ9++mkeffRRfTrzTm/h4eHXxI3INE3z6jv4eXtGb88HktFdJGPReXs+kIzuIhmLztvzgWR0F8lYdN6eDySju5RERmW3Yl0wCOe5WDSTH5pfOD4dp2IIramvYz/yJ9Y5N2G58Q0sTcblyFfWz4btz5E4Lx5GM/qghdbEt+OHaP7hKIcN6x8DUBcPoYVUx7fHLDSDCWW3kjrnJvxu+QXNt0yxnFvWjNf681xUV1PGkJAQ4uLivHKUo1KKpKQkT8fIl2R0j6shY2GFhoZSvnz5XAvQvr6+hdpHqSvaKqV46KGH+OWXX1i+fDnVqlUrcJstW7YAULFixTzX8fHxwcfHJ8d8g8HgtW++7qRpmtefq7dn9PZ8IBndRTIWnbfnA8noLpKx6Lw9H0hGd5GMReft+UAyuktxZ1QGA5YG92Gq2gNN00jf8gFpS+8noN+yjOVpCaSveRZT1Z56lhz5TGZ8bngeU6UbAbCuepz01U/h130atkOLMfiG4XfbHFIXDccZtwhz9VuwbpyCT+MHMfqXLZbzypHxGn+e3eFqyAgQHx+PyWQiMjLSq7IqpbDb7ZhMJq8dfSkZ3eNqyFgYSilSUlKIj49H07Rca42F/R0rdUXbMWPG8O233zJ37lyCgoI4efIkACEhIfj5+bF//36+/fZbevbsSdmyZdm2bRuPPPII7dq1o2HDhh5OL4QQQgghhBDC22kmX8zVeurTxootSfv7LX3auuwhfFo8i33fL3nuw+BfHkPgpT/mjRVuIH3L1Iz9G8woeyoAyp6KZrTgOL0N5/nd+LaZ4u7TEdc4p9NJSkoKlSpVwt/f39NxXFwNhTzJ6B5XQ8bC8vPzAzI+DImIiMBoNF7Rfrzn4xM3+eijj0hISKBDhw5UrFhR/5k9ezYAFouFJUuW0K1bN+rUqcNjjz1G3759+e233zycXAghhBBCCCHE1Sj9n/cx17gNANveH0Ez6NOFoZwO0rdMxfTfNsbormiWQJK+aYxmCcYY1Qnrysfwbf9uccQX17jMlggWi8XDSYQoPTI/ALHZbFe8j1I30raghtlRUVGsWLGihNIIIYQQQgghhCjN0ja8jPPCPvz7LsGZfJK09VMIuGNZobdXSmH9czSabxm9962mGfDr8tmlY2x+F3ONXuC0k/LHYHCkYWk0GlNUJ7efj7h2Xe2jG4XwJu74fSp1RVshhBBCCCGEEKIkpP39JrZ9vxDQZzGa2R/70WWolBMkzWwCgLKegQO/olJP59nWwLp8LCrpKH63/oKm5fwyrPPiYeyH/sC/9x9YF96Npf5IjOWvJ3lWKwKH/lus5yeEEMJzSl17BCGEEEIIIYQQorilbX4b2+5ZBPRZhOYbCoC52s0E3XeSoBEHCRpxEHPNO/C54fl8C7bOC/vxu+VnNGPuX023Ln8Y3/Zvo2kGlC0ZNA0yHwsh3OLQoUNomqbfqH758uVomsaFCxc8mutql/26issjRVshhBBCCCGEEOIyOBOPkrZyPCrtAsk/diLpmyYkfdeywO3St31M2rqJADiOryZ9yweoi4dIntWSpG+akPJbH5f1bbu+xRDeEGPZegD4NH8S6/JxJH/bHJ8bnnP/iQlRBA6nYuPheP7YEcfGw/E4nPm3r3SHI0eOMHz4cCIjI7FYLERHRzNu3DjOnj1bpP22bt2aEydOEBIS4qakeTt58iQPPfQQ1atXx8fHh6ioKG699VaWLl1a7McublFRUZw4cYL69esX63EmTZqEpmlomobRaCQqKor77ruPc+fOXdZ+7r77bm6//fbiCXkFpD2CEEIIIYQQQghxGQxBlQl+2Fngen7dp7lMWxo+kHHTp/h4jJFtCtyHuc4gzFmmjRVaEHjXlitILETxWrL7GK8v3sKpxFR9XvkgP57o2pgutSsVyzEPHDhAq1atqFWrFt999x3VqlVjx44dPP744/zxxx+sW7eOsLCwK9q3xWKhQoUKbk6c06FDh2jTpg2hoaG88cYbNGjQAJvNxsKFCxkzZgy7du0q9gxFYbPZMJvNeS43Go0lch0B6tWrx5IlS3A4HMTGxjJ8+HASEhKYPXt2iRy/OMhIWyGEEEIIIYQQQghxRZbsPsb4n9e6FGwB4hNTGf/zWpbsPlYsxx0zZgwWi4VFixbRvn17qlSpQo8ePViyZAnHjh3j2Wef1detWrUqL7/8MsOHDycoKIgqVarw6aef5rnv7O0Rpk+fTmhoKAsXLqRu3boEBgZy0003ceLECZftPv/8c+rWrYuvry916tThww8/zPccRo8ejaZpbNiwgb59+1KrVi3q1avHo48+yrp16/T14uLi6NWrF4GBgQQHB9O/f39OnTqlL580aRKNGzfm66+/pmrVqoSEhDBw4EASExMB+PTTT4mOjs740CiLXr16MXz4cH167ty5NG3aFF9fX6pXr87kyZOx2+36ck3T+Oijj7jtttsICAhgypQpnD9/nsGDBxMeHo6fnx8xMTFMm5bxgVVu7RFWrFhBixYt8PHxoWLFijz11FMux+jYsSNjx47liSeeICwsjAoVKjBp0qR8ryOAyWSiQoUKVKpUiS5dutCvXz8WL16sL3c4HIwYMYJq1arh5+dH7dq1ee+991yu4YwZM5g7d64+anf58uVAxoju/v37ExoaSlhYGL169eLQoUMFZioqKdoKIYQQQgghhBBCiMvmcCpeX7yF3BohZM57ffEWt7dKOHfuHAsXLmT06NH4+fm5LKtQoQKDBw9m9uzZKHXpuG+99RbNmjXjn3/+YfTo0YwaNYrdu3cX+pgpKSm8+eabfP3116xcuZK4uDjGjx+vL585cyYTJkxgypQpxMbG8vLLL/P8888zY8aMPM9hwYIFjBkzhoCAgBzLQ0NDAXA6nfTq1Ytz586xYsUKFi9ezIEDBxgwYIDL+vv372fOnDnMmzePefPmsWLFCl599VUA+vXrx9mzZ1m2bFmO4w8ePBiAVatWMXToUMaNG8fOnTv55JNPmD59OlOmuPbknjRpEr1792b79u0MHz6c559/np07d/LHH38QGxvLRx99RLly5XI952PHjtGzZ0+aN2/O1q1b+eijj/jiiy946aWXXNabMWMGAQEBrF+/ntdff50XXnjBpQBbkEOHDrFw4UIslku9wp1OJ5UrV+aHH35g586dTJgwgWeeeYbvv/8egPHjx9O/f3+9GH/ixAlat26NzWaje/fuBAUFsWrVKlavXq0X7dPT0wud6UpIewQhhBBCCCGEEKKE2OOW4PvnQ9g7/Q9L1W6ejiNEru6ctpQzydYC10u3O7iQmnfhSgGnElPp9P5vWEzGAvdXLsCX7+7pXOB6e/fuRSlF3bp1c11et25dzp8/z+nTp4mIiACgZ8+ejB49GoAnn3ySd955h2XLllG7du0CjwcZrQA+/vhjatSoAcCDDz7ICy+8oC+fNGkSb731Fn36ZPSmrlatml78HDZsWI797du3D6UUderUyfe4S5cuZfv27Rw8eJCoqCgAvvrqK+rVq8fGjRtp3rw5kFGUnD59OkFBQQAMGTKEpUuXMmXKFMqUKUP37t359ttv6dKlCwA//vgj5cqVo2PHjgBMnjyZp556Ss9avXp1XnzxRZ544gkmTpyo5xk0aBD33HOPPh0XF0eTJk1o1qwZkDGqOS8ffvghUVFRfPDBB2iaRp06dTh+/DhPPvkkzz//vL5ew4YN9WPGxMTwwQcfsHTpUrp27Zrnvrdv305gYCAOhwOrNeO1+/bbb+vLzWYzkydP1qerVavG2rVr+f777+nfvz+BgYH4+fmRlpbm0tLhm2++wel08vnnn6NpGgDTpk0jNDSU5cuX061b8b2PS9FWCCGEEEIIIYQoAUop0tc8h+HiXtLXPIc5uqteBBDCm5xJthKfrd1BUeRX2C2KrCNpC9KwYUP9saZpVKhQgfj4+EJv7+/vrxdsASpWrKhvn5yczP79+xkxYgQjR47U17Hb7XnezKyw2WNjY4mKitILtgDXXXcdoaGhxMbG6kXbqlWr6gXb7PkA7rzzTkaNGsVHH32Ej48PM2fOZODAgRgMGV/C37p1K6tXr3YZWZtZAE1JScHf3x9AL85mGjVqFH379mXz5s1069aN22+/ndatW+d5Lq1atXJ532vTpg1JSUkcPXqUyMhIwPW5yu1cclO7dm1+/fVXrFYr33zzDVu2bOGhhx5yWWfq1Kl8+eWXxMXFkZqaSnp6Oo0bN853v1u3bmXfvn0u1xbAarWyf//+fLctKinaCiGEEEIIIYQQJcBxeBHO+E0AOOM34Ti8CFPV7h5OJURO5QJ8C7VeQSNtM4X6WQo90rYwatasiaZpxMbG0rt37xzLY2NjKVOmDOHh4fq87DfM0jQtR4/X/OS2fWbhNSkpCYDPPvuMG264wWU9ozH3846JiUHTNLfdbKyg87vlllt44IEHmD9/Ps2bN2fVqlW88847+vKkpCQmT56sjxTOytf30vOSvZVDjx49OHz4ML///juLFy+mc+fOjBkzhjfffLPYziU3FouFmjVrAvDqq69y8803M3nyZF588UUAZs2axfjx43nrrbdo1aoVQUFBvPHGG6xfvz7f/SYlJXH99dczc+bMHMuyvr6KgxRthRBCCCGEEEKIYqaUwrr2eZd51rXPExDdTUbbCq9TmBYFkNHTtseHvxOfmJprX1sNiAjy44/RPTEa3Pc6L1u2LF27duXDDz/kkUcecelre/LkSWbOnMnQoUNL7HerfPnyREZGcuDAAb1HbEHCwsLo3r07U6dOZezYsTmKoRcuXCA0NJS6dety5MgRjhw5oo+23blzJxcuXOC6664rdEZfX1/69OnDzJkz2bdvH7Vr16Zp06b68qZNm7J792698Hk5wsPDGTZsGMOGDaNt27Y8/vjjuRZt69aty08//YRSSn9uVq9eTVBQEJUrV76sInpBnnvuOTp16sSoUaOIjIxk9erVtG7dWm+RAeQYKWuxWHA4HC7zmjZtyuzZs4mIiCA4ONht+QpDbkQmhBBCCCGEEEIUM8fhRThPbXKZ5zyVMdpWiKuV0aDxRNfGQEaBNqvM6Se6NnZrwTbTBx98QFpaGt27d2flypUcOXKEBQsW0LVrVypVqpTjBlrFbdKkSbzyyiu8//777Nmzh+3btzNt2jSXvqrZTZ06FYfDQYsWLfjpp5/Yu3cvsbGxvP/++7Rq1QqALl260KBBAwYPHszmzZvZsGEDQ4cOpX379jlaFRRk0KBBzJ8/ny+//DJHcXnChAl89dVXTJ48mR07dhAbG8usWbN47rnn8t3nhAkTmDt3Lvv27WPHjh3Mmzcvz17Do0eP5siRIzz00EPs2rWLuXPnMnHiRB599FG9TYO7tGrVioYNG/Lyyy8DGSObN23axMKFC9mzZw/PP/88GzdudNmmatWqbNu2jd27d3PmzBlsNhuDBw+mXLly9OrVi1WrVnHw4EGWL1/O2LFjOXr0qFszZydFWyGEEEIIIYQQohhdGmWbs6xlXfv8ZfXlFMLbdKldiTf7tCIiyM9lfkSQH2/2aUWX2pWK5biZRbjq1avTv39/atSowX333UfHjh1Zu3YtYWFhxXLcvNx77718/vnnTJs2jQYNGtC+fXumT59OtWrV8tymevXqbN68mY4dO/LYY49Rv359unbtytKlS/noo4+AjNYAc+fOpUyZMrRr144uXbpQvXp1Zs+efdkZO3XqRFhYGLt372bQoEEuy7p37868efNYtGgRzZs3p2XLlrzzzjtER0fnu0+LxcLTTz9Nw4YNadeuHUajkVmzZuW6bqVKlfj999/ZsGEDjRo14oEHHmDEiBEFFoav1COPPMLnn3/OkSNHuP/+++nTpw8DBgzghhtu4OzZsy6jbgFGjhxJ7dq1adasGeHh4axevRp/f39WrlxJlSpV6NOnD3Xr1mXEiBFYrdZiH3mrKfnX4YpcvHiRkJAQEhISSnx4dElzOp3Ex8cTERHh9k8+3MXbM3p7PpCM7iIZi87b84FkdBfJWHTeng8ko7tIxqLz9nwgGd3FGzPaDy0kZU6PPJf73/6HV/W29cZrmJ1kdA+n08mxY8dITEykevXqLr1LL5fDqdh85DRnkqyUC/SlaVR4kUfYKqWw2+2YTCavbSMiGd3jash4OaxWKwcPHqRatWo5fq8KW1OUnrZCCCGEEEIIIUQxcR1lm9uYKYP0thWlgtGg0Tw6wtMxhCg1vPOjHiGEEEIIIYQQojRwpKMSj5B7wRbAiUo8Co70kkwlhBDCy0nRVgghhBBCCCGEKCaayQf/AWvB97/+mgYL6XXH6stNdYcScOcGNJOPhxIKIYTwRlK0FUIIIYQQQgghipG6eAis5wAwVrsZe+2RZN6UzHlmG4agyp4LJ4QQwitJ0VYIIYQQQgghhChGtr3f649NMXeAb1kMEU0BcJ7egjP5lKeiCSGE8FJStBVCCCGEEEIIIYqJcjqw7/s5Y8Loi6nqzRkPq3TR17HHLfJENCGEEF5MirZCCCGEEEIIIUQxcRxbiUqJB8BU7WY0S2DG4yrdLq1zWIq2QgghXEnRVgghhBBCCCGEKCa2PZdaI5hr9dMfGyq2AnNGAdd+eBFKOUs8mxBCCO8lRVshhBBCCCGEEKIYKKf9UmsEkx+majfryzSjBVNUp4z1Uk/jPL3VExGFEEJ4KSnaCiGEEEIIIYQQxcBxdAUq9TTwX2sEc4DLclP0pRYJ9sMLSzSbENeSlJQU+vbtS3BwMJqmceHChVznVa1alXfffVffTtM05syZ47HcpUX26yoKR4q2QgghhBBCCCFEMXBpjRDTL8dyU3R3/bFd+tqKq1TyyfOc3X00z5/kk+eL7dhHjhxh+PDhREZGYrFYiI6OZty4cZw9e9ZlvRkzZrBq1SrWrFnDiRMnCAkJyXVedidOnKBHjx7Flj9Teno6r7/+Oo0aNcLf359y5crRpk0bpk2bhs1mK/bjF7eNGzdy3333Fesxli9fjqZp+k94eDg9e/Zk+/btl7Wf6dOnExoaWjwhL5PJ0wGEEEIIIYQQQojSRjnt2Pf/kjFh8sdUrWeOdQyhNdBCqqMSDuA4vhqVnqTfqEyIq0HyyfPMGfQqznR7nusYLCZu//YpAiqUceuxDxw4QKtWrahVqxbfffcd1apVY8eOHTz++OP88ccfrFu3jrCwMAD2799P3bp1qV+/vr59bvOyq1Chglsz5yY9PZ3u3buzdetWXnzxRdq0aUNwcDDr1q3jzTffpEmTJjRu3LjYc1yp9PR0LBZLvuuEh4eXUBrYvXs3wcHBHD9+nMcff5ybb76Zffv2FZjRG8lIWyGEEEIIIYQQws0cR5ejUs8AubdGyKS3SHDasB9dVlLxhHALa0JyvgVbAGe6HWtCstuPPWbMGCwWC4sWLaJ9+/ZUqVKFHj16sGTJEo4dO8azzz4LQIcOHXjrrbdYuXIlmqbRoUOHXOflJmt7hEOHDqFpGj///DMdO3bE39+fRo0asXbtWpdt/vrrL9q2bYufnx9RUVGMHTuW5OS8z//dd99l5cqVLF26lDFjxtC4cWOqV6/OoEGDWL9+PTExMQCkpaUxduxYIiIi8PX15cYbb2Tjxo36fjJHmi5dupRmzZrh7+9P69at2b17NwB79uzBYDCwa9cul+O/88471KhRQ5/+999/6dGjB4GBgZQvX54hQ4Zw5swZfXmHDh148MEHefjhhylXrhzdu3dHKcWkSZOoUqUKPj4+REZGMnbsWH2b7O0R4uLi6NWrF4GBgQQHB9O/f39OnTqlL580aRKNGzfm66+/pmrVqoSEhDBw4EASExPzvI6ZIiIiqFChAk2bNuXhhx/myJEjLuf89ttv06BBAwICAoiKimL06NEkJSXp1/Cee+4hISFBH7E7adIk/fqPHz+eSpUqERAQwA033MDy5csLzFMUUrQVQgghhBBCCCHczKU1Qq2crREyubZIkL62QhTGuXPnWLhwIaNHj8bPz89lWYUKFRg8eDCzZ89GKcXPP//MyJEjadWqFSdOnODnn3/OdV5hPfvss4wfP54tW7ZQq1YtBg0ahN2eUbjev38/N910E3379mXbtm3Mnj2bv/76iwcffDDP/c2cOZMuXbrQpEmTHMvMZjMBARkf+DzxxBP89NNPzJgxg82bN1OzZk26d+/OuXPncuR766232LRpEyaTieHDhwNQq1YtmjVrxsyZM3Mcf9CgQQBcuHCBTp060aRJEzZt2sSCBQs4deoU/fv3d9lmxowZWCwWVq9ezccff8xPP/3EO++8wyeffMLevXuZM2cODRo0yPV8nU4nvXr14ty5c6xYsYLFixdz4MABBg4c6LLe/v37mTNnDvPmzWPevHmsWLGCV199Nc/rmF1CQgKzZs0CcBllazAYeP/999mxYwczZszgzz//5IknngCgdevWvPvuuwQHB3PixAlOnDjB+PHjAXjwwQdZu3Yts2bNYtu2bfTr14+bbrqJvXv3FjrT5ZL2CEIIIYQQQgghhBsphw37viytEarmbI2QyVS5IxhM4LTjOLy4hBIKkb/5I97Beq7gUY1Om6NQ+/vzsc8wmI0FrucbFsTNXzxS4Hp79+5FKUXdunVzXV63bl3Onz/P6dOniYiIwN/fH4vF4tLuILd5hTF+/HhuvvlmACZPnky9evXYt28f9evX55VXXmHw4ME8/PDDAMTExPD+++/Tvn17PvroI3x9fXM9l7xG+mZKTk7mo48+Yvr06XqP3c8++4zFixfzxRdf8Pjjj+vrTpkyhfbt2wPw1FNPcfPNN2O1WjGZTAwaNIipU6fy4osvAhmjb//++2+++eYbAD744AOaNGnCyy+/rO/vyy+/JCoqij179lCrVi39vF5//XV9nfnz51OhQgW6dOmC2WymSpUqtGjRItdzWbp0Kdu3b+fgwYNERUUB8NVXX1GvXj02bdpEy5YtgYzi7vTp0wkKCgJgyJAhLF26lClTpuR7rSpXrqxfM4DbbruNOnXq6MsznxvIGAH80ksv8cADD/Dhhx9isVgICQlB0zSX10VcXBzTpk0jLi6OyMhIION1sGDBAqZNm+ZyvdxJirZCCCGEEEIIIYQbOY4uR1kzboRkqn4rmtk/z3U1n2CMFVvjOLYS54W9OBMOYgipVlJRhciV9VwiKacT3Le/C0lu21dWSqli2W9+GjZsqD+uWLEiAKdPnwZg69atbNu2zWU0q1IKp9PJwYMHcy0yF+Yc9u/fj81mo02bNvo8s9lMixYtiI2NLTBffHw8kZGRDBw4kMcff5x169bRsmVLZs6cSdOmTfWi5tatW1m2bBmBgTl7a+/fv18v2l5//fUuy/r168e7775L9erVuemmm+jZsye33norJlPOsmNsbCxRUVF6wRbguuuuIzQ0lNjYWL1oW7VqVb1gm3ku8fHxBV6rVatW4e/vz7p163j55Zf5+OOPXZYvWbKEV155hV27dnHx4kXsdjtWq5WUlBT8/XN/r96+fTsOh0M//0xpaWmULVu2wExXSoq2QgghhBBCCCGEG9n2ZmmNEHNHgeuborviOLYSyGiRYGn4QLFlE6IwfMOCCl6JjJG2hSnI+oYGFnqkbWHUrFkTTdOIjY2ld+/eOZbHxsZSpkyZYrkBltls1h9rmgZkjAoFSEpK4v7773fp55qpSpUque6vVq1aOfrMFle+ChUq0KlTJ7799ltatmzJt99+y6hRo/T1k5KSuPXWW3nttddy7DezAAzoLRsyRUVFsXv3bpYsWcLixYsZPXo0b7zxBitWrHDJc6XnkXkumeeRn2rVqhEaGkrt2rWJj49nwIABrFyZ8f566NAhbrnlFkaNGsWUKVMICwvjr7/+YsSIEaSnp+dZtE1KSsJoNPL3339jNLq+jnMrcLuLFG2FEEIIIYQQQgg3cWmNYA7AVLVHgduYoruTtuZ5AOyHF0nRVnhcYVoUAJzdfZTfR7xT4Hqd3hpJ2dqVixpLV7ZsWbp27cqHH37II4884tLX9uTJk8ycOZOhQ4fqRcuS0rRpU3bu3EnNmjULvc2gQYN45pln+Oeff3L0tbXZbKSnp1OjRg29h2x0dLS+bOPGjS5f9y+MwYMH88QTT3DnnXfm6CXbtGlTfvrpJ6pWrZrrKNn8+Pn5ceutt3LrrbcyZswY6tSpw/bt22natKnLenXr1uXIkSMcOXJEH227c+dOLly4wHXXXXdZxyzImDFjeOWVV/jll1/o3bs3f//9N06nk7feeguDIeM2X99//73LNhaLBYfDte1HkyZNcDgcxMfH07ZtW7dmzI/ciEwIIYQQQgghhHATx5E/UdaMGwOZquXfGiGTIaIpml85AOxHlqIctmLNKERp8MEHH5CWlkb37t1ZuXIlR44cYcGCBXTt2pVKlSoV2Pu0ODz55JOsWbOGBx98kC1btrB3717mzp2b743IHn74Ydq0aUPnzp2ZOnUqW7du5cCBA3z//fe0bNmSvXv3EhAQwKhRo3j88cdZsGABO3fuZOTIkaSkpDBixIjLytinTx8SExMZNWoUHTt21Hu0QkaR89y5c9x5551s3LiR/fv3s3DhQu65554chcyspk+fzhdffMG///7LgQMH+Oabb/Dz89MLzFl16dKFBg0aMHjwYDZv3syGDRsYOnQo7du3z9F2oaj8/f0ZOXIkEydORClFzZo1sdls/O9//+PAgQN8/fXXOdonVK1alaSkJJYuXcqZM2dISUmhVq1aDB48mKFDh/Lzzz9z8OBBNmzYwCuvvML8+fPdmjkrKdoKIYQQQgghhBBuYtv7g/7YXKtfobbRNAPGKl0zJtITcZxcVxzRhHA735AADJb8R2QaLCZ8QwLyXedKxMTEsGnTJqpXr07//v2pUaMG9913Hx07dmTt2rWEhYW5/ZgFadiwIStWrGDPnj20bduWJk2aMGHCBJfCaHY+Pj4sXryYJ554gk8++YSWLVvSvHlz3n//fcaOHUv9+vUBePXVV+nbty9DhgyhadOm7Nu3j4ULF1KmTJnLyhgUFMStt97K1q1bGTx4sMuyyMhIVq9ejcPhoFu3bjRo0ICHH36Y0NBQfWRqbkJDQ/nss89o06YNDRs2ZMmSJfz222+59nvVNI25c+dSpkwZ2rVrR5cuXahevTqzZs26rPMorAcffJDY2Fh++OEHGjVqxNtvv81rr71G/fr1mTlzJq+88orL+q1bt+aBBx5gwIABhIeH6zdcmzZtGkOHDuWxxx6jdu3a3H777WzcuDHPthfuoClPdG0uBS5evEhISAgJCQkEBwd7Ok6xcjqdxMfHExERke8vqSd5e0ZvzweS0V0kY9F5ez6QjO4iGYvO2/OBZHQXyVh03p4PJKO7eDKjcthI/LQCpJ0HcyBB959CM/m5rJNXvvSdM7AuugcAS4tn8W39Yolmz0qeZ/e4WjIeO3aMxMREqlevjq+v72XvI/nkeawJyXku9w0JIKDC5RUWMymlsNvtmEymEm91UFiS0T2uhoyXw2q1cvDgQapVq5bj96qwNUXpaSuEEEIIIYQQQriB48jSjIItYKp+a46CbX5M0d30x/bDi8CDRVshLkdAhTJXXJQVQuTNOz/qEUIIIYQQQgghrjK2PZduaGOOKVxrhEyGgIoYyjUAwHlqE87UM27NJoQQ4uoiRVshhBBCCCGEEKKIlCMd2/45GRPmQExVb7rsfVwabatwxC1xWzYhhBBXHynaCiGEEEIIIYQQRWSPWwppFwAw1+iFZrr83qCm6O6X9nd4kbuiCSGEuApJ0VYIIYQQQgghhCgi+94f9MemmDuuaB/GyBvhvz649sOLkPuGCyHEtUuKtkIIIYQQQgghRBEoRzq2fb9kTFiCXEbMXg7N5IupcoeMfSYfx3l2h5sSCiGEuNpI0VYIIYQQQgghhCgCe9xiSE8AwFz9ylojZDJW6Xppv4cXFjmbEEKIq5MUbYUQQgghhBBCiCKw7/lRf2yq1a9I+zJVlb62QgghpGgrhBBCCCGEEEJcMWVPw7Z/TsaEJRhTlW5F2p+hTB20oCgAHMdWomwpRUwohBDiaiRFWyGEEEIIIYQQ4gq5tEao0QvN5FOk/Wmahin6v8KvIw3HsZVFjSiEcBNN05gzZ46nY3jMoUOH0DSNLVu2eDrKNUGKtkIIIYQQQgghxBWy7/1Bf2yKKVprBH0/0VlbJEhfWyGy0zQt359JkybluW1xFx5PnjzJQw89RPXq1fHx8SEqKopbb72VpUuXFsvxSlJUVBQnTpygfv36xXqcSZMm6c+l0WgkKiqK++67j3Pnzl3Wfu6++25uv/324glZAkyeDiCEEEIIIYQQQlyNMlojzM2YsIRgynITsaIwRXUGzQDKif3wYrfsU4iSYI9bgnX5OHw7vIepSpdiO86JEyf0x7Nnz2bChAns3r1bnxcYGFhsx87PoUOHaNOmDaGhobzxxhs0aNAAm83GwoULGTNmDLt27fJIrsKy2WyYzeY8lxuNRipUqFAiWerVq8eSJUtwOBzExsYyfPhwEhISmD17dokc3xvISFshhBBCCCGEEOIK2OMWQfpFwD2tETJpvmUwlm8BgPPcTpyJR9yyXyGKk1IK6+pncJ6Lxbr6GZRSxXasChUq6D8hISFomqZPR0RE8Pbbb1O5cmV8fHxo3LgxCxYs0LetVq0aAE2aNEHTNDp06ADAxo0b6dq1K+XKlSMkJIT27duzefPmy8o1evRoNE1jw4YN9O3bl1q1alGvXj0effRR1q1bp68XFxdHr169CAwMJDg4mP79+3Pq1Cl9+aRJk2jcuDFff/01VatWJSQkhIEDB5KYmAjAp59+SmRkJE6n0+X4vXr1Yvjw4fr03Llzadq0KX5+ftSuXZvJkydjt9v15Zqm8dFHH3HbbbcREBDAlClTOH/+PIMHDyY8PBw/Pz9iYmKYNm0akPso5RUrVtCiRQt8fHyoWLEiTz31lMsxOnTowNixY3niiScICwujQoUK+Y6EzmQymahQoQKVKlWiS5cu9OvXj8WLL32I5XA4GDFiBNWqVdPP77333nO5hjNmzGDu3Ln6qN3ly5cDcOTIEfr3709oaChhYWH06tWLQ4cOFZippEnRVgghhBBCCCGEuAL2PVlaI9RyT2uETMboSzc0sx9e5NZ9C1EcHIcX4Ty1CQDnqU04PPS6fe+993jrrbd488032bZtG927d+e2225j7969AGzYsAGAJUuWcOLECX7++WcAEhMTGTZsGH/99Rfr1q0jJiaGnj176oXSgpw7d44FCxYwZswYAgICciwPDQ0FwOl00qtXL86dO8eKFStYvHgxBw4cYMCAAS7r79+/nzlz5jBv3jzmzZvHihUrePXVVwHo168fZ8+eZdmyZTmOP3jwYABWrVrF0KFDGTduHDt27GDq1KnMmDGDKVOmuBxn0qRJ9O7dm+3btzN8+HCef/55du7cyR9//EFsbCwfffQR5cqVy/Wcjx07Rs+ePWnevDlbt27lo48+4osvvuCll15yWW/GjBkEBASwfv16Xn/9dV544QWXAmxBDh06xMKFC7FYLPo8p9NJ5cqV+eGHH9i5cycTJkzgmWee4fvvvwdg/Pjx9O/fn5tuuokTJ05w4sQJWrdujc1mo3v37gQFBbFq1SpWr15NYGAgN910E+np6YXOVBKkPYIQQgghhBBCCHGZlN2K7YD7WyNkMkV3J339C0BG0dZSf4Rb9y9EfpK+bY5KOVno9ZVSkHraZV7Kr7eBXziaphV6P5p/BQIHbSz0+rl58803efLJJxk4cCAAr732GsuWLePdd99l6tSphIeHA1C2bFmXr/p36tTJZT+ffvopoaGhrFixgltuuaXA4+7btw+lFHXq1Ml3vaVLl7J9+3YOHjxIVFQUAF999RX16tVj48aNNG/eHMgoSk6fPp2goCAAhgwZwtKlS5kyZQplypShR48efPvtt3Tu3BmAH3/8kXLlytGxY0cAJk+ezFNPPcWwYcNQSlGlShVeeOEFnnzySSZOnKjnGTRoEPfcc48+HRcXR5MmTWjWrBkAVatWzfNcPvzwQ6Kiovjggw/QNI06depw/PhxnnzySSZMmIDBkDFWtGHDhvoxY2Ji+OCDD1i6dCldu+b9vrl9+3YCAwNxOBxYrVYA3n77bX252Wxm8uTJ+nS1atVYu3Yt33//Pf379ycwMBA/Pz/S0tJcnudvvvkGp9PJ559/rr82p02bRmhoKMuXL6dbt0sfmHmaFG2FEEIIIYQQQojLZD+8ENIzRuCZa/ZGM1oK2OLyGCs0B59QSLuAPW4JyulAMxjdegwh8qJSTqKSjhVtJ04bJB+n+Jok5HTx4kWOHz9OmzZtXOa3adOGrVu35rvtqVOneO6551i+fDnx8fE4HA5SUlKIi4sr1LEL2w4iNjaWqKgovWALcN111xEaGkpsbKxetK1atapesAWoWLEi8fHx+vTgwYMZOXIkH374IT4+PsycOZOBAwfqhdKtW7eyevVql5G1mQXQlJQU/P39AfTibKZRo0bRt29fNm/eTLdu3bj99ttp3bp1nufSqlUrl8J8mzZtSEpK4ujRo1SpUgXIKNpmlf1cclO7dm1+/fVXrFYr33zzDVu2bOGhhx5yWWfq1Kl8+eWXxMXFkZqaSnp6Oo0bN853v1u3bmXfvn0u1xbAarWyf//+fLctaVK0FUIIIYQQQgghLpN974/6Y1OMe1sjAGgGE6YqXTKOk3Yex6mNmCq2dPtxhMiN5l/4m03po2ydtpwLDebLGm17Ocd1t2HDhnH27Fnee+89oqOj8fHxoVWrVoX+ynxMTAyaprntZmPZbwimaZpLD9tbb70VpRTz58+nefPmrFq1infeeUdfnpSUxOTJk+nTpw9KKex2OyaTCU3T8PX11dfL3sqhR48eHD58mN9//53FixfTuXNnxowZw5tvvlls55Ibi8VCzZo1AXj11Ve5+eabmTx5Mi+++CIAs2bNYvz48bz11lu0atWKoKAg3njjDdavX5/vfpOSkrj++uuZOXNmjmWZo7C9hRRthRBCCCGEEEKIy6Dsqdj2/9cawScUU5XOxXIcU3Q3vTjsOLxIiraixFxOiwL7oYWkzOmR+0KnDf+uX2Cq2t1NyfIXHBxMZGQkq1evpn379vr81atX06JFxs39MvuiOhwOl21Xr17Nhx9+SM+ePYGMm1WdOXOm0McOCwuje/fuTJ06lbFjx+Yohl64cIHQ0FDq1q3LkSNHOHLkiD7adufOnVy4cIHrrruu0Mfz9fWlT58+zJw5k3379lG7dm2aNm2qL2/atCm7d++mZs2aOYq2BQkPD2fYsGEMGzaMtm3b8vjjj+datK1bty4//fQTSil9v6tXryYoKIjKlSsX+lwK47nnnqNTp06MGjVKf45bt27N6NGj9XWyj5S1WCw5nuemTZsye/ZsIiIiCA4OdmtGd5MbkQkhhBBCCCGEEJfBfmgh2JIAMNe43e2tETKZqsjNyIR3U0phXfs8eZeXDFjXPl/o1gHu8Pjjj/Paa68xe/Zsdu/ezVNPPcWWLVsYN24cABEREfj5+bFgwQJOnTpFQkICkDFS9uuvvyY2Npb169czePBg/Pz8LuvYU6dOxeFw0KJFC3766Sf27t1LbGws77//Pq1atQKgS5cuNGjQgMGDB7N582Y2bNjA0KFDad++fY5WBQUZPHgw8+fP58svv9RvQJZpwoQJfPXVV0yePJkdO3YQGxvLrFmzeO655/Ld54QJE5g7dy779u1jx44dzJs3j7p16+a67ujRozly5AgPPfQQu3btYu7cuUycOJFHH31Ub9PgLq1ataJhw4a8/PLLQMbztWnTJhYuXMiePXt4/vnn2bjR9cOGqlWrsm3bNnbv3s2ZM2ew2WwMHjyYcuXK0atXL1atWsXBgwdZvnw5Y8eO5ejRo27NXFRStBVCCCGEEEIIIS6Dbe8P+mNzrf7FdhxDcBUMZTJuauQ4uR5lvVBsxxLiijjSUYlHgLy+6u5EJR4FR+FaDLjD2LFjefTRR3nsscdo0KABCxYs4NdffyUmJgYAk8nE+++/zyeffEJkZCS9evUC4IsvvuD8+fM0bdqUIUOGMHbsWCIiIi7r2NWrV2fz5s107NiRxx57jPr169O1a1eWLl3KRx99BGS0Bpg7dy5lypShXbt2dOnSherVqzN79uzLPtdOnToRFhbG7t27GTRokMuy7t27M2/ePBYtWkSLFi1o27Yt7777LtHR0fnu02Kx8PTTT9OwYUPatWuH0Whk1qxZua5bqVIlfv/9dzZs2ECjRo144IEHGDFiRIGF4Sv1yCOP8Pnnn3PkyBHuv/9++vTpw4ABA7jhhhs4e/asy6hbgJEjR1K7dm2aNWtGeHg4q1evxt/fn5UrV1KlShX69OlD3bp1GTFiBFar1etG3mqqJD/uKEUuXrxISEgICQkJXvekupvT6SQ+Pp6IiAi3f1LiLt6e0dvzgWR0F8lYdN6eDySju0jGovP2fCAZ3UUyFp235wPJ6C7FnVHZU0n8pHzGSFufMgTddxLNaC54wyvMZ13+MOlb3gfA7+YfMMf0veLshSXPs3tcLRmPHTtGYmIi1atXd+l1Wuh9JB5BpZ7Oc7nmF4Eh6Mq+Kn+5X+v3BMnoHldDxsthtVo5ePAg1apVy/F7VdiaovS0FUIIIYQQQgghCsl+aMGl1gg1b7+sgu2VMEZ3h/+KtvbDC0ukaCvE5TAERUFQlKdjCFHqeOdHPUIIIYQQQgghhBey7cnSGiGm+FojZDJVbg//9cy1H15Uor1BhRBCeI4UbYUQQgghhBBCiEJQthTsB38DQPMNwxjVqdiPqZn9MUa2zTh+YhzO83uK/ZhCCCE8T4q2QgghhBBCCCFEIdgP/QG2ZABMNXsXe2uETKbobpcyHF5YIscUQgjhWVK0FUIIIYQQQgghCsG290f9sTmmX4kd1xTdXX/sOLyoxI4rri3SekMI93HH75MUbYUQQgghhBBCiAIoWwr2A5mtEcpirNyxxI5tKNcALaAiAPajy1H2tBI7tij9DIaM0lB6erqHkwhReqSkpABgNl/5NzJM7gojhBBCCCGEEEKUVvZDv4M944/wkmyNAKBpGqYq3bDFzgB7Co7jf2Gq0rnEji9KN4PBgL+/P6dPn8ZsNutFXG+glMJut2MymdA0zdNxciUZ3eNqyFgYSilSUlKIj48nNDQUo9F4xfuSoq0QQgghhBBCCFEA254f9MfmWv1L/Pim6P+KtmT0tZWirXAXTdOoUKEChw8f5vDhw56O40IphdPpxGAweG0hTzK6x9WQ8XKEhoZSoUKFIu1DirbCKyi7ldTf78R5bieY/ND8I/Dr9CGG0Jo4U+JJXTgMlbAfjD74dpyKqXK7XPeTvul17Lu+BoMFzeSLb4f3MFZogbKeJ2VeX1TqGYyVbsSv04cAOFNOkzq/P/59FpXoJ+VCCCGEEEKIq4eyJWM/OB8Aza8cxsodSjyDsUoXQAMU9sOLoW2JRxClmMViISYmxutaJDidTs6ePUvZsmW9agRwVpLRPa6GjIVlNpuLNMI2kxRthdcwNxiJqWoPNE0jfcsHpC4eSUC/ZaT99RTGCjfg2/sPHCc3kjKvD4H3HMhRZNXO/4tt+8cEDvkXzRJIeuw3pC57iMA712PbNRNT5Q74tJxA8o+dcZz5F2O5+qStfAyfG1+Rgq0QQgghhBAiT/aDWVoj1OiNZij5P6UN/uEYIprijP8b55mtOJNPYggo2iguIbIyGAz4+vp6OoYLp9OJ2WzG19fXawt5ktE9roaMJU2ugvAKmskXc7We+hB4Y8WWOC8eAsC29wcsDR/ImF+hOVpAJI5jK3LbCzhtKFtyxmTaBQyBlTMeG8woewpKOcGRBkYL9kML0HxDMVVsWcxnJ4QQQgghhLia2fZ6tjVCJlN0N/2x/fAij+UQQghR/KRoK7xS+j/vY65xG87Us+C0uXyCbAiuijMxLsc2qkw9zI3HkTStOomfR5H2z7v4dnwfAHPdu3Be2E/yzKYYq3TGEFiJtA0v49N6SomdkxBCCCGEEOLq49oaIRxj5fYey2KK7q4/lqKtEEKUbtIeQXidtA0v47ywD/++S1D21EJvpyXFYd//C4F378UQGJnRYuH3gQT0X4VmDsD/lkufjltXPIKl2RM4L+wjbeMrAPi0eBZjeCO3n48QQgghhBDi6mU/OB/++7vEVNMzrREyGSu2AnMg2JJwxC1CKSeaJmOxhBCiNJJ3d+FV0v5+E9u+X/C//Xc0sz8Gv7KgmXAmn9TXcV48hCGoSo5tjUfnYyjbAENgJADmevfgOL4a5XBtpO44uQFnymnM1W/Bunwcvje+ju+Nr2FdPq54T04IIYQQQghx1bHt8Y7WCACa0YwpqhMAKvUMzvh/PJpHCCFE8ZGirfAaaZvfxrZ7FgF9FqH5hurzzTH9SN/2MQCOkxtRSccwVsr5lSQVEI3zxBpUehIA9gPzMJSphWa0XFrHYcP611P4tnsrY9qWDJoGmgFlSyrGsxNCCCGEEEJcbVR60qXWCP4RGCu183Ai6WsrhBDXCmmPILyCM/EoaSvHo4VUJ/nHjE+OMfoQeOc6fG58ldSFQ0maXgsMFvxu+hrNaAbAunYChoBITPXvw1G5B0brHpK/aw5GHzRzAH43zXQ5Tvrfb2CuOwRDQHkAfFpNJmXOzRmP275ecicshBBCCCGE8Hr2g/PAYQXAVLOPR1sjZMre19anxdMeTCOEEKK4eP5fHCEAQ1Blgh925r4soDwBfRbmusy31QsAOJ1O0DR82ryMoe2reR7Hp8UzLtPm6rdgrn7LFaYWQgghhBBClGYurRFiPNsaIZMhtAZaSA1Uwn4cJ1aj0hPRLEGejiWEEMLNpD2CEEIIIYQQQgiRjUpPxH7oDwA0//IYK7X1cKJL9BYJTjv2I8s8G0YIIUSxkKKtEEIIIYQQQgiRjf1A9tYIRg8nusS1RULu30oUQghxdZOirSgV7HFL8P29Pfa4JZ6OIoQQQgghhCgFbHuztEao5R2tETKZKneA//rrOuIWezaMEEKIYiFFW3HVU0qRvuY5DBf3kr7mOZRSno4khBBCCCGEuIqptItZWiNUwBh5o4cTudJ8gjFWbA2A88I+nAkHPJxICCGEu0nRVlz1HIcX4YzfBIAzfhOOw4s8nEgIIYQQQghxNbMfnAeONABMMd7VGiGT3tcWsB+SFglCCFHaSNFWXNUc5/eS8segLHM0rKseL5bRtspuJeXX3iRNr03SN41J/rkbzgv7AHCmxJP8Sw+Sptci6esG2I+uzH0f6Ukk/3wTiR+Hc/HDMi7LnAkHSfquJUlf1Sdtw8uXzvFcLCm/9nL7+QghhBBCCCFyZ9uTpTVCjHe1Rsjk2tdWBq4IIURpI0VbcVVypsSTuuwhkr+qC2nnsyxROM/+S9L0WqRtegPnxcNuPa65wUgChu0i8K4tmKvfRurikQCk/fUUxgo3EHj3Hvy6fknqgsEohy3nDgxmfJo/gX+fnH2n0rdOxdJoNAF3bcUW+xUqPRGlFNblj+Db/l23nocQQgghhBAidyrtIvbD/7VGCKiIMbKNhxPlzhDRBM0vHAD70T9z//tDCCHEVUuKtuKqomzJpK1/iaTpMdi2TgXlzH29hP2k/fUkSV9WI3n2jaRv+R/O5JNFOrZm8sVcrSeapgFgrNgS58VDQMZNCiwNH8iYX6E5WkAkjmMrctmHD6aoTmg+oTkPYDCDPQWctozz0gzYtn2MKborhpBqRcouhBBCCCGEKBzbgV/BkQ6AOaavV7ZGANA0A8YqXTMm0hNxnFjr2UBCCCHcSoq24qqgnHbSt3+aMYJ27QRITyz0to4Ta7AuH0fS55VJ/qkL6ds/w5l6tsiZ0v95H3ON2zL25bRhCKigLzMEV8WZGHdZ+7M0GYtt3xySZ7fG0vQxVFoCtn0/YWnycJGzCiGEEEIIIQrHvvdH/bEppp8HkxTMFN1Vf2yPkxYJQghRmpg8HUCI/CilsB/4lbS/nsZ5fleWJQbwKwupZ4Dc+tdqaAGR4FsGdfbf/3bmxHHkTxxH/oRlYzBFd8dUawDmGr3QLEGXlSttw8s4L+zDv+8SlD31Sk/PhSGgIgF9FujTKfP749v2TRxHl5G+7WMw+uDb5mUMwdFuOZ4QQgghhBDClUpLwH444//Jvbk1QiaXm5EdXgStX/JgGiGEEO4kI22F17IfX0vKD+1I/a23S8HWVLMP/oP/QdMM5F6wJWO+chB450YChmzH0uJZDKE1Ly122rEfnI914VASPylPyrx+2Pb+WKgCbNrfb2Lb9wv+t/+OZvbH4FcWNJNL+wXnxUMYgqpc4ZmDbe9PGEKqY4xojHXZWPy6TcNS/17S1k684n0KIYQQQggh8ufaGuGO//7m8F6GgIoYyjUEwHnqb5ypZzycSAghhLvISFvhdRzndpO2+hns+39xmW+MbIPPja9himwNQMCdG1CppwFwOp2cO3eOsLAwDIaM/7HS/CLQTD4Yy9bD2PpFVKsXcMZvxrZnFrbds1FJR/87oBX7vp+w7/sJzIGYa/TCVGsApuhuaEaLS4a0zW9j2z2LgD6L0XxD9fnmmH6kb/sY31aTcJzciEo6hrFS+ys6f2W9QPqW9/HvnfEJv7KngGYAzYCyJV3RPoUQQgghhBAFs+/5QX9sqtXfg0kKzxTdjfQz2wCF4/BiDHXu9HQkIYQQbiBFW+E1nMknSVs3Gdu/n4Ny6PMNZergc+MrmKrfpt8EDMAQFAVBUQBoTieKeIwREXrRNjtN0zCWvx5j+evxufE1HMfXYNszC/veH1Ep8Rkr2ZKw7ZqJbddM8CmDOaYP5loDMVbugEo+QdrK8Wgh1Un+sVPG+kYfAu9ch8+Nr5K6cChJ02uBwYLfTV+jGc0AWNdOQPOvCOV7A5D0TSNUymlIv0ji51GYKnfE76av9JzWv57Ep+VENJMfAD4tniX52+ZgtODX9XP3XGwhhBBCCCGEC2W9gP3wQgC0wEoYK7bycKLCMUV3J/3vN4GMFglmKdoKIUSpIEVb4XEqPZG0v98kffPbYEvW52v+FfBpNQlzveFoBve+VDXNgKnSjZgq3Yhq/y6Oo8ux7Z6Fbd/PkHYhY6W089j+/QLbv1+g+ZfHFHMH/v1XYazYKsfXpAwB5QnoszDXY/m2egGn0wnxGYXhwLu25pvNr8snLtOWBiOxNBh5ZScqhBBCCCGEKBTbgV/BaQPAXLOv17dGyGSMbAMmP7CnYo9bhFLKZbCLEEKIq5MUba8B1uVjse3/DZV4mIBBmzFGNAbAfmgB1jXPgzMdzeSPb+ePMYY3yrG9/fBCfFc8ToopY+SoSo1H869A4OC/UdbzpMzri0o9g7HSjfh1+hAAZ8ppUuf3x7/PIn3EaXbKYcP272ekrX/h0khXAEsQPtc/jqXpI2jmAPdejFxoBhOmKl0wVemCb6cPsR9ehH3PbGz75+hFZJVyCtvWqdi2TkULisJcqz/m2ndiCG8i/0MkhBBCCCFEKWDfe/W1RgDQTL6YKnfAfugPVPIJnGf/xViugadjCSGEKCIp2l4DTDXvwHL9EyR/31afp6znSV1wF/79VmAsWw/7sVWkLriLwCHbc24f3R3rTU2I+K/1QMrcWzFW7gCAbddMTJU74NNyAsk/dsZx5l+M5eqTtvIxfG58JdeCrVIK+76fSFv9LM4Ley8tMJiwNHwAS4vnMfiHu/06FIZmtGCufgvm6rfga0vBfnA+tj2zsR+cD460jPyJR0j/+y3S/34LQ2gMptoDMloolL3OI5mFEEIIIYQQRaOs57EfXgSAFlgZY8WWHk50eYzR3bAf+gPIGHQjRVshhLj6XR3f9xBFYqrcDkNQZZd5zoT9aL5lMZatl7FOpbY4E+NwxG/Od1/OpOPY45ZirjskY4bBjLKnoJQzo6hptGA/tADNNxRTLv+jYz+2ipTZbUid39+lYGuK6U/g0J34dnjfYwXb7DSzP+Za/fC/5UeC7juFb/cZmKr2hCytGpwX9pK+/iWSv65P0jeNSNvwMs4L+3Psyx63BN/f22OPW1KSpyCEEEIIIYQoBNv+uZdaI8RcPa0RMpmiu+uPM4vPVwPr8rEkflGNi+8acMRv0efbDy0g6dvmJH3TiORZrXCczrvFnDMxjpS5t5E0ow5JX9Ujfcv/MuYnHCTpu5YkfVWftA0v6+s7zsWS8muvYjsnIYRwl6vrX6JCeOWVV2jevDlBQUFERERw++23s3v3bpd1rFYrY8aMoWzZsgQGBtK3b19OnTrlocSeYQiNQVnPYj++BgDb/l8hPRHnxUP5bmfbOR1TtZ4Y/CMAMNe9C+eF/STPbIqxSmcMgZVI2/AyPq2nuGznOLuTlF97kfJDexwn1+nzjZU7EDBwPf43z8IQWtO9J+lGmk8wlrpD8L99HoEjT2S0kqjcEbjUGsF5Zjtpa54jaXoMSd+1JG3zOziTjqGUIn3Ncxgu7iV9zXMopTx3IkIIIYQQQogc7Ht/1B+bYq6e1giZDGVqowVVAcBxbBXKluLhRIVjqnkHAf1XoQVF6/MyvxXq1306gXdtxaft66QuuCv3HSiFdd4dmOsOIXDYLgKH7tCfv/StU7E0Gk3AXVuxxX6FSk9EKYV1+SP4tn+3BM5OCCGKptS1R1ixYgVjxoyhefPm2O12nnnmGbp168bOnTsJCMjoj/rII48wf/58fvjhB0JCQnjwwQfp06cPq1ev9nD6kqP5hOB38w+krX4Gqy0JY8WWGMKuAy3vl4RSivQd0/Dt8N6l/ZgD8L/lUu8n64pHsDR7AueFfaRtfAVlT0UzmLAf+A2UU1/PULYePje+hqlqj6uuJ6zBryyWBvdhaXAfzuQT2Pb8gH3PbBwn1urrOE9tIO3UBtJWjsdQth7Os/9mzI/fhOPwIkxVu+e1eyGEEEIIIUQJUtbz2OP+a40QFIWx4g0eTnT5NE3DFN0N27+fgyMN+9EVmKv18HSsApkqt8sxL79vhRojmrqsazi1CowWzLX6XZoXUP6/B2awp2SMoFZO0AzYtn2MKborhpBqxXdSQgjhJqWuaLtgwQKX6enTpxMREcHff/9Nu3btSEhI4IsvvuDbb7+lU6dOAEybNo26deuybt06Wra8unoXFYUpqiOmqI4AKHsaiZ9VzLcvq+PYCnBYXb5647L85AacKafxrX4LybNaY4hogn3Hl3ovWAAtsBI+rV7AXHcomsHo3hPyAENARXyajMWnyVicCYew7f0e2+5ZOE9v+W8NpRdsM2hY1z5PQHS3q65YLYQQQgghRGlk2z8HnHYAzDF3XHWtETLpRVvAcXjhVVG0zU3Wb4WaIlu7fCs0R9H24h40v3BSfr8T5/ndGIKr4tvuTQwh1bE0GUvqwntI3/4plqaPodISsO37Cf/eCz10ZkIIcXlKXdE2u4SEBADCwsIA+Pvvv7HZbHTp0kVfp06dOlSpUoW1a9deU0VbZ/IJDAEVAUjb8CKmqE75tiiw75iG+bphuRZblcOG9a+n8O0+g7R/3sNxaqNLGwQsIfg0fxJLk3FoJj+3n4s3MIRUxafZE/g0ewLHuV3Y98wm/d8vUElHs6ylcJ6S0bZCCCGEEEJ4C9ueS98cvBpbI2QyRXUGzQDKiT1usafjXLHL+lao04Hj6DICBq7FWLYe6ds+JmX+AAIHbcQQUJGAPpcGdaXM749v2zdxHF1G+raPweiDb5uXMQRH59yvEEJ4gVJdtHU6nTz88MO0adOG+vXrA3Dy5EksFguhoaEu65YvX56TJ0/mua+0tDTS0i6NGL148aJ+DKfTmddmXsH65ygcB39HpZwk5ZebwBJEwLDdWNdMwHH8L3DaMVZoiU/nT/VzSVs3EUNAJOYG9+N0OlFpCdj3/4L/4C25nm/axtfQQmuR8kMH1MWDWZZoGKvfim/nz9D8yqIAVQzXy+l0opTymudCC62Fqflz2A7MRyUfd2kNAZC68jH8o7p41Whbb7uGuZGM7uHtGb09H0hGd5GMReft+UAyuotkLDpvzweS0V0uJ6OynsNxJONmwVpQFbSIZsV+bsV2DS0hGMq3wHlyHc5zsdgTDmMIivKujPkdUznR/jueoVJ7/Pr+CWR8KzT5i0poZeq45HE6nTj9IzGEN0YrUxen04mx9mCcf47BYUtDM5r1de37fkYLroZWriEpX9fHf8BaHKc2YV0zAd9u04rvnErZ74sneHs+kIzucjVkdJfCnmOpLtqOGTOGf//9l7/++qvI+3rllVeYPHlyjvmnT5/GarUWef/Fqv7kjJ8skuPjocGL0CDLzIR0ID7jcfUxGf+Nj8fpdJKQ4kT12UNKmgHi4132ZTj1F+bdP2I8v81lvj26N7YGT6ICq5CY6IBE1+3cyel0kpCQgFIKg8E7vs5kOLEc3/hNuS5T53ZydtNnOKNvL9lQ+fDGa5idZHQPb8/o7flAMrqLZCw6b88HktFdJGPReXs+kIzucjkZjQe+w+e/1gjpkT1IPn3aq/JdLlO5Nlj++8bj+X9/xFFj8BXtp6SfZ1+Hg3PnzqHUf38zpp4Cv4zetOZtr6GFt+ZMerDL36JOp5OLfk2xJEzh9KGtKP+KGON+wxwcw+mz5y/tPD0Bn41vkdb+W4iPxzctkdNnzmJIuIgp6SwX46+tv1Oz8/aM3p4PJKO7XA0Z3SUxMbFQ65Xaou2DDz7IvHnzWLlyJZUrV9bnV6hQgfT0dC5cuOAy2vbUqVNUqFAhz/09/fTTPProo/r0xYsXiYqKIjw8nODg4GI5B2/hdDrRNI3w8HCXXxzHmW2kr34Gx2HXPsLGqM5Y2rySo9+QJzJ6ilKK1D/fxokByP0TFN+Nj+PfeAgGs3e0i/C2a5gbyege3p7R2/OBZHQXyVh03p4PJKO7SMai8/Z8IBnd5XIypq5ZiOO/x8GNhmGMiPCqfJfLUbc3qf++BYD/+XX4RTxyRfspqedZ/1Zo6kn8V9516VuhS593/VbozV+h+YQCl74Vaqw3Ek3T8Ov8EaY1wwEFlmB8bplNcLmILMeYiOnGFwmJzGiDYGv5PMalt6AZLfh0/rRYn/PS9vviCd6eDySju1wNGd3F19e3UOuVuqKtUoqHHnqIX375heXLl1OtmutdIa+//nrMZjNLly6lb9++AOzevZu4uDhatWqV5359fHzw8fHJMd9gMJT6FxNk3I0081ydF+NIWzsRW+xXgNLXMZRrhG/b1zBFd/N4Rk9T9jRU0hHyKtgCYE8mfelI/HrM9Jo2Cd50DfMiGd3D2zN6ez6QjO4iGYvO2/OBZHQXyVh03p4PJKO7FCajM/UsjiNLM9YPisZU8YYS+//y4rqGWsUW4BMKaRdwHFmKhhPNcGV/9pfE8+zf5ZPc53f9LM9t/Fq/CFwq8Jirdsenet43Xct+DJ+G9+HT8L4rSHtlSsvviyd5ez6QjO5yNWR0h8KeX6kr2o4ZM4Zvv/2WuXPnEhQUpPepDQkJwc/Pj5CQEEaMGMGjjz5KWFgYwcHBPPTQQ7Rq1eqaugnZ5bDHLcH3z4ew3/gKzlPrSd/yPjgu9ffVgqrg0/pFzHUGX7V3WnU3zeRDwJ0bUKkZX69yOp2cO3eOsLAw1IW9WBePAIcV+55ZpJdrgE+Lpz2cWAghhBBCiGuLff8voDLG2Zpr3eE1AymKQjOYMFXpgn3vjxmF25MbMUXmPThJCCGE9yp1RduPPvoIgA4dOrjMnzZtGnfffTcA77zzDgaDgb59+5KWlkb37t358MMPSzjp1UEpRfrqZzBc3Iv1jwH6/9QA4FMGnxbPYGk0Bs1UuKHd1xJDUBT81/hfczpRxGOMiMBQoRmayYfUeXcAkLbmWQxhdTHXvN2DaYUQQgghhLi22Pb8qD82x/T3YBL3MkV3yyjaAo64RVK0FUKIq1SpGxaplMr1J7NgCxm9I6ZOncq5c+dITk7m559/zref7bVKKSfWv57EeXrzfzP+K9gafbBcP56ge/bhc/1jUrC9AuaaffD572s9AKkLh+CI3+K5QEIIIYQQQlxDnKlnLrVGCK6KoXwzDydyH1N0d/2x/fAiDyYpXva4Jfj+3h573BJPRxFCiGJR6oq2wn2UAtvWj1xn+pYlYOgufNu+juZbxjPBSglL82cw1R6UMWFLJuW3XjiTT3o2lBBCCCGEENcA+74srRFi+pWK1giZDEFRGMLqAuA4uR5lPe/hRO6nlCJ9zXMYLu4lfc1zKKUK3kgIIa4yUrQVeXLGLQZ7sutM61nU+V2eCVTKaJqGX9fPMFa4AQCVeITU3/qg7FYPJxNCCCGEEKJ0s+39QX9srtXPg0mKh35zaOXE/t+I4tLEfmAezvhNADjjN+EoxSOKhRDXLinailwppbCufR40o+sCzYh17fPySaabaCY//G79BS2wMgCOk+uwLrlPrq8QQgghhBDFxJlyGseRZQBowdUwRFzv4UTuZ6zSTX9sP7zQg0ncTylF6pJ7XebJ36hCiNJIirYiV47Di3Ce2uR64zEA5cB5Sj7JdCdDQAX8b5sLJn8AbLu+IX3Tax5OJYQQQgghROlk35+lNUKt0tUaIZOpcnsw+gAZfW1LU0Ezbc1zkHraZZ78jSqEKI2kaCty0EfZ5vnyMMgnmW5mjGiC301f6dNpq5/Ftn+uBxMJIYQQQghROtn2ZG2N0N+DSYqPZvbHWKktkNGGzXl+t4cTuYfj/N48B7hYVz0hf6MKIUoVKdqKnBzpqMQjgDOPFZyoxKPgSC/JVKWeuWYffFq98N+UInXBXThOb/VoJiGEEEIIIUoTZ0o8jqP/tUYIqY4hvImHExUfva8tpaNFgrJbSZnTE1Tuf6c6z27HfmhBCacSQojiY/J0AOF9NJMPAXduQP33lROn08m5c+cICwvDYMio82t+EWgmH0/GLJUsLZ7FcS4W++7vwJZMyq+3ETBwA4aA8p6OJoQQQgghxFXPvu9nvehnjimdrREymaK7k7bqCSCj/R1Nxnk4UdGkrnwMlbA/33WsS+/HNOJwqX5ehRDXDinailwZgqIgKAoAzelEEY8xIkIv2orioWkafl0/J/nCPpynNqISj5A6rw/+fZeimXw9HU8IIYQQQoirmm3vj/rj0toaIZOhbH20gIqo5BPYjy5H2a1X7d8Utr0/Yt/2UYHrqaSjOOL/wVS+aQmkEkKI4iUVOCG8jGbyw/+2OWiBlQBwnFiLden90p9JCCGEEEKIInAmn8JxdDkAhtCaGMIbezRPcdM0DVOV/1ok2FNxHP/Ls4GukDPhAKmL79WnLc2fIWDQJvwGbiC12wL8Bm7AVGeQvty6ZCRKWvkJIUoBKdoK4YUMARXxv20umPwAsMV+Tfqm1z2cSgghhBBCiKtX1tYIppg7romv0F/tfW2VPY2U+QMg/SIAptp34tP6RYwRTTFGNEWFNcQY0RS/Lp9jCLsOAOfpf0hb/6InYwshhFtI0VYIL2WMaIpf96/06bTVz2Db/6sHEwkhhBBCCHH1su39QX9sjindrREyGaO7AhnFafvhxZ4NcwXS/noCZ/zfABhCY/Dr/HGuxXbN5Itf9xlgyOgAmb7xFewn1pdoViGEcDcp2grhxcwxffFpNfm/KUXqgsE4Tm/zaCYhhBBCCCGuNs7kkziOrQQyin+G8EYeTlQyDH7lMJS/HgDnmW04k094OFHh2fb9QvqW/2VMGH3w6zkbzRKU5/rG8tfjc8PzGRPKiXXRMJQtpQSSCiFE8ZCirRBeztLiOUy1BmZM2JJJ+fU2nMmnPBtKCCGEEEKIq4hLa4Ra/a6J1giZXFskLPJgksJzJhwkddFwfdq3/TsYIxoXuJ2l+dMYyrfI2Mf5PaStfqq4IgohRLGToq0QXk7TNPy6fYGhfHMAVGIcqfP6ouxpHk4mhBBCCCHE1cG259prjZBJvxkZV0fRVjnSSfl9IKQnAGCK6Y+5wf2F2lYzmPDrPh2MvgCkb/kAe9yS4ooqCsm6fCyJX1Tj4rsGHPFb9Pm2g7+TNPN6kr5pQtLXDUjfOSPX7bWkOFK+a6GvlzK/P8p6Hsgo8Cd915Kkr+qTtuFlfRvHuVhSfu1VrOclRHGToq0QVwHN5If/rb+gBVYCwHFiDdal96OU8nAyIYQQQgghvJsz+cSl1ghlamEo18DDiUqWsWIr+K+tgCNuMeq/EcfeKu2vJ3Ge2giAFlIDvy6fXtbIaGNYHXxufFWfTl00HGW94O6Y4jKYat5BQP9VaEHR+jylFKkLhuDXbRqBd/2D/22/YV36ACo9Mcf2yq88fv1WEHjXPwQO2Y4hoCJp6yYBkL51KpZGowm4ayu22K9Q6YkopbAufwTf9u+W0BkKUTykaCvEVcIQGIn/rXPA5AeALfYr0v9+w7OhhBBCCCGE8HL2fT8DGYMdTDHXVmsEAM1oxlS5EwAq9QzO+M0eTpQ32/65pP/zXsaE0YL/zd+j+QRf9n4sjR/EGPXfOScdxbpinDtjistkqtwOQ1DlHPM1TUOlXQBApV9E8y0LRp+cOzD6oP33d7ByOlC2ZDJvsIfBDPYUcNoyWqBoBmzbPsYU3RVDSLViOiMhSoYUbYW4ihjLX59xV9T/pP31NLb9v3owkRBCCCGEEN7NpTVCrWurNUKmq6GvrTPhEKmL7tGnfdu9hTGiyRXtS9MM+HX9EiwZBV9b7NfY9v7klpzCPTRNw6/nLFLn9SXxi6ok/9AWv+7T0YyWXNdXjnSSvmlC4ifhOC/s02/YbWkyFtu+OSTPbo2l6WOotARs+37C0uThEjwbIYqHFG2FuMqYY+7Ap+Wk/6YUqQvuwnF6mycjCSGEEEII4TZF7X+p0pPwWX4nSZ+W5+KHoTiOrQLAUKYOmAKuyf6Xpqrd9cfeWLRVjnRS/7gT/ht1aYq5A3PD0UXapyG4Cr4d3tenrUsfkBs6exHltJO2fgp+t/xE0IhDBPRZQuqCoThTz+S6vma0EHjXPwTddxJDmdqkb/8EAENARQL6LCBw8GYsDe/HuuJhfNu+iePoMlLm3UHKH4NxXjxckqcmhNtI0VaIq5Dlhucx1RqQMWFLIuXX23CmxHs2lBBCCCGEEG5Q1P6XGMzY6o7Br/dCcNjQWyPUugPbtg+vyf6XhpDqGEJrAhn3x1BpFz2cyFXa6mdwnFwPgBZSHb8un7mljYW57hBMNXoDoKxnsS69T+4L4iWcp7egko9jqtwOAGOF5mhBlXHG/5PvdprRgrnePdhiv8mxzLb3Jwwh1TFGNMa6bCx+3aZhqX8vaWsnFss5CFHcpGgrxFVI0zT8un2JoXwzAFRiHKnz+qLsaR5OJoQQQgghRNEUtf+lZvLBWf5GNJ9QcKbr880x/a/p/pfGKl0zHjjt2I8u82yYLGwHfiN989sZEwYz/j1nofmEuGXfmqbh2/ljNP8IAOwHfsO2c7pb9i2KRguMyrhJ4LlYAJwX9uG8sB9Dmdo5100+irKlAKCUE/veHzCUa+iyjrJeIH3L+/i0zCjQKnsKaAbQDChbUjGfjRDFw+TpAEKIK6OZ/PC/dQ7J37VAJR/HcXw11qUP4Nvty2vu5gpCCCGEEKJ0y9r/EnMAKu08/rf8lGf/SwBn8klQDgAMYXUxlK2HpclYUhfeQ/r2T136X/r3XlhSp+Ixpuju2LZ9BID98ELMNTzfDsJ5MY7UhXfr077t3sT438AUdzH4h+Pb+RNSf8sYcWtd8TCmyh0xhFR163FE3lKX3I/90O+o5JOkzLkJzEEE3bMXv86fkDp/QEZxVTnx6/g/DMFVALCunYAhIBJT/fswXNhJ6poRGfceU06MEU3x7fCeyzGsfz2JT8uJ+g3LfFo8S/K3zcFowa/r5yV9ykK4hRRthbiKGQIj8b9tDsk/tAd7KrbYGRjKXodPs8c9HU0IIYQQQgi3ydr/0lS5HY6TG0n5tRcBQ7Zh8CuX6zaOw5cKsaaYfmiahvZf/8tMKfP76/0v07d9DEYffNu8jCE4OrddXtVMUR3BYMoYaesFfW2Vw/ZfH9vzAJhq9sHc6MFiOZa5Ri/s192dMco2PZHURffgf8dSNE2+fFwS/Lp8kut8c507Mde5M9dlvq1eAMDpdOKo1A3/JndhMOT9fGU/hqXBSCwNRl5hYiG8g7xDCXGVM5Zvhl+3afp02l9PYTvwmwcTCSGEEEII4V5X0v/SfnC+/thcq1+O5dda/0vNEoSxYhsAVMIBnBf2ezRP2ppncZxYC4AWXBW/Lp8X6zcGfdu/gxaUMYrTcWwF6f+8V8AWQgjhWVK0FaIUMNfqr/fuAUXqH4NxnNnu0UxCCCGEEEK4y+X0vwTQUo6jTm8GwBB2Hcay9VyWX6v9L03R3fTH9sOeawlhOzif9L/fzJgwmPHvORvNN7RYj6n5hLgOdln9DI6zO4v1mEIIURRStBWilLDcMAFTTP+MCVsSKXNvw5kS79lQQgghhBBCXKbUJfeT+HkUKukoKXNuInFaDIaA8nr/y6RvGpPyW58c/S/Tt32s78Nn0c36Y2fSEVIXDHU5Rl79L63Lx+HT/KkSOEvPcC3aeqZFgjPxCNYsfWx9bnwNY4XmJXJsU1RHLE3GZUw40khdOAzlsJXIscWVscctwff39tjjlng6ihAlTnraClFKaJqGX7cvSb54AOepTajEw6TO64t/nyVoppx31RVCCCGEEMIbFaX/ZSYVGAXWUwAEDFiLsex1+R7jWul/aYhoguYXjko9jf3InyhHer43c3O3jD62g1DWswCYqve6VEQtIT5tXsZ+aCHO87twxv9N2oYp+LaaVKIZROEopUhf8xyGi3tJX/Mc5uiuctNtcU2RkbZClCJpq59CJZ/Upx3HV5O66B6Svmly6Wd6bS6+Z0ZZz+W6D2diHClzbyNpRh2SvqpH+pb/ZcxPOEjSdy1J+qo+aRtevnSMc7Gk/Or5O88KIYQQQggBGSM5jWc2AWAoWy9HwfZapmkGjFW6ZkzYkvSesiUlbe0EHMdXZ2QJisav25clXoTTTH743fQVaEYA0jdMwXFyY4lmEIXjOLwIZ3zG77IzfhMOL7iBnhAlSYq2QpQippp3EDBgDZp/RTBkfGJu3zMLc93BBN71D4F3/YO5wUhMVXug+Ybl3IFSWOfdgbnuEAKH7SJw6A695UL61qlYGo0m4K6t2GK/QqUnopTCuvwRfNu/W4JnKYQQQgghRN7SN76iPzbH5LwB2bXOUy0S7IcWkL7ptYwJgwm/nrPQfMuU2PGzMpZvhqXFsxkTykHqwqEoe6pHsojcKaWwrnnOZZ51zfMopTyUSIiSJ0VbIUoRU+V2GIIqg9GCT6vJ+vy0VU9iOzAPANu/X2KuNzzX7Q2nVoHR4nJ3XUNA+f8emMGeAk4bKCdoBmzbPsYU3RVDSLXiOykhhBBCCCEKSSmFfddMfdoYc4cH03gnTxRtnUnHSF14qa+wz42vYqp4Q4kcOy8+LZ7FEHE9AM7zu0n762mP5hGuMkbZ/u0yT0bbimuNFG2FKKVM0d2x3DDhvylF6h+DSI/9BpV2HlP1W3LdxnBxD5pfOCm/30nSzKak/NYHZ8IBACxNxmLbN4fk2a2xNH0MlZaAbd9PWJo8XDInJIQQQgghRAFssd+APfnSjMQjngvjpQwBFTCUawSAM34zzpTTxXo85bRn9LFNPQOAqfqtWJo8UqzHLAzNaM5ok2DMuP9H+pb3sR/508OpBGR8+JK64uFcl6WueERG24prhhRthSjFfFpOwJT5lTBbEtY/R2OqeQeaIY97EDodOI4uw+eG5wgcvBlTdDdS5g8AwBBQkYA+CwgcvBlLw/uxrngY37Zv4ji6jJR5d5Dyx2CcFw+X0JkJIYQQQgjhSilFmsvXqTWsa+Xr1LkxRf/X1xaFI25xsR4rbe1EHMdWAaAFReHXbZrX3EzKGFYXnzaX2mmkLroHlZbgwUQCwL7vF9T53bkuU+d3Yd/3SwknEsIzpGgrRCmmaQb8uk3Tv/aTebMBZU/LdX0VUAlDeGOMZesBYK47BGf8ZpTD5rKebe9PGEKqY4xojHXZWPy6TcNS/17S1k4s1vMRQgghhBAiL47Di1BJWUfWKpyn5OvUuTFFd9cfF2eLBPvhRaRvfDVjwmDCr8es3O+t4UGWJmMxVu4AgEo8gnWF50cBX8uUUqQuvT/fdVKX3IfT6SyhREJ4jhRthSjlNLM//rfNAUsIAM7Tm7H+OSrXEQeOip1QScdwJh0DwH7wdwxhddGMZn0dZb1A+pb38WmZUaBV9hTQDKAZULak4j8hIYQQQgghslFKYV37fM4FmlFG2+bCGHkjmPyBjMJqcVwfZ9JxUhcMATL27dN6CqbIVm4/TlFlDnTBEgSAbed0bPvmeDbUNcy26zuwns1/pbRz2HZOK5lAQniQFG2FKEVSl9xP4udRqKSjpMy5icRpMfyfvfsOj6LcHjj+ndmeQgkk9N4E6U1AegcFFcFLU7Agtmu/4FVU1GvDcpHftWClCRaaCEivCkhvUqSGTujJ9t2Z+f2xYSEkgQAhm4TzeR4ed955Z+bMLC6zZ985L4AaUwo1tiwoobIIgW1j8K//GADvytfwb/4itANzFLY2n+KefifOCXXxb/ofjq6T0hzD+/tQbE1eRzE7gFABf9fERniXPIOt0Us5dKZCCCGEEEJcoCXOQz++Nv0KQ5PRthlQzDbM50eXuo+hn9ySrfsP1bHth+EJ1cs1V7gDa4MXsvUY2UktUA57q0/Cy96Fg9HdSRGM6OakOw/jXfLP8LK16Zs4eq/G03EOjt6r05Sy8C19Dv3s7kiEKUSOyaSwpRAiL3K0H53pupj7NxHY+QOe3/oC4Fs+BDXuFuxN3wQIP15iLtcRa4XOWT6GtdYgrLUGXW/oQgghhBBCXJMLo2wVzo/qTEvFu/JVost1zDW1VHMDU7mOBPfPBiCYOBdTfO1s27fvzzfRDi8FQIkpjb3jGBQld48Zs9QYQHDPLwT3/oLhOYF3wWAc3abK35kcYhg6nnkPge8MAOYq92Fr/AqGYWCQhCkhAUvxhuhn/ybw13cQcOL+rR/R9/2e5slQIfKT3P2pKYTIVpZqvbHedv6xMQPPb33RTm6NaExCCCGEEEJcF82PkXKQjBO2ADpGyiHQ/DkZVa53o+raBg8swP/n26EFxYSj6yRUR5Fs2/+NoigK9vajURxFAQju/YXA9nERjurmEdj0aXhSPCW6JI52n2WYMLe3+gS1UOiJUv34GnwrX8vROIXISZK0FeImY2vyOuYqPUML/hTcM7qju09ENighhBBCCCGukWK2Ed1nNaZSLcJt3ubf4ei9mui+a0N/+qxGMdsiGGXuoxauihJbFgDtyHKMgOu696m7juKZ058LdWz/g7nk7de935yiRiVgb3fhyULvkmfQkw9EMKKbg3ZqG97lQ8PLjo7fZTphnWKNwdFlIqih0bX+tSMIHlyUI3EKkdMkaSvETSZUaH8MakJ9AIzk/Xhm9SS4fw722a0IHlgQ4QiFEEIIIYS4OmpsGfRze0ML1oLopTpiSqgf/qPGlo5sgLmQoiiYy3UMLWh+goeWXtf+DF3D81t/jNRasObyXbA2/Nf1hpnjLJXvwVL9gdCCPxnP/IcwDD2yQeVjhubHM/cB0LwAWOv+E3O5DpfdxlSsAbZm/zm/BzxzHkD3XGHyMiHyIEnaCnETUixRRHWbjhJdAgDt8HK88wagJu/Cv2KYzK4rhBBCCCHyFN2dhOE8DIAaXwekDmmWXFwiQUuce1378v35FtqhxUDo8fa8UMc2M/ZWI1FiywCgHVyEf+P/IhxR/uVb9QZ60noA1Ljq2Jq/l6XtrA1ewFS2PQCG6wjeBYPke6zId/LmJ6gQ4rqpsaWJ6jYNTPZQgzf0y6SeJLPrCiGEEEKIvEVP2hB+bYqvF8FI8hZzmXagmAAIJs6/5v0EDy7C/+dboQVFDdWxjYrPjhAjQrEXwtHh2/Cy7/eX0E7viGBE+VPwyB/4174fWlDNODqPRzE7srStoqg4Oo29UIN4z3QCW768UaEKERGStBXiJmYq3hh7+68vaVXwrnxVfqUUQgghhBB5hnbiQtJWlaRtlin2QpiK3waAfmbHNdVv1V3H8PzWj3Ad26ZvYr6ovnBeZS7bDmvdf4YWNC+euQ9gaIHIBpWPGP4UPHMegNTSE7Ymb2BKLeGXVWp0Cewdvgkve5c9j3ZqW7bGKUQkRSxp63a7+eWXXxgyZAh33nknTZo0oWnTpnTr1o0hQ4bwyy+/4HJdfyF0IcTlpZ/J1UA/vpbAtrERiUcIIYQQQoirpV000lZNkKTt1bi4fmjwKkskGLqGZ879GO7jAJjKdsTa6KVsjS+SbLe/i1q4GgD68bX417wb4YjyD+/SZzGS9wFgKnk71oZDrmk/lordsNR5IrQQ9OD5rS9G0JtdYQoRUTmetN2yZQsDBw6kePHi3HPPPXz66afs3r0bRVEwDIO///6b//3vf9xzzz0UL16cgQMHsmXLlpwOU4ibgmEYeFe+Gn4k6mLe+Q/j2/g/KbovhBBCCCFyPS21JiZmRzjJdrPzLnmalG8qkDxSRUvaGG43gj48i5/COaYqzvG1CR5dGV4XvLhMmqHjW/YCznG34pxQB9fktuhndwOgn9uHc1ITnF+VRDu4EAAlugS224bh+fWeHDm/nKBYonB0Ghv+vuRb/R+042sjHFXeF9g9ncBf34UWLDE4Oo1DUdN/J80qe4sPUIvUBEA/uRnf7/nnhwNxc8vRpO0//vEP6tWrx44dOxg+fDibNm0iOTmZHTt2sHLlSlatWsXOnTtJSUlh06ZNDB8+nJ07d1KvXj369OmTk6EKcVPQEuehH18LhpbBWgPfkqdxT+mAfm5/TocmhBBCCCFElhi+cxjn9gBgKlobRTVHOKLcwVy5J9H3LUeJLZem3ffHS4BC9ICdxNy/GXv7b8BWGIDggQUYehAA0+G5aEdXEN1vIzH9N2Eu0xbvH68A4N/0KeYybTE8J1P3qmDv/D2+P/+DvdXIHDrDnGEq3hhr43+HFvQgnrkDMIKeyAaVh+muY3gXPhpetrf+BLVghevap2J24OgyEUw2APwbRxHYN/u69ilEbpCjSVtVVVm7di2rVq3i+eefp1atWphM6X9NMZlM1KpVixdeeIGVK1eydq38kiVEdguPsr3Cx4B2aDHOCbXxbx4tdW6FEEIIIUSuo53YGH4tpREuMJduiRpbOk2bEXDh/+tb7M3eRlEUAEyxpTCXbR/q4D+Hdmx1am8FND9oXgzDwPAno8aUCu1HD+Df/Bnn69haG7+CcXo75nIdrjsBlxvZGg9DTa23qp/ejm/FsAhHlDcZhoF3/iPhZL+50t1YagzMln2bitbE3vLD8LJ33oPormPZsm8hIiVHk7aTJk2ibt26V71d3bp1mTRpUvYHJMTNTPNjpBwELlP+QEn9iAg48S56HPe0Ttc0OYEQQgghhBA3ysX1bE0yCdll6Wf3oNji8K15B+fERrh+aknwwELM5TqG+5wvkaCV6oipdCtSviyB86uSBA8uwtb0TQxDRzuxCfzJACiFb8FScxCB3VOw1ns2Eqd1wykma6hMwvmRnOtHEjy4JKIx5UWBLaMJ7g+NgFWiimFvNzr840F2sNR+AnOFOwEwPCfwzB0o5f5EnhaxiciEEJGlmG1E91lNdN+1RPddi6P3ajwd5+DofaEtuv8WLDUfCW+jHViAc0It/Fu/llG3QgghhBAiV9BPXJS0vcrZ5286RhAjJRE1rgYxfddgb/0Jntm904xQ1lKTturpTeinthL7yCFiBh0OlUdY9Dj+Ne+iH14GgBJVnOiei/EtfwF7iw/RDi3GPbMn7t/6oScnRuQUbxRTkVux3f526pKBZ96DGL7kiMaUl2hn/sa77MXwsr3DN6hR8dl6DEVRsHf4BiW6ROiYB+bh3/BJth5DiJyUq5K2Y8eOpWPHjtx66620a9eOL7/8UhJDQtxAamwZTAn1w3+MuNpplk1FquNo/yVRd/+GEpP6aJU/Be+CR3FP74qeciiyJyCEEEIIIW564UnIVHN4MiKRMSW2LCgqllv6AWBKqIdSsAJ4T6PGVQdAO74aw3sG0/6fMZVug2IvhKKoWGoMILh/Lr6Vr6fuTMXR5Xu0I7+jFqyIKaEu3sVP4+j4Hdaaj1zol4ddOpmbtd6zmEq1AsBISSTlmzI4x9fGM+f+TPehnN2Oe0pbnGNr4Bxbg8DuqQBox9finFAP57hb8W8bG+4fPLgIz4LBN/bEcpihBfDMeQCCbgAstQZjqdD1hhxLjYoPjYpO5fv9pTSj8YXIS3JN0vatt97ipZdeolmzZjzzzDPUqlWLZ599lqFDh0Y6NCFueubynYi5fwuWWx8Mt2mJc3GOr4X/rzHy44oQQgghhIgII+BGP70dADXuVhSzLcIR5W6qoyimMu0IJs4FQD+3D+PcPtS46hdKJBg62sGFGNHl0A4txtD8AAR2/ogRSIHUx81tt72KKb4e/o2jsDUJJWiNoDtUYk1RMQLOnD/BbHbpZG6KouLo+B2ollAHfwq2Zv/B1uKDDLc3Am5syx/E1vRNYgZsI/r+LZhKtgDAt+Z97K0/IbrPanyr3gz1D3rwrXoDe/P3b/zJ5SDf6rfRj4dqJauFqqSpPXsjmMu2x9ogdVSvHsDzW1+MgOuGHlOIGyHHp9VMTEykXLly6drHjBnDDz/8QKtWrcJtxYsX5+OPP2bEiBE5GaIQIgOKrSCODt9grnwv3gWPYriOgP8c3vkPEdw9BXu70agxJSMdphBCCCGEuIloJ7eEk4gmmYQsDc+CwQT3z8ZwHcM9vTNYYol9cBeOdp/jmf8Ivt9fAkXF3u4L1JhSmMt1Cj9KHkycR/DWV1ECh3BNqAuqBd11BDQfAKYybbE2HoZ30RPYmryOYnYAYGv8Cq6JjcBkxdHh60iderYxl26Zrk2JigfFBAQA8C58lOj+WzLcPrhzEnqR+phKNg9tq5pC20Mo8Rt0YwS9KGpognbfyuFY6z6NYi+U7ecSKcGjf+JfnVpWQjHh6DwexRJ9w49ra/YfggcXoSetRz+zE+/S53C0//KGH1eI7JTjSdsaNWrw/PPP8+9//5uoqKhwe2xsLImJaWveHDhwgNjY2JwOUQhxGZYKXTHfvwXv0ucIbB8HQHDfLJzja2Jv/QmWW/pnazF5IYQQQgghMpO2nq0kbS/maD86w3a1YEWiey5K124q1TI00ZbmQzswD2q9FRqYoar41ryH74+XgdAEUo7OE1BUU7pjWGsNwlprUPafTC6in92DElUMVDPGuT0Y7iTcM+8lutfSdN+D9NPbwGTFM6M7hvMwpvja2Fp8iBoVj63Jq3gXPoYRcGFrPgItaSN68j7sLfLPKFsj4MI79wEwNABstw3DVLxxjhxbMVlxdJmIa2IDCLgIbP0ac7lOWKrcmyPHFyI75Hh5hKVLl7Jo0SKqVavG999/H25/7bXXGDRoEG3btqV///40bNiQ0aNHM3z48JwOUQhxBYq9MI5OY3B0m44SVTzU6DuLd+4APL/eg+46FtkAhRBCCCHETSFczxZQZRKy66JYokKJW8BwHkJJ3g1A8PDv+Fa8er4Xjs4TUKOLRyjKXCB1MrfQiNgiAOhHfse/6bMM+mqox5Zja/s50f3Wo0SXxLvoCQBMcdWJ7rWUmL5rMVfshnf5i9hbjSSwYxLumT3xzHsIw3smJ88s23mXvYB+dhcApuK3YW38So4e31S4KvbWo8LLngWPoqcczNEYhLgeOZ60bdiwIX/88QfvvvsuL730Ek2bNmXNmjX06NGDzZs307ZtWwoUKEC3bt3YtGkT99+feUFvIURkWSp1J/r+LZir9Q23BffOwDW+JoEdk6TWrRBCCCGEuKEuTDCkYCpaJ6Kx5Afmch3Cr03HlmB4TuL5rU94pKT1tlcwl20XqfByhfOTuVnrPIG93Rfhdt/vL6VLCCqxZdCL3Y4aUwpFUbBU7492bFW6ffo3jMRSpSeKrRC+1f/B0fUHTKVa4Nsw8kafzg0T2DeLwJbUcgTmKOydxqGoOf6wN5YaAzFXuS+04DuDZ84DGLqW43EIcS0iNhFZ//792blzJ23btqV169YMHDiQggULMmzYMD777DNef/11ataUmT+FyO1URxGiukzAcecUlKgEAAzvaTxz+uGZ1QvdnRThCIUQQgghRH5kaAH0U6FaomrhqijWmAhHlPeZy3UKv7Zsfh/PjG4YzsMAmEq3xnbb65EKLde4eDI3S5V7MVe8O7Qi6AqNjk2tsQxgrtIL9fRGDF9yqMu+2el+XNDP7SN4YAGWWoNBD4AeBJTQhG55dDI33X0C7/xHwsv2lh9hKlwlIrEoioKj3eehZDugHV6Kf+17EYlFiKsVsaQtQFRUFG+//TZbt24lOTmZqlWr8u677+L3+yMZlhDiGlgq30P0/VsxV/1HuC24e2po1O3fP4fbvEueJuWbCiSPVNGSNobbjaAPz+KncI6pinN8bTxzMh5lr55ci3tiA5wT6uEcVxPPgsEYwdCECNrxtantt+LfNvZCHAcX4VkwOJvPOHKu9xoGDy7CNq8rrvG1cI6riXf5kPDNpX5uH85JTXCOq4lv9TvhbbTT23HPuOuGnpcQQgghxNXQT28DLfTdUUojZA+1SE2IKgGAornRj68JvXbEh+vY3kw8CwaT8nUZDOch3NM7k/JdKPHoaPc5/nUf4hxfG/3cLrDHAaAdXIj7x+YE9swAQI0tS6D603h+boFzQh2CBxdjb/d5mmN4lz6LvdV/URQFxVYQc7U+uCbUxr/pM6x1nsrZE84GhmGEJq52HwfAXOFOLLUejWhMir0wjs7jQ4lwQhO+BY+mH/EsRG6T82PTgVWrVvHrr7/idru57bbb6N27N1OnTmXRokU8++yzfPXVV3z44Yf06NEjEuEJIa6R6ihKVNdJBCr3wLv4SQzPydAjVbP/QWDXFOxt/4e5ck+sDYbg+qlFmm19f7wEKEQP2ImiKJnWxdUL1cDxj1WYLDYMQ8czsyf+zZ9hq/8cvjXvY2/9CaZiDXCOr421xgCMoAffqjeI6vZLDlyBnHG911CxFcbf7HMKVmyEovtxT+1AYNs4rLcOxL/pU6x1nsBySz9c42/FWvefYInBu+Q5HJfcYAohhBBCRNKF0ghgiq8buUDyEUVRMBWtiXbgaJp2R+fxqDElIxRV5GR1Mrdg4nzc00KjlLWTm1ALVwuv0yr0JOq2J1DVjMfMRXVP+z3F3nQ4NB1+fYFHUGDbdwT3hs5JcRTF3v6rXDFRtblUC6yNX8H/51tgaHh+60dMvw0otgKRDk2ITOX4SNtvv/2W5s2bs2LFCg4cOMDDDz/MP/4RGpnXtm1bNm7cyPPPP8+jjz5K27Zt2bJlS06HKIS4TpaqvUKjbitfmJkzuOsnXONrYnhPosaWTtPfCLjw//Ut9mZvh/9Bz3RyA3MUiskSeq35MYIeIPUmQLVA0I0R9IZHAfhWDk+dJKBQdp5iRJlLt7yua2hKqIcRUw4AxWxHja+Dnrw/tDL1GqIHwNBBUQls/gJzuQ6oBSvcsHMSQgghhLha+okLk5CZZKRttjAMA/3cnjRtSnQJTGU7ZLKFgFAtYEudJ0MLQQ+euQMw9GBkg4oA/dxevEueDS/b23+FGl0scgFdwnbbq5hKNAPASN6Hd3HeG8ksbi45nrR9++23eeqpp1i8eDFTpkxh6tSpTJ48mb1794YCUlWeeuopdu3aRfXq1WncuHFOhyiEyAZqVAKOO37C0WUiSurjQoY7Cc/Mnrh/6xdKCKbSz+5BscXhW/MOzomNcP3UkuCBhZnuW0/ej3NCXVJGx6NYC2KtE5qB1dbkVXxr3sU9rRO25iPQkjaiJ+/DUuXeTPeVX1ztNQxv5zpGcNcULBXvBMBa72kCu6fj+rEZ1vovYPjOEdg9BWu9Z2/wGQghhBBCXJ00I20T6kUwkvxDS5yHcW5vmjbDdRQtcV6EIso77M3fRy0UKp+gH1+Nf+37EY4oZxl6EM+cB8J1eC23PoSlUu4qr6aoZhydJ4A1NLo2sGMC/u0TIhyVEJnL8aTtmTNnqFLlQgHqSpUqYRgGZ8+eTdOvcOHCfPrpp6xZsyaHIxRCZBdFUbBU6x0adVvxwj/YwZ2TMFyHCR5aEmowghgpiahxNYjpuwZ760/wzO6N7jqe4X7VAuWJ6b+R2EFHQfMR3D0VAFNcdaJ7LSWm71rMFbvhXf4i9lYjCeyYhHtmz9DEAN4zN/q0I+MqryGA4UvGPaM71ob/wlSsIQBqdAmie8whpt96rLUHh2pstfgQ7dBi3KkJdz05MafOSgghhBAiQ4aho53YCIASWy48SEBcO8Mw8K58FZRL6tYqJrwrX8UwjMgElkcolijsncZeqJu66g38m7/APrsVwQMLIhzdjedfOwLt6AoAlIIVsbf6b4QjyphasDyOdl+El72Ln0Q/u+cyWwgROTmetO3SpQvvvfce33//PbNmzeLRRx+lTJky1KxZM8P+mbULIfIONbo4jm5TsXcaB7bCoUZDx7fseTxzB4ClACgqllv6AaGREkrBCuHZgDOjWGOwVPsHgR0T063zbxiJpUpPFFshfKv/g6PrD5hKtcC3YWR2n16uoMSWvbprGHDi+aUrlordsdV/PuMuu6agFqyIKaEu3sVP4+j4Hdaaj+BbKbMGCyGEECKy9DO7IOACpDRCdtES56EfXwuGlnaFoaEfXyujbbPAXKIJ1kYvhRb0IP7lL6Im78K/Yli+Tnprx9fhWzU8tKCoODqNQ7HGRjSmy7FU642l+oDQgj8Fz5z+GFogskEJkYEcT9p+9tlndOrUiRdffJF+/fphMpmYNWsWVqs1p0MRQuQgRVGwVu9PzP1bMFe4M9we2D4e9+RWqEVrE0ycC4B+bh/GuX2ocdXT7ydlX/gfVEPzE9g9HbVorTR99HP7CB5YgKXW4FBtVj0IKKFfvVMf18lvVEdRTGXaZekaGn4ntqV9MZfrhO22YRnuz/Cexb9xFLYmoQStEXSHrp+iYuTTayiEEEKIvEM/cXFphLqRCySfCI+yzTRFoMpo2yyy3fYa6vmJ8TQvAHpS/k16GwE3njn3p37nAmujlzCXbBbhqK7M3mYUaqHKAGjH/sT35xsRjkiI9Mw5fcCCBQvy9ddf5/RhhRC5hG/VG2gnNoQSgIYBGBiuoxiuo3jnP4LPXhhUC/Z2X6DGlALAM/8RzBW7Y6pwJ+rxP/CsfBRUE+hBzGXbYrvt1TTH8C59Fnur/4Ym5LIVxFytD64JtcESQ1TXHyJw1tnLs2Awwf2zMVzHcE/vDJZYYh/chaPd53jmP4Lv95dAUTO8hpZK3QlsGoV6aiNBAjj3TAfAUrUntsavhI/h/X0otiavo5gdANgav4JrYiMwWXF0kM9wIYQQQkSWlnRhEjJVRtpeP82PkXIQ0DPpoGOkHALND2ZbTkaW5ygmK/aOY3F/Xxc4n+RW8P4+lOhyHcOTBucXvj9eQj+zAwA1oQG22/LGU3mKNRZH5+9x/XR7aFT06ncxl2mPuUzrSIcmRFiOJ22FEDc3R/vR4dd6yiE8Cx5FS5wDgOE+BqoZR/uvMJfvdGGb1CShrutolfsT1ex5VDXzBwWiuv+SZtnedDg0HZ59JxFhF1/Di6kFKxLdc1HG21yUaLU2epmz5R4hISEh0+t46TGstQZhrTXoGiMWQgghhMheaSchk6Tt9VLMNqL7rMbwnABC992nT58mLi4ufL+oOBJQJGGbNa4jXEjYAhjoJzfjnf8I9nafo5jyx5PGwcR5+Df+L7RgsuPoPA7FZIlsUFfBVLwRtqZv4fvj34CBZ+79xPTfJDWyRa6Ro+URBg8ezL59+656uz179jB48OAbEJEQIpLU2NJE3T0Le/uvILXmkeE8hHt6FzwLBmH4kiMcoRBCCCGEyG0MwwiXR1CiiqFGl4hwRPmDGlsGU0L98B8jrnaaZTW2dKRDzBMyndANCGz7Duf42gQPLIxAZNlL95zCM+/B8LK9xQhMGZRmy+2sDf+FqUxbAAznYTwLHpUyICLXyNGk7cGDB6lWrRpdunRhzJgxHDx4MNO++/fv5+uvv6Zjx47ccsstHDp0KAcjFULkFEVRsNZ8mJj+WzCV7RBuD2z9BueEWjfFTKtCCCGEECLrjJQDGN7TgIyyFblPphO6pTLO/o17agfcs3qjp+TNPIdhGHgXPY7hOgqAqVwnLHWejHBU10ZRVBydxqLYiwAQ3D2VwFYpBydyhxxN2s6ePZvFixdjt9t59NFHKV++PAkJCTRu3JhOnTrRsWNHGjVqRNGiRalUqRJPPPEEUVFRLF68mFmzZuVkqEKIHKYWKEvUPXOwt/sCLDEAGCkHcU/tiGfh4xj+FIIHFmCf3UoSuddJrqMQQggh8rKLSyOEJ3wSIhe48oRuFwR3/YRzXHV8az/A0Pw3PrhsFNgxgeCuyQAo9jgcHb7J07V61ZhS2C8qJ+dd+iza6e0RjEiIkBxN2gLcfvvtTJs2jcOHD/P1119z9913Y7fbOXToEIcPH8bhcNCjRw++/vprDh06xPTp02nevHlOhymEiABFUbDWepSY/pvDj6gABLaMJmV8bXxLnkZN3oV/xTB5ZOUaGYaBf8UwuY5CCCGEyLO0ExcmIZORtiJXueKEboC1AKSO6iTgwvf7UFzf1yN4MOO5KXIbPTkR7+J/hpdDkx+XjGBE2cNS6S4stR8LLQQ9eGb3xQj6IhuUuOlFbCKy+Ph4HnzwQR588MErdxZC3FTUguWJ6jGPwOYv8C4fAkE3pCSGS/nrSWvREuelmaxMZE1w/xz0pLWAXEchhBBC5E26TEImcqksT+hmica7YhiBzV8ABvrp7bintMdctTf2lh+gxpSK4FlkztA1PHMHgD8094il+gNYqvSMcFTZx97iQ7RDy9BPb0M/uQnfH//G3urjSIclbmI5PtJWCCGyQlFUrHWeIOb+zaglW6Rb710+REaJXiXt7D48s3pd1KLgWfwUesATsZiEEEIIIa5WuDyCrRBKgfIRjUWIS2VlQjfFXhhH20+J7rMaU/HbwtsG//4B59jq+NZ9hKEFIngWGfOv/xjt8DIAlNhy2Ft/EuGIspdiicLRZSKYbAD4N4wkuH9OhKMSNzNJ2gohcjW1YEVsjV9K166f2oJv5fCcDyiPCuyZgev72qFRy2EGxrk9OEfH45n3EMHE+Rh6MFuO513yNCnfVCB5pIqWtDHcnvJNBZxjb8E5oR7OCfUI7Pwxw+3V4ytwfhoT7uecUA8jGEoua8fXhtrG3Yp/29jwNsGDi/AsGJwt8QshhBAid9JdxzFcRwAwxdfL03U0hTAVa0DUP/7A3v6r8ERYBJz4lv8rtWTCkkiGl4Z2YhO+FcNSl5TQ5F22ghGN6UYwxdfG3mJEeNkzbyC663gEIxI3s4iVRxBCiKwwDAPfytdBMaWbgdW/+i2MgBN78/dQTJYIRZi7GVoA3x//xr/+Mo/1BN0Eto0hsG0MSlQxLFXvw1ytL6bija/5i5C5ck+sDYbg+in9KGlHlx8wJdS94j7UwtWI6b8hXbtvzfvYW3+CqVgDnONrY60xACPowbfqDaK6/XJN8QohhBAib9BPXFwaoW7kAhEimyiKirXmw1gq34P3j1cIbPmSUMmEbbintMVcrS/2FiMiWjfWCHrxzLkf9NDoX2uDFzGXbhmxeG4E75KnCez5FSMlkai+6zCX70pw/2wMdxLO7yqjFqoMgK3RS1iq/SPd9urxFTh/7o9auFq4Lbr3ChSzA+34WjzzB4Hux9pwCNYaA4DQoJPAzh9xtB+dMycp8hwZaSuEyNW0xHnox9emS9ieF9jwX9xT2qI7D+dwZLmfnnIQ9+TWl0/YXsJwH8e/8f9w/9gU55gqeFe8ek0zp5pLt0SNLX3V22WJaoGgGyPoRVFNAPhWDsda92kUe6Ebc0whhBBC5ApakkxCJvInxR6Ho93nRPf5E7VYo3B7cOdEnOOq41v/ccRKJvhWvIJ+aisAatE62Jq+GZE4biRz5Z5E37ccJbYcCgr2jt+iRBULrQy6sNw6kJj+GzJM2J53ftDJ+T+K2QFcGHQS3Wc1vlWha3d+0Im9+fs3/NxE3iVJWyFErmUYBt6Vr3KljyrtyB+4vq9P8MCCnAksDwjs+w3X9/XRjq68qDWzUbMqSsFKmCrdE67fBGCc24t/9du4xt2K8/v6+NZ+gJ5y8Lpj88wbgHN8bTzzH0Z3n8i0n35uD87vG+Cc1Bj/ps/C7bYmr+Jb8y7uaZ2wNR+BlrQRPXkflir3XndsQgghhMjdtIsmIVMlaSvyIVOxhkT3Xom93WgUe1yo0Z+Cb9mLuCY2IHhoWZr+11uW7DzDMHBNbkfyZ4XDbfq5fTjH1sC//r+pwdlwdB6PnrwX94y7suV8c4tLB52oUQk4Ol0oxeb7fSjaiU3XtnMZdCKukZRHEELkXpofI+UgoGfeR1HB0DE8J3BP7YSt6XCsjV9BUW7O36QMPYhvxav41170i21MWQg6wXs6k610CLiI6jIRgh4Ce6YR2DkJ7eAiMELXXj+xEd+Jjfh+H4qpVAss1fpgrtIT1VH0quKL7rUUtUDZUNmGFcPwzhtI1N2z0kcUV4vohxIxOQqjpxzC/csdKI6iWKrehymuOtG9lqaer4Z7WiccncYR2DGJwO4pKNYC2Ft+hGIvnG6/QgghhMjbtPPlEcxRqIWqRjYYIW4QRVGx1hqEuXIPfH+8TGDr14CBfmor7smtsdzSH1uL91GjS2RLWTKAwIaRqIUqphnN7lv/Mbr7Qj1X622voRa5Ffe0LjjafX69p5nrmct1BGsB8CeD5sf1c2ui+2/EVKBchv3PDzpBNWGtMRBrnSeA0KAT78LHMAKuNINO7C1klK24vFyRtF21ahWLFy8mKSmJJ554gipVquB2u9mxYwdVq1YlJiYm0iEKISJAMduI7rMawxMajanrOqdPnyYuLg5VDSVlDcWC/4+XCO7/DQjVvw0eWYGj8/irTijmdbrzCJ7f+oZndAUwV+yOo+N3GAHnZa+j4khAMdvAbMN664NYb30Q3XWUwN8/Edz5A9qxP8P71A4vRzu8HJY8jblcp1ACt2J3FOuVP6vVAmVDxzNZsNZ/FueYahl3tMSGJzZQY0tjqdqb4OHlWKrel6abf8NILFV6otgK4Vv9H6L7byKwfTy+DSOxN30j6xdPCCGEELme4T2LcW4vAKb4OuERa0LkV6qjCI72o7HWfBjP4qdCZeOAwI4JBPb+gq3JG1jrPoWiXl9qRzm3k+DeX4jq+B2BvyeH24MHF4PvTGjB7MBa9ykCm7/AXK4DasEK13XMvCK6zxo8s3uH6mn7z+H+qSWxjySm6yeDTsSNENGhaH6/nx49enD77bfzyiuvMGrUKA4eDD16q6oqHTt25JNPPolkiEKICFNjy2BKqB/+Y8TVTrNsjq+F465fsTV7KzTqFtAS54YeHTq6KsLR55xg4nxc39e7kLBVzdhafICj2zQUe+ErXseM6s+q0SWw1XuG6N4riRn4N7amb6IWvuVCBz1IcN8sPHP6k/Jlcdy/9SOwdyaG5s8wRiPgwvCeDS8Hdk7ClFAv4xPyHMdIHeVr+FMI7puVrq9+bh/BAwuw1BocmhRBDwJK6O9BwJnlayeEEEKIvEE7sTH8Wo2vG7E4hMhppuKNif7HSuxtPwdbamLPn4Jv2fOh7z2Hl2e4XVbKkhlaAOvqF0P7Vi78EBLY+QPG6W2hBcWE7bZXwZ9CYPcUrPWezc7Ty9VMhavg6DIRzFEAGM6DBHZPS98xk0Enl7p00Imj6w+YSrXAt2HkjTwNkUdFNGn76quvMnPmTD7//HN27tyJYRjhdXa7nV69evHLLzITuBDi8hRFxdb4FaLumYsSlQCAkXIQ98+t8G0YleazJb8xdA3vytdxT+scHkmrxJQmqudSbA1eQFEyq2N7ddRClbHdNozoB/4iuu96rA1eRIm5KNEbdBPcOQnPjO44vyqJc1wNUkYnYDgP4Z7emZTvqmC4j+Oa0hbnhDo4x9dGO7QMR8cLdaI88x8hsGcGAOaDs/B8XxfnhLq4fmiKqWx7LDUeTBOTd+mz2Fv9F0VRUGwFMVfrg2tCbfybPsNa56lsOW8hhBBC5B7aCZmETNy8FNWEtfZgYgbuxFLz4XC7fnIL7p9bYXhPontPhtujey0lpv8movuuQ7EXxTtvYIb79a9+E61MV9S46hf2mXIIz6InwsuOTuOwNXopdP/d4kO0Q4txz+yJ+7d+6MnpR53mF+cHnZjiqmFvPTLc7l0wCD3lUNrOMuhE3AARLY8wadIkHn/8cR599FFOnTqVbn316tX5+eefIxCZECIvMpdtR3Tf9Xhm90Y78jvoAXxLn0U78geO9l+h2ApEOsRspbuO4ZnTP1R7NpW5fBfsncbesNIQiqJgSqiLKaEutubvoR3+ncDOiQR3TcZIrZlreE+HXysxpbFU+weWan1RClQgpt/6TPft6PB16Lx0nWDVh4hq/lK4fENGorqn/VHP3nQ4NB1+fScohBBCiFxLv2gSMknaipuV6iiKo/1XWG59BO/ip9CT1oVWBFx4ZtyDfvvbWOs8keWyZNrhZZjP7se1ZxwYQfAn4xxTFTQvAOaqvbHc0ofArimoBStiSqiLc2wNovv8iXZ8Lb6Vr+PoNCYnTv2G8iwYTHD/bAzXMdzTO4Mllugec3HP7AmGBoaBEl0Cw3UUw3saz9wBqAXKY650F6YKd4YGnSzvB6oZ9CDmKj0vO+iEiwadYIkhqusPETpzkZtFNGmblJRErVq1Ml1vMplwu905GJEQIq9TY0oSde9CfCtewb/uQwCCu37GdWIjjjsnYyqa+WdOXhI8uBjPb/0w3MdCDYoJW7O3sDYckmOTsCmKirl0S8ylW2K0HkXwwHwCOyYR3DMdgqHPbsN5CP+6j/Cv+wi18C1YbumDpVof1EKVcyRGIYQQQuQf2vmkrWpBLXJrZIMRIsLMJW4juvcqAlu/wvvHK6Has0EXvqXP4t/6NfYWI7CU7wxcvixZVM+lJCUlkZCQACkHcI6rEU7YKjGlcbT9FMN7Fv/GUUTdMwcAI+gOjQ5VVIx8MkLU0X50hu0XDzoxvGdwfl8XI+Ug2qHFmJu9jaVSdxl0Im6YiJZHKFOmDDt27Mh0/R9//EHlyvLFXghxdRSTBXuLETi6TQNrqK6QfnYXrh+a4N829gpb526GoeP78z+4p3YIJ2yV6BJE3bsQW6OXcixheynFZMVS4Q6iukwgdvBxHJ2/x1zhztAvzan0MzvwrXwd55iqOCc1wbfhE3TX0XT7Ch5YgH12K4IHFuTkKQghhBAiFzMCbvQzoe+OapGaKCZrhCMSIvIU1YSWtAHFbAculEUzTm3FM70ryZ8XwTm2xmXLkl1MO/t3OGEL4Oj4HYq9MN7fh2Jr8jqK2QGArfEruCY2wrvkGWyNXrpxJ5jLKPbCODqN5/y19q18jeDRPy+/kRDXIaJJ2759+zJ69GhWrlwZbjtff/Grr77ip59+4oEHHohUeEKIPM5S6S5i+q1DjU/9VTnowTvvQTzzH8EIeiIb3DXQ3SdwT+uKb+VrkFovyVS2A9H9NmAu3TLC0V2gWKKx3NKHqLtmEDPoKPa2n2MqlTY+/fhqfEufw/l1GVxTO+L/67vQjNCGgX/FMNTkXfhXDMvX9YiFEEIIkXXayc0X7n8ym8hUiJuQo/1oYgcdpsCzGlH3/XHhuw+A7wy66yjmsu1RLpp42NHhayyVuqfZj6H58f1+IQFrrfcM5rLtwscwl2l7YV2tQcQM2EZM/42YijW8QWeWO5lLt8Ta+OXQgqHhmdMPw58S2aBEvhXRpO0rr7xCs2bNaNmyJW3atEFRFJ577jnKli3L4MGD6dy5M88991wkQxRC5HFqwYpE/+MPLDUHhdsCf32L64dm6Gd3RzCyqxM8/DuuifXRDsxLbVGwNX2DqLtno6ZOvpYbqY4iWGsPJrrXEmIeTsTW/P20sz0bOtqBBXjnP0zKV8Vx/dQCPWktAHrSWrTEeRnvWAghhBA3FT3p4knIJGkrREbMJZsS3Wc19jb/B7ZCoUb/ObxLnsY1qTHBIysy3da/6nX0ExsBUIvciu32d298wHmUrcnrmEo0BcA4txfPrF7ypKC4ISKatLVarcyZM4fvvvuOihUrcsstt+Dz+ahduzZjxozh119/xWQyRTJEIUQ+oJjtONqPxt5pLJijANBPbsI5sSGB3dMiHN3lGYaOb+0I3JPbYDgPA6BEFSPq3vnYbnsVRc07n5FqbBlsDf9FTL/1RN+/FWvjV1AKVrrQQfOjH734RlLBu+xFGW0rhBBCCLSLkrZqvExCJkRmFNWEtc6TxAzYgaXGwHC7fmIj7p+a45n3ELo7Kc02atKfBFLnA0G14Og0PrXkgsiIoppxdJ4A1lgA9IML5ElBcUNENGkLoXII/fv3Z/r06fz1119s376dmTNn8sADD4RLJQghRHawVr+f6N6rUAunzpzqT8Yz8168y17A0AKRDS4DuucUnhl3hR5TMjQATKVbh8ohXPR4Ul5kKlIDe7O3iBn4N9G9V2Gt+/SF0QBhBvrpv3CNq4H/rzEYAZmYUgghhLhZaakjAEHBFF87kqEIkSeoUQk4On5L1H2/oxatE24PbBuDc+wt+Dd9hqFrBPfMwLa4FxBKNtqavokpoW5kgs5D1IIVcLT9PE2bPCkosltEk7YVK1Zkxoz0xa/PmzlzJhUrVszBiIQQ+Z2paE2i+6zGXPUf4Tb/+v/intIWPXUka24QPLoK18QGBPfNSm1RsN42jKge81Gji0c0tuykKAqm4o2xtfovasFKoVloL6Gf2Yl3/kOkfF0a75Jn0U5vj0CkQgghhIgUQ/Ojn9wCgBp3C4olOsIRCZF3mEs2I7rvGuytR4UnacZ3Fu/ip3BObIR3/oMoRhAAtWRzrA1ejGC0eYu5Wh+wF0nT5ln4GLquRygikd9ENGm7f/9+nE5npuudTieJiYk5GJEQ4magWGNxdJkYqvWkWgDQjvyB6/v6uH+9h5RvKpA8UkVL2phuW/9f35E8UiWwe3qG+w4mzsU+pz3uiQ1wTqhHylelcH7fAADDewbX5LY4x9fGs+iJ8Da6+wSun9tgaAEMw8C3/r+4f26JkXIgFK+jKFH3/Ia96Zt5qhzC1dAS56EnrQtPMJIh31n8G0fhGncrrp9bE9gxCSPou+Zjepc8fV3vtXp0Sfh9vpb3WgghhBBZo5/aBnro305TgpRGEOJqKaoZa92nQiUTqg8ItxsnN4L/XHjZWvORfPt940bQEueB91SaNiMlEfcPt+WqAUEi78oV5REys2bNGgoVKpRzwQghbhqKomCt8yTR9y1HiS0LgOE5QXDPL1gq90CJLZduG/3cfgJbv8ZUvEmm+zWX64S38wKi+q4jpv8GTAn1sdzSF4DAju8xl25NzP2b0U/vRDu5FQDfshewNX8XAi48M3viW/YC6KFfu00lm4fKIZTrmN2XINcwDAPvylfJ/J8kJfQLtmoLt2iHl+GZ0w/nN2XwLh9yTZPKmSv3TH3/r+291ku0Dr/PV/teKybLVccrhBBC3Ky0ExfXs60buUCEyOPU6GI4On1HVK9lKEVqXbJWwb/pf1KTNYvC32GU9EluPWkdznE18W+fINdTXJccT9p+8sknVKxYkYoVK6IoCs8++2x4+eI/RYoUYeTIkXTt2jWnQxRC3ERMxRsT3Xcd5vIXPmv8G/6L4T6O7jsTbjMMHc+CQaHHiky2jHaVju48QvDAQizV7w81qBaMoBvD0EHzgclKcP8cFHshFNWMc2IDgnsuTIxmbfAvou5diBpTKntONrfS/BgpB4HMRtkaKKqFmIf3hcooFL7lwhrPSfzrPsQ5piquqR0J7JqS5VGs5tItUWNLpz/aDX6vzSUyTwQLIYQQIr2LJyGTkbZCXD9zqebYm797SauBflxqsmaVljgP/fja8Nwj6fjP4Z37AJ5ZvdDdJ3I2OJFvmHP6gAkJCdx6661AqDxCqVKlKFUqbUJCURSio6Np0KABTzzxREa7EUKIbKM6iuC4awb+Ne/hW/la6BF9zYtndh+UbtMwl2yKf/3HmEo2w1SsQZb3G9g2BnOFrqhRCQBYqvfHM3cgru/rY650F2pMKdzzH8Fc6S5cPzUHzR/a0FYYR6exWCreeSNON9dRzDai+6zG8IRuZnRd5/Tp08TFxaGqod8WFUcCanRxbPWewVr3abTDy/FvGU1w1+Tw45LagQV4DixAiSqOpebDWGs+glog/SjaK7mR73XU3bOusCchhBBCXEq/qIyRSUbaCnHdDMPAt2p4aJToxUlHxYR35atEl+soE8NfRtonBS9fvza4eyquw8uxtxuNpfLdORGeyEdyPGnbp08f+vTpA0CbNm0YNmwY7dq1y9ZjLFu2jA8++IB169Zx9OhRpk2bxt133x1eP3DgQMaOHZtmm06dOjFnzpxsjUMIkXcoioqt8cuYSjTF81sfDHcSeJJwT26Fpf7zaIeWEd1raZb3ZxgG/r++w976kwvHsEQTdefP4WXPoidDN0zL/xVuU4vUIuquX1ELlM2eE8sj1NgyEFsGAEXXMUjClJAQTtpeTFEUzKVbYi7dEr3VfwlsG4N/y1cY5/YAYLiP4V/9Nv7V72Cu0BVLrcGYy3fJUn0u7eRWgrumEpXN77V36XNYG4bKOPjWhEY12Bq/gim+Trr9CSGEEOICQ9fQTmwEQClQAcVeOLIBCZEPhEeJXsrQwqNtzeU75XxgecUVnxQEbIVCSXHvKQzPCTwzexCsfj/2Vp+g2AvlUKAir8vxpO3FFi9efEP263K5qFOnDg899BA9evTIsE/nzp357rvvwss2W9YegRVC5G/mMm2I7rse53eVQo+160ECa0eAyY5zTGVAxXAfw7twG4brKNY6j2e4H+3wUtC8mMtlfLPj3zaBwPbxELgwGaOlxoPoZ3fddAnb66FGJWBrOARrgxfRDiwMjb7d80vqiAGD4L5ZBPfNQoktg7XmI1hufRg1pmSm+9MOL0dP3o9zTFWArL3Xhy7/XmvHVqO7T2CveCeun1ri6DQOMPDMe5DoXkuu8woIIYQQ+Zt+dhcE3YCURhAiO1x5lKgqo22vIKtPCqJa8C58jODeXwAIbB9P8OBiHB2+wVyuQ8TiF3lHRJO25wUCAXbs2MG5c+fQ9fQfGi1btryq/XXp0oUuXbpcto/NZqN48eJXtV8hxM1BjSmJElUcU+lWBLePCzVqXhSTHccdP+Nd8gzWes9c9vGW4F/fYakxIN3oTsMw8G/+At/ip4DUovSKiq3NZ1jKd8I9894bc1L5nKKomMt1wFyuA7rzCIG/vsW/9avUX8DBSDmIb+Xr+Fa9ibnSXVhrPYqpbHsUJe1IXmudx9MkZ10/t7niex3469sM32sAQwvg/f0lHF0mhZYDLlAUQMG4KGEvhBBCiIzpaerZ1o1cIELkF1ccJapjpBwKlW4zy+C2zGT1SUFHt6kEto/Hu+Rp8CdjOA/hntYJS+3HsDcfgWKNiUT4Io+IaNJW13X+/e9/89lnn+F2uzPtp2mZFHa+DkuWLCEhIYHChQvTtm1b/vOf/1CkSJFsP44QIm/xLBhMcP9sDNcxtMQ5KFHFMYIe8J9DP7sL1w9NUC6pk+pd+RpqdEmstR8LNfiTCe6ZRsz9m9P0M/xOPAsfI7hzYrhNTWiAtfZg/OtHEtg4CluLETf8HPM7NaYkttuGYW30b4KJcwhsHk1w3yzAAEMjuHsqwd1TwRoLKBBw4Z7eGSyxxD6467L7Pv9em2s+CoDhO0dg99R07/V5/nUfYKl+P2p0MQBsTd/APf2O0Gt5r4UQQogrungSMjVeRtoKcb2yOkpUkYRttlAUBWuNBzCXaYNn/sNoBxYAENj8BcHE+Tg6foe5VPMIRylyq4gmbd955x0++OADBg8eTPPmzbn//vt5//33KVSoEJ999hmKojBiRPZ/qe3cuTM9evSgQoUK7Nmzh5dffpkuXbqwcuVKTKaMax76fD58Pl94OTk5GQh9wGU0Ojg/0XUdwzBy9Xnm9hhze3wgMZ5na/s5l96e6Of24p3dG/3EetC8GGd2Etj7K2rZDihmB9bbhofj03UdwxJL1GNnQVXDsWqntuKd3RvjzI7wfi21n8TafASK2Ya5xkNpzvNGyu3vdfbFp2Aq1wVTuS7oKQcI/PUNwb++xXAdDa32p4T+q1owlW6DpeajaJqW5jEwx70LwzEBad5rwzAwLLHEPJGcps/FLA1fSrPOVL4rUeW7pjnXGyW3v88gMWaH3B4fSIzZRWK8frk9PpAYM6IlbQi/VorWydJxc/t1zO3xgcSYXXJtjNGlUKJDE8Iruo5unEApGo9y0SjR3BJzrr2GF8lSjNGlsN/1G8EtX+D7fSgE3Rjn9uD+OTSHirXJGyhme2RjjLC8EGN2yeo5RjRpO2bMGO677z4+//xzTp06BUCDBg1o27YtAwYMoGnTpixatIj27dtn63F79+4dfl2rVi1q165NpUqVWLJkSaaTor377ru88cYb6dpPnDiB1+vN1vhyG13XOXfuHIZhZDgpUG6Q22PM7fGBxHh5MdB6Mpb1r2PZMx6A4Lbv8B/5E9/tX2LEVrxsjKa9P2Bd9zKKFvqsMMwx+Bt/hLtsNzh9LgfPI/MYc5MbE58dKj4J5R/FdHg+5t3jMB1flnrAAMG/fyT494/oBSoTrHQ/wfI9wZb5RCe5/RqCxJhdcnuMuT0+kBizi8R4/XJ7fCAxpmMYOI6vRwF0ezFOulRwJeWuGK9Bbo8PJMbsIjFev9weH1xljMXvRelUH+ufz2A6uRYwCKz/CN+emfhu+wQjrnbkY4yQvBBjdklJSclSv4gmbQ8dOsSQIUOACxOBnU+AWq1W+vfvz8cff8w777xzQ+OoWLEiRYsWZffu3Zkmbf/973/z/PPPh5eTk5MpU6YM8fHxFChQ4IbGF2m6rqMoCvHx8bn2f5zcHmNujw8kxiy54zsCO9rjW/Q4BN2oZ7fhmN8Ve/tvMFe+J12MiubFt+SfBLePDe9CLVoXe9dJxBaqkvPxp4r4dbyCGx5f8YHQYCD62d0E/vqawF9jwHsSADV5N9YNr2Pd/C7mqvdhqTkItXiTdJMw5PZrCBJjdsntMeb2+EBizC4S4/XL7fGBxJjuWMn7cQdCP3BbijegQEJC1rbL5dcxt8cHEmN2kRivX26PD64hxoQEjAp/ENjwX/wrXwPdj3puJ44Fd2Jt9AqWhi+hmCyRjTEC8kKM2cVuz9qo6ogmbYsUKYLTGZqIJSYmhgIFCrB37940fc6cOXPD4zh06BCnTp2iRIkSmfax2WzhxPLFVFXN93+ZIFSHJbefa26PMbfHBxJjVthq3I+5WH08M3uhn9kB/mS8s3thrfcstubvox9ajGPRP9Ea/YvAxk/QT/0V3tZS61HsrUbe0MdesirS1/FKciI+Na4q5hYjsDd9i+Ceafg3j0Y7vDS0UvMS3D6O4PZxqEVrY601GMst/VBsoR/pggcW4Fj0T/S2/4e5fMcbFuP1yu3vM0iM2SG3xwcSY3aRGK9fbo8PJMaLaSc3hl+bEupd1fFy+3XM7fGBxJhdJMbrl9vjg2uIUVUxNRqCpUJXPHMHoJ/YAHoQ/59vENw/C0fHMZiK1IhsjBGQF2LMDlk9v4hehXr16rFmzZrwcps2bRg5ciR//PEHy5cvZ9SoUdSpU+eq9+t0Otm4cSMbN24EYN++fWzcuJEDBw7gdDr517/+xapVq9i/fz8LFy7krrvuonLlynTq1Cm7Tk0IkY+ZitxKdJ/VmKteKLXi3zAS18+t8S0fgpq8C9/Cxy4kbC3RODpPwNHui1yRsBVpKWYblmq9ie61mOgH/sJa7xmwFQqv109uxrv4SVK+LoVnwaMEj6/Dv2IYavIu/CuGYRhG5IIXQggh8rGLJyEzJcgkZEKI/MdUtCbRvVdivW0YKKE5lvTja3FNbIBv/ccYuhbhCEUkRTRp++ijj6aZ4Ovtt9/m7NmztGzZklatWpGcnMxHH3101ftdu3Yt9erVo169egA8//zz1KtXj9deew2TycTmzZvp3r07VatW5eGHH6ZBgwYsX748w5G0QgiREcUag6PL99jb/A/U0KMr+rGVGKc2p/YIFRZXi9Qkus8aLLf0jVCk4mqY4qpjb/VfYgcdxt7xO0zFm1xYGXAR2Po17kmN0JPWAqAnrUVLnHdDYvEueZqUbyqQPFJFS9qYbr3/r+9IHqkS2D09032oh+fjGn8rzjFVcf96L4YvddK0c/twTmqCc1xNfKsvlCDSTm/HPeOu7D6ViJLrKIQQeZd+0ee2Kb5e5AIRQogbSDFZsTd9k+h/rEAtfEuoUfPhW/Yi7ilt0c+Fnki/7vvagAvP9C6kfBFP8mdp5++Q+9rcKaJJ2+7duzN16tRwsrRGjRrs2bOHqVOnMmPGDHbt2kWTJk2usJf0WrduHZrV+5I/Y8aMweFwMHfuXJKSkvD7/ezfv58vv/ySYsWKZffpCSHyOUVRsNZ5guj7foeYsuk72IsQ9Y+VmOJuyfngxHVRzA6sNQYQ3XsF0f02YKn9GFhjM+zrnv0PfJtHo7uvPDHK1TBX7kn0fctRYsulW6ef209g69dpk8qXMPxObKtfwHHHFGIG/o0SUwLf6rcA8G/6NPR3t/8mAtvHYfhTMAwD75LnsLcameUY80JCNC9cRyGEEBkLj7S1FUYpkP5zXAgh8hNT8UZE91uHtf5zQGhODe3wcpwT6uDfPBpT5Xuv674W1Yylwb+I6jE/3Sq5r82dcl2RiIIFC3LXXXdx5513EhcXx7JlyyIdkhBCXJapeCMcrT5Mv8J7Cv3I7zkfkMhWpvg6ONp+Ruwjh7HUfTp9B38yvkWP4/yqJK7JbfFv/B+68/B1H9dcuiVqbOl07Yah41kwCHvrUWDK/AkRLXEOeuGaqKk/GlhrP0Fg5w+hlaoFgm7QA2DooKgENn+BuVwH1IIVsh5jHkiI5oXrKIQQIj3ddRTDfQwIlUa4dFJQIYTIjxSzA3vLj4jquQilQPlQY8CFd9Hj+Ne8Dxl8Fmb1vhaTDXOZtigXlYILk/vaXCnXJW3PmzFjBrfffjtt2rSJdChCCHFZhmHgWzsiXIMoTDHhXfmq1DzNLyzRaEdXpH+fzzN0tENL8C55GufXZXD9eDu+9R+jJydmaxj+9R9jKtkMU7EGl+2npxxAj76QrFQLlMdwHcXQg1jrPU1g93RcPzbDWv8FDN85ArunYK337FXFkpcTornpOgohhEhPS9oQfm1KqBu5QIQQIgLMpVsR038TlpqDwm1a4hyc42tBwIXBhe+YWb2vvRy5r82dzJE46Pz58/nkk0/Ys2cPhQsXplevXjz33HMATJ8+nWHDhrF9+3aKFCnC66+/HokQhRAiy7TEeejH16ZfYWjox0M1T83lZaLDvC7T9zmVEl0Sw3XkQv+jK9GOrsS37EXUhAZYqtyLufK9mApXufYYTm4luGsqUb2WXvM+ANToEkT3mBNeds+6D3uLD9EOLca/+Qsw2bDf/g7qNT6Kml0JUc/cB/Fv+TLNjWPUPXOvKaaL5ZXrKIQQNzP94knI4mUSMiHEzUexxuJoPxpzpbvxLngEw3UUfGcxIDQBdpeJGO7jcl+bj+V40nb27Nl069YNwzAoWrQou3fv5s8//yQpKQm3283//d//UalSJT799FMGDhyI3S4zrQshci/DMPCufJXQgwt6Bj1UvCtfJbpcR3msLw/LyvusxJTEcfdsgrunEtw9Ff3U1vBaPWkdvqR1+P54GbVoLSyV78Vc5V7UuBpX9fdCO7wcPXk/zjFVQ3G5j+FduA3DdRRrncfTRhRbFnX37AsxJO9HiS6Boqb9pz+wawpqwYqYEuriHFuD6D5/oh1fi2/l6zg6jclybOEY80BCNC9cRyGEuNlpJzaGX6sJMgmZEOLmZanQBfP9W/AsfprgzokAaAcX4ppQC3Ole7J8X5tVcl+be+R40nbEiBGULFmS+fPnc8stt3Du3Dl69+7Nf//7XxRF4X//+x+DBw/GZMrk8VMhhMhNND9GykEyTuQB6Bgph0Dzg/ky9YVE7pbF99lUuBrmpsOh6XC00zsJ7p5CYPfUNKOF9JNb8J3cgm/VcNTC1TBXuRdL5XtR4+teMYFrrfN4mpsv189tsNZ7Bkvlu9P1NZXrhLroSfTTO1CL1sC/+TMsVf+Rpo/hPYt/4yii7gklR42gGxQVFBUj4MzKlUknLyRE88J1FEKIm114EjJLNGqha39KRQgh8gPFHkdUlwkEKt+NZ1ZvQMdwJxHYMhpL9QewtxqJYi902fvarJD72twlx5O2GzZsYOjQodxyS6h+XcGCBfnPf/5Do0aNeOONN3jiiSdyOiQhhLhmitlGdJ/VGJ4TAOi6zunTp4mLi0NVQ2XDFUcCiiRs87RreZ9NcdUwNX4ZW+OX0c/tI7B7CsFdU9GOrQr30c/sxL/6Hfyr30EpWBFL5R6hEgrFG+Nd+BjB/bMxXMdwT+8MllhiH9x12Ti9K19DjS6JtfZjKNZY/I0/RJ11L+hBTEVqYr8kwen9fSi2Jq+jmB0A2Bq/gmtiIzBZcXT4+pquVW5LiHoWDM6T11EIIW5mhvcMRvJ+AEzxdVFUGdAjhBDn72tRFFCtoYFBQGD7OIIHF+Ho+G26bc7f15prPgqA+/t6oe80/mRSvi6DuXQbHJ3HXegv97W5So4nbVNSUihXLu2jjOeXGzVqlNPhCCHEdVNjy0BsGQAUXccgCVNCQjiZJ/KH63mf1YIVsDV4EVuDF9FTDhFILaGgHV4OqZMIGOf24l/3If51H6LElMZSuQeOLhMxlWiW6ZfV6F6L0yzbm76ZZlkr1YnoevdnGqOj/eg0y9Zag7DWGpRh34zkhYTopeeYkUhfRyGEEGlpJy5MQqbG141cIEIIkYtcfM9pGAaB7ePwLnkG/MkYzkO4p3bEUvtxzOU6hPudv6/V9dATg1H9Nlz2+4vc1+YuEZmI7NLHP88vW63WSIQjcoGnn36aGTNmkJiYyIYNG6hbty4AHTt25NixY6iqSmxsLKNGjaJevYxrWm3fvp3evXtz/PhxAN5++2169OjB2rVreeSRR/D7/QwdOpQBAwYAsGjRIn788UdGj77yF3ohhMguamxpbPWexlbvaXTXMYJ7phPYPRXt4GIwNAAM5yH8G0fh3zgKJaoY5sr3YKl8L6bSrdKVDogkSYgKIYS4EbSLJyFLkEnIhBDiUoqiYK0xAHPpNnjmP4x2cCEAgc2fox2Yj73jGMwlm0U4SnG9IvLNb9y4caxadeHxUK/XG65nO3369DR9FUXhk08+yeEIRU7r2bMnQ4YMoXnz5mnaf/rpJwoVKgTAtGnTGDhwIJs2bUq3vdvtZuDAgYwfP56WLVuiaRqnT58G4L333mPUqFE0aNCAWrVqMWDAADweD8OHD2fGjBk3/NyEECIzanRxrLUfw1r7MXTPKYJ7fyG4ayrBA/NBDwBguI8T2PwFgc1foNiLYK50F+bK92Iu2w7FJD92CiGEyH+0pI3h16Z4mYRMCCEyoxYoS1SPuQQ2f453+RAIetDP7sb9c0us9V/A1vQNFLM90mGKaxSRpO28efOYN29euvZLE7YgSdubRcuWLTNsP5+wBTh37lymk/RMnDiRBg0ahJO+JpOJ+Ph4ACwWC263G6/XG57gbvjw4TzzzDNp9i+EEJGkOopgvfUhrLc+hOE9S2DfTIK7pxLcPwc0LwCG9xSBv74l8Ne3YC2IpWI3zFXuxVyuY7h8wHnBAwuwL/onwbb/h7V8x0icUr4g11EIIXJeeAJPkxW1SI3IBiOEELmcoqhY6zyJqWxHvPMGoh1dCYaOf90HBPfPxtFpHJr7hNzT5kE5nrQ9X0dDiKx64IEHWLw49Hjt7NmzM+yzfft2rFYr3bp14/Dhw9SuXZuPPvqI+Ph4XnvtNQYPHozL5eKDDz5g48aN7N27l/fffz8nT0MIIbJMsRfCWr0/1ur9MfxOgvt/I7BrCsH9syDgCnXynyOwYwKBHRPAEo25/B1YqtyLuXwXsETjXzEMNXkX/hXDsJTrkOmPXpGU2xOihmHkiesohBD5iRFwoZ/ZCYBapJY8VSKEEFlkKlyFqF7L8K/7EN+q10Hzo5/6C9ekxihRxVBdR+SeNo/JPYXxhMjEuHGhmQzHjh3L0KFDM0zcBoNBli9fzqpVqyhdujQvv/wyjz/+OJMnT6Z69eosW7YMAE3T6NixI+PHj2fSpElMnjyZAgUK8PHHH1O4cOEcPS8hhMgKxRqDpWovLFV7YQQ9BPfPJbh7KoG9v4L/XKhTwEVw108Ed/0EJjtqQj30pLUA6ElrCfz9M5byncBkBdUCiiniN2o5kRA1DAM0HwS9GJo37X+DHtC8GEFvaCRz0IuhedL00U79leY6aonzMJfvlK0xCiGESEs7sYnzk3SaEupGNBYhhMhrFNWErdFQzBW64pk7AP3ERjA0DNcRQO5p8xpJ2oo8Y8CAATz22GOcOnWKIkWKpFlXtmxZmjVrRqlSpVAUhf79+9OpU/oPoZEjR9KrVy8KFSrEW2+9xebNmxk/fjwjR47kjTfeyKlTEUKIa6KYHVgq342l8t3YNT/BAwsJ7p5McM8vGN5QHW80L/rRlWm28/7WG2/aPYUTuIrJCqoVTBZQrSiqJbTOlPpaveh1al/FZAklfzPYh6Jaw+uU80li9aLXqX31k5vSJER9K1/DFHdLKJmaLsnqCSdXjaAvNcl6cdv5/6bd9nxZieziWfJPoh/YjqqasnW/QgghLtAvnoQsXiYhE0KIa2EqWovo3qvwrnqTwJp30qzzLHmGmAHbIz6IQ1yZJG1FrnX27FncbjclS5YEQjWPixQpQlxcXLq+vXr14ssvvyQ5OZlChQoxe/Zs6tSpk6bPvn37mD9/Pr/99hvJyckEg0EURUFVVZxOZ46ckxBCZBfFZMVSoQuWCl0w2n6BdngpgV1TCPz9I/jOXmHr1BGomg8jkG5NRPhXvx2hI2edcXY3ru8qY2v2JpaqvUPJayGEENlKO7Eh/FpNkEnIhBDiWikmK5ZSLQisSdtunP0b78LHsLf7QhK3uZwkbUWuMHjwYGbNmsWxY8fo1KkTsbGxLFy4kF69euHxeFBVlfj4eGbOnBn+UHnkkUfo3r073bt3p2zZsjz99NM0b94cVVUpVaoUX375ZZpjPPPMM4wcORJFUShYsCB9+/alVq1axMTE8OOPP0bitIUQIlsoJgvmsu0xlWmHdnwd+on1YFxSQ95aADW+PooRxND8oPtBD4Dmx0j9L7ofQwuE1ml+MLTInNDVMlnBZA/NjGuyhyZlu2gZ86VtttB/zQ6U1PXhdWYHqDZ8K4dhnNub7joaKYl45w7At+JVrPWfx1rzYRRLdIROXAgh8h8tKTVpq6iYitaObDBCCJGHGYaBd+WroJjS3dcHtn6FEXDh6PBVugmNRe4hSVuRK4wePTrD9tWrV2e6zddff51muVevXjz55JOoqpph/xkzZqRZHj58OMOHD7+6QIUQIhfTEueFSw6k40/G3mjoVdWvMgwdzidx9UAo2aulvtYven25dt0f3oehhf4ENn2K4TpK2nG9CkpsGayNX0ExO0I3j2Z7OKka+q/josTsRQlZJePP/WsV3D8X4+zuy1+blAP4lj6L/8+3sNb9J9a6T6HY0z8JIoQQIuuMoA/91F8AqHHVUSxREY4oc08//TQzZswgMTGRDRs2ULduXQA6duzIsWPHUFWV2NhYRo0aRb166UcM//7774wYMQKn04miKNxxxx289957qKrKvn376N27N06nk379+vHyyy8DocmXhw4dmu57jRBCZERLnId+PJPvBkBw50RcZ3YQdedU1AJlczAykVWStBVCCCHygfAv6aiAnkEPFe/KV4ku1zHLj0EpigpmG2ALLWdDnMH9c/GnToSQloGRcgBTbNmIToxw5euogLVAeBI4w3sK36rh+NZ9gLXmIKz1n0eNLZ2TIQshRL6hn/4r9BQIYIrP3aURevbsyZAhQ2jevHma9p9++olChQoBMG3aNAYOHMimTZvSbV+wYEEmTpxI5cqV8Xq9tG/fnnHjxjFw4EA+/fRTnnzySfr160eNGjX45z//SUxMDM8++yxffPFFTpyeECKPu/I9bYietB7XpIY4uv6EuUzrnApPZFH2Dk0RIgI03WDtgRMs3nuCtQdOoOmRqsgohBARpPkxUg6S+U2ZjpFyKDQKNkLS3jxmJJRYNowIfo5f8ToaKGYHUb1XY7mlX+hxM4CAC/+GkTi/q4Rn3kNop3dk+ZDeJU+T8k0FkkeqaEkbw+2uqZ1wTqiDc0I9XD+1vPDI8CUU50HcU9qS/FkhnBPSJjm042txTqiHc9yt+LeNDbcHDy7Cs2BwlmMUQoicoF00CVlur2fbsmVLSpdO/yPd+YQtwLlz5zL9obRWrVpUrFgRALvdTt26ddm/fz8AFosFt9tNIBBA13VUVeWLL76gY8eOVKhQIdvPRQiRD13xnpbwfazhOYl7agd8G0ZF9j5cpBPxkbbbt2/nu+++Y+/evZw5cybdXxBFUVi4cGGEohO53YKdhxkxfyPHUzypLTspFutgSIe6tK9WKqKxCSFETlLMNqL7rMbwnABA13VOnz5NXFxcuGyM4khAMdsiF+TVJJYjFGdWr6MaWxpz5/HYmr6Jb/1HBLZ+C5oX9ACBbWMIbBuLudLd2BoNxVS88WWPaa7cE2uDIbh+apGmParrjyj2QgAEdk/DM+9BYvpvTLe9YYnB1vRNCKTg+2NYmnW+Ne9jb/0JpmINcI6vjbXGAIygB9+qN4jq9ss1XiUhhLgxLv5xKrePtL2cBx54gMWLFwMwe/bsK/Y/duwYkydPZubMmUCo9MLAgQMZPXo0L774IufOnWPy5MnMmzfvhsYthMg/snJPi2rFu/xfaIlzwdDwLX0WPWkD9nafh0qRiYiLaNJ2/PjxPPjgg1gsFqpVq0bhwoXT9ZEsv8jMgp2HeXHqynQznSeleHhx6ko+7NE0WxO311u3CuCbb75hxIgR6LpO27Zt+eyzz7BYLKxdu5ZHHnkEv9/P0KFDGTBgAACLFi3ixx9/zLTmrxBCXEyNLQOxZQBQdB2DJEwJCZnW+s5peSKxzNVdR7VgBRxt/oftttfwbxyFf9Nn4DsLGAT3TCO4Zxqm0m1CyduyHTIccWUu3TLDOM4nbAEM3zkyLVBhK4wpoRr64WUZnIwFgm6MoBdFDY2m8K0cjrXu02n2L4QQuYGeJmlbN3KBXKdx48YBMHbsWIYOHXrZxG1ycjLdunVjyJAhNGzYEIASJUowd+7ccJ9evXrx0UcfsXjxYj7//HNsNhvvvvsu5cqVu7EnIoTI07JyTxt110x8K4bhX/s+AIHtY9FO/0XUnVNC24uIimjSdvjw4dSrV4/ffvuNokWLRjIUkcdousGI+RvTJWwhNK2NAoyYv5E2VUpiUrOjCuP11606cOAAr7/+OuvXr6dYsWLcddddfPnllzz55JO89957jBo1igYNGlCrVi0GDBiAx+Nh+PDhMtGAECJfye2J5WulRiVgb/YfbA2H4t/yJf71/8VIrd2rHVqM+9Bi1Ph62BoNxVz53nAC9Uo8cwcQPBgarRV196yrjsvW5FW8Cx/DCLiwNR+BlrQRPXkf9hbvX/W+hBDiRjJ0De1k6B5aKVgpX/ywNGDAAB577DFOnTpFkSJF0q1PSUmhS5cu3HXXXTz//PMZ7mPKlClUqlSJunXrUr16dVavXs3atWt57bXXGDt2bIbbCCFEVimqCXvzdzEl1MMz7yEIutGPr8U1qRGOO37GXKrFlXcibpiIfkM6cuQIDz30kCRsxVVbte/4RSUR0jOA4ykeft9zLNuOeb11q2bOnEm3bt0oXrw4iqLw2GOPMWnSJOBC3Sqv14vJFPoiP3z4cJ555pk0+xdCCJG7KdZYbA1eIObBPdjbf4VaqEp4nX5iA57ZvXGOvQX/li8xgt4r7s/RaSyxjxzA1uwtfL+/dNXxmOKqE91rKTF912Ku2A3v8hextxpJYMck3DN74pn3EIb3zFXvVwghspt+ZicEQ/f3plxezzYzZ8+e5ciRC5NtTp8+nSJFihAXF5eur8vlomvXrnTu3Jlhw4alW39+f5988gmvv/46AG63G1VVUVUVp9N5Y05CCHFTslS9j+h/rEApEKqbbbiTcE9ph3/TZ/IEfARFdKRt7dq10/yjJsSVJHv8/Lh+D9+t2pml/s9O+YP6ZYrSvFIJmlcqTuWiBbI8a/rVyErdqsOHD1OpUqXwcvny5Tlw4AAAr732GoMHD8blcvHBBx+wceNG9u7dy/vvy0goIYTIixSzDWvNh7HUGEhwz3R8a95DT1oHgHFuD96Fj4XKFNR/Fmutx1BsBS67P2uNAXgXPo7uOYXqSD9aKyv8G0ZiqdITxVYI3+r/EN1/E4Ht4/FtGIm96RvXtE8hhMguF09Clhfq2Q4ePJhZs2Zx7NgxOnXqRGxsLAsXLqRXr154PB5UVSU+Pp6ZM2eGv3888sgjdO/enTvvvJOvvvqK1atX43K5mDp1KhAqg/DKK6+EjzF06FCGDx+Ow+EAYNiwYTRs2BCr1co333yT8ycthMjXTPG1ie6zGs9vfdAOLAA9iHfxU2hJ67G3+TTiJcxuRhFN2n788cf06tWLLl260KxZs0iGInK5Y8luJqzZxZSN+3D7g1neTjdg7YGTrD1wkpGLt1CiQBTNKxWneaXiNC6XQJQ1e/4XuJq6VRmpXr06y5aFahFqmkbHjh0ZP348kyZNYvLkyRQoUICPP/44w7rPQgghci9FNWGpci/myj3QDi7Ct/b90E0wYLiP4fv9JXyr38Va53EwtPB2hvcsRtCNGlMSgMDu6SiOIij29KO1skI/t4/ggQVE3T0b/MmgBwEFFBUCMlpLCBF5+omL6tnmgZG2mc05sXr16ky3+frrr4FQTfdnn32Wd95557LlgS49xqBBgxg0aNA1RCuEEFmjOooQdfdsfH/8G/+6jwAI/PUt+qm/cNw5GTVGJnzPSRFN2r7//vsULFiQFi1aUKNGDcqWLRt+NPw8RVH45ReZ3fhmtfvEOcb++Tez/zpAUL8wJF8BrGYTvqCW6bZRFjNFom0cPOsKtx1NdvPzhr38vGEvVpNKw7LxNK9UnBaVSlA2Lua6471c3apSpUqRmJgYXt6/fz9ly5ZNt4+RI0fSq1cvChUqxFtvvcXmzZsZP348I0eO5I03ZCSUEELkRYqiYC7bDnPZdmjH1+JbO4LgrimAAf5z+Ne8B4Drp9tRHPFE91qKe9Z9oUeFFRXFEU9U91/Do7U88x/BXLE7pgp3QtCN65tyoPswfOdI+boMllv6Y2/+bvj43qXPYm/139D2toKYq/XBNaE2WGKI6vpDJC6JEEKkoV00CZmaB0baCiFEfqWoZuwtPsAUXw/PgkEQ9KAd+xPXxEY47vwZc8nbIx3iTSOiSdvNmzejKAply5bF6XSybdu2dH1uxKPsInczDIMNh07x3aqdLNt9NM06m1nlrlrlub9xVf4+cY4Xp64MbXNRn/N/Y97q1oj21UqReDqF3/ccY9nuo6w7eJKApgPg13RW7DvOin3HGbFgE+XiYmhRqTjNK5WgQZmiWM1XniTm7NmzuN1uSpYMjYS6XN2qO+64g3vuuYc33niDYsWK8cUXX9C7d+80ffbt28f8+fP57bffSE5OJhgMoiiK1K0SQoh8xFSsIVF3/IR25m/86z4gsG0c6IHQyqAHw3kI7x8v42j/Jab4Ohnuw9HhwmgtzFFEP5x42dFaUd3T/gBubzocmg7PjtMRQojrZhhGOGmrxJRCjS4W4Yiyn+vYGbznQoNJDF0n+cwZzGf8KKmf3faC0UQXl6fqhBC5h+WWvqhx1XH/2gMjJRHDfQz35LbYW4/CWntwpMO7KUQ0abt///5IHv6m8fTTTzNjxgwSExPZsGEDdevWxev10rt3b7Zt24bD4SAhIYHPP/+cypUrp9t+7ty5/Otf/8JsDv11SUpKonjx4qxfv54zZ87Qo0cPTp48SYsWLfjss88AOHHiBL169WL+/PlYLJYsxakbBkt3HeW7VTvYdPh0mnWxdgu961eiT8PKFIm2A1A2LoYPezRlxPyNaSYlS4h1MKRDXdpXCw3bLxcXS7m4WPo1qoLbH2R1YhLL9xxj+e6jabZLPO0k8fRuJqzZjcNiokn5YuFSCsULRF133apy5crx+uuvc/vtoV+lWrduzeDBaT/onnnmGUaOHImiKBQsWJC+fftSq1YtYmJi+PHHH7N0HYUQQuQNpsJVcbT/CluT4fg3jMS/eXSoVIGhE9w5ieDOSZjKdcbWaCimUi3lh2whRL5lJO8D/zkgb9SzvVquY2eY3vc99MuUeVOtZu6e+JIkboUQuYopoR7RfdfgmfUPtEOLQQ/gXfR4ap3b/0MxWSMdYr4W0aStyBk9e/ZkyJAhNG/ePE37o48+SpcuXVAUhf/973888sgjLFmyJN32nTp1ol69eiQkJKCqKnfeeSdt2rQB4Pvvv6dNmza89tprtG3blq1bt1KzZk2ef/553nvvvSwlbP1BjVl/HWDsn3+z71RKmnXFYh3c37gKPepUINqWfl/tq5WiTZWSrDuQxJ4jSVQqmUCDsgmY1Iy/2EZZzbSuUpLWVUpiGAa7TySzfM9Rlu85xqZDp9BSZ0X0BDQW7zrC4l2hifKqxBekRZ+nGDzsP9QuVQTzRaOZslq3CkJ1qC5N1F5sxowZaZaHDx/O8OHDM+0vhBAi71NjSmFv8QG2Ri/j3/wZ/g2jMDwnANAS5+BOnIOpeBOsjYZirtgNRcl8RK0QQuRFF09CpuaBerZXy3vOddmELYDuD+I955KkrRAi11EdRYnqMRff8n/h3/AJAIGtX6XWuf0ZNbpEhCPMv3JF0nbp0qXMmjUrXO+zXLly3HHHHbRq1SrCkeUPLVu2TNdmt9vp2rVreLlJkyZ8+OGHV9zXkSNHWLhwId9++y0AFosFt9uNruv4fD6sVitz5syhcOHCNGnS5LL7cvoCTN6wlwlrdnHC6U2zrlLRAjzYpBqda5TBYrr8l1OTqtCwbDxl7QYJCfGomSRsL6UoClUSClIloSAPNb2FZI+flfuP8/ueY/y+5xin3b5w310nzrHrxDm+XbmTWLuFphWK0bJSCZpVLBYe+SuEEEJcD8VeGFvjV7DWe47Atu/wrf0QIyV0b6QdW4Xn13tQ46pjbTgES7W+KCYLwQMLsC/6J8G2/4e1fMcIn4EQQlybi+vZ5seRtkIIkdcpqhl7q/+iJtTHu+BR0HxoR1ek1rmdjLnE5fM/4tpENGnr9/vp06cP06dPxzAMChUqBITqhH700Ufcc889TJo0KcuP14tr98knn3DXXXddsd+YMWPo2rUrCQkJAPTv358BAwZQr1497r77bkqVKsXDDz/M7NmzM93HCaeH79fs5ucNe3D60v7i3KBMUQY2qUaLSsVz/DHQAg4rnaqXoVP1MuiGwbajZ/h9zzGW7znKX0fPhOvmpngDzNt+iHnbD6EANUoUpkXqZGY1ShRGvSRuTTdYd+AEe46coJJXuexIYCGEEEKxRGGt8ySWmo8S3PUTvjXvo5/aCoB+ejveeQ/iW/kalnrPEdw+HjV5F/4Vw7CU6yAlFIQQeVKapG1C/QhGIoQQ4nKs1e/HdL7OrfMQhusI7smtsbf5FGvNhyMdXr4T0aTtG2+8wbRp03jxxRd54YUXKFYsVHA+KSmJjz76iA8++IA333yTt956K5Jh5nvvvPMOu3fvZuHChZftZxgG3377LaNGjQq3RUdHM3ny5PDyc889x9ChQ9m9ezfvvPMOAMOGDaNOnTrsP5XCuNV/M2NLYngyMAhNHNa6akkealKN2qWKZO/JXSNVUahZMo6aJeN4rEUNTrm8rNh7nOV7jrJi33FSvKEJYwzgr6Nn+OvoGb74fTuFo2w0rxiqg9usQjFWHzhxSc3dnRS7pOauEEIIkRHFZMFySz/M1foS3D8b/5r30Y78DoCRchD/sufDffWktWiJ8zCX73RDYvEueZrAnl8xUhKJ7rseU0JdjKAXz+w+6Ke3gdmBEpWAo+1nqIXS18e/mGfugwS2jyX2sdMo9kIY3jO4Z96L4TmJqVRzHG1D9fF19wk8s+4jqsc8FJP8gC9EfmUYBvqJUHkExV4EJbZMhCMSQghxOaZiDYnuuxbPrPvQDi8DzY93waBQiS//OYyUA3K/mE0imrSdOHEiAwYMYMSIEWnaExISeP/99zl+/Djjx4+XpO0N9OGHHzJ16lQWLFhAVFTUZfsuXboUr9dLp04ZfyFcvXo1SUlJ3HnnnbRo0YLx48djGAa9+vSj5ZCPWLTzcHi0KoDFpHJnzbIMuK0qFYoUyMazyn5Fou10q1WObrXKEdR1Nh8+xfLUMgp/J50L9zvj9vHr1kR+3ZqICugZ7CspxcOLU1fyYY+mkrgVQghxRYqiYKlwB5YKdxA8/Dv+tSMI7puZrp97Zg+sDYdiqXIvalyNbB11a67cE2uDIbh+apGm3VJrEObyofr4/o3/wzN/ENG9Fme6n8DuqWBKe/sZ2PE95tKtsTV5Ddfkdmgnt2IqWhPfshewNX83X96ACyEuMFxHMdxJQKierTwxIIQQuZ8alUBUj/l4lz1PYNOnAOintqAWawRG2kzI1d4vBndPk/vFVBFN2h49epTbbrst0/W33XYbP/zwQw5GdHP5+OOPmTRpEgsWLAiXpricb7/9loEDB2IymdKtCwQCDB06NPx+uVwu1iSeYPLGPWw7eIzAzsPhvjE2Mz3rVaRfwyokxDqy7XxyillVqV8mnvpl4nmmdS2OJbvDdXBX7T+OJ6ABGSdsITQ6VwFGzN9ImyolpVSCEEKILDOXao65VHN8m7/At+iJtCuDHvyrhuNfNRy1UBXMle/BXOkeTMUbXffkZebS6evjK2Y7lgoX6uObSjTBt+6jTPehu4/jW/0u0T0XEdj6zYUVqgUj6MYwdNB8YLIS3D8HxV5I6qMJcRPQTkg9WyGEyIsUkwVHm//DlFAf76LHQfOjH18Dignt1F+YEupe9f0i3hP4174n94upIpq0LV26NEuWLOGxxx7LcP3SpUspXbp0DkeV/wwePJhZs2Zx7NgxOnXqRGxsLEuWLOGFF16gYsWKtGnTBgCbzcaff/4JwGuvvUbJkiXD701ycjLTpk1jy5YtGR7jgw8+4IEHHiCuaDwztybiuP0e7r+vBwAlutwPQHyMnX6NqtCzbkVi7fnnV5DiBaLoWa8iPetVxB/UWHfwJJM37GHBziOZbmMAx1M8rD94gkblEnIuWCGEEHmeYRgE/voWFBMYWoZ99LO78K8dgX/tCJSYUpgr3Y2lcg9MpVqgqDfm9s+/YRSWSt0zXe9bOBh7i/dRrLFp2i3V++OZOxDX9/UxV7oLNaYU7vmPEHX3rBsSpxAid9GT1odfmxIkaSuEEHmN9dYHMRW5NVS+wHkYDA3v/IfACGKtMSBN3yvdL1pXv4j19nflfjFVRJO2AwYM4PXXX6dQoUI899xzVK5cGUVR2LVrFyNHjuTnn3/mjTfeiGSI+cLo0aMzbDcMI8N2gDfffDPNcoECBUhJSUFVMx6p8+yLQ5i2aR/dvpjD0WQ3lKxOtWf/C0D5uFgGNKnKnbeWxWpOP0o3P7GaTTStUIyzbt9lk7bnnXR6cyAqIYQQ+YmWOA/9+NpM16tFaqGf/iv8aJrhPExg06cENn2KYi+CuWK30Cjcsh1QzPZsicm3+h30s7uJundBhutNe75HjS2LuUzbdOsUSzRRd/4cXvYufQ5rwyHoZ3fjW/MuALbGr2CKr5MtsQohcpeLJyFT8+kkZPaC0SgmFUPL7Fk8UC1m7AWjczAqIYTIPqbijYnuswbPrF5oR/4APYB33oPoSeuxtfgQxWS54v1iYOs3GNGl5H7xIhFN2r788svs2bOHL7/8kq+++iqcENR1HcMwGDBgAC+//HIkQxRXcNrtY9La3fywbjfJqZNznVe7ZBwPNq1G6yolUW+y2lRFY7L2JXjr0TN0rlFGancJIYTIEsMw8K58FTKtnK6C2Ub0I0fQ9v1KcPc0ggcXgOYPbe89RWDbGALbxoAlBnP5rlgq3425fFcU27XVl/et+5DA7mlE95iPYsm4Pr4paQXBU2tI2XdhNITz+zpEdZueZmSddmw1uvsE9op34vqpJY5O4wADz7wHie615JriE0LkbuGkrSXmihPT5FX2uFhshaLxnkoBoNmwPmgFLByctoYjK7cDUHdQZ6KLF45kmEIIcV3U6OJE3buQlC+KQsAJgH/j/6Gd2IypdGuC+2Ze9n5RO7wE06EluL6rFG672e8XI5q0NZlMjBkzhueff57Zs2eTmJgIQLly5ejatSu1a9eOZHgC0HSDdQdOsOfICSp5FRqUTcCkKhw642Tc6l1M37wPXzDtl8YWlYrzUNNq1Ctd9KZNRtYvE0+xWAdJKR4yH88ME9bsIsnp4bXODfJVyQghhBA3iObHSDlI5pXTdYyUQ6i2gphqPoy15sMYvmSC+2cT2D2N4P7ZEHCFugacBHf9RHDXT2CyYi7bAXOluzFX7I4aFZ+lcHzrPyaw84fQDbi9UKb9/E0/pVBCQvgH+uSRKjH9NqXZxtACeH9/CUeXSaHlgAsUBVAwUm/8hRD5i+45hZES+g5oiq973fW3c6s9v60JJ2xLNatBhY71SUpKotaA9uGk7f6FG6nRp/VN+/1JCJE/KCYrir0IlnrP4l/7PugBtMNL0Y6uIKrH3MveL9o7jSc5KYmE1HtGuV+McNL2vNq1a0uCNhdasPMwI+Zv5HiKJ7VlJ3FRNsoWjmHzkVPoF2UjzapClxplGdCkKlXiC0Yk3tzEpCoM6VCXF6euRIHLJm7nbT/EtqNnGHH3bdxaIi6nQhRCCJEHKWYb0X1WY3hOAKGnk06fPk1cXFw4Iao4ElDMtgvb2ApgqdYbS7XeGEEPwQMLQiNw987A8J4OddL8BPfNIrhvFiiDMZVqmVoH9x7U2DJ4FgwmuH82husY7umdwRJLdM/F+Ja9iFKwIq7JqY+xmWzE9FkFgHfla6jRJTHXfDRL5+Zf9wGW6vejRhcDwNb0DdzT7wi9bjHiuq+dECL30U9sDL/Or/VstUCQreMXhpdrP9gh/LpI9TLEVS3N6b8PcXrnIU5tP0jRGmUjEaYQQly3i+8XA1u/RIlKwNAC4EkCPYB7cjuU2DIoUcXT3S9aa2c819Wlbrb7xVyRtBW5z4Kdh3lx6sp0ycbTbh+n3b7wssNi4t66Fbm/cRWKF8h4iPvNqn21UnzYo+kliW8oFutgSIe6AAyftZYUX4BDZ108MG4xz7etTd+GleUXdiGEEJlSY8tAbBkAFF3HIAnTRaNYL0cxO7BU7IalYjcMPYh2eBmB3VMJ7vklNHEEgKGjHVqCdmgJvqXPohZrhKXS3UT1WIAprlqa/RV4NvP6jPamofr4up6+T0bb2RqnLYllqXgnlop3XvGchBB5l3bRJGRqfP5M2u6duw7XsTMAlGxyC0Wrlw1/LiqKQrUezVj53k8A7Jz2hyRthRB5lqN9+vmUdOcRPDN7oh1bBRgYKQewVLkXQw+iqObw/WJG5H4xh5O2qqqiqiputxur1YqqqldMTimKQjAYzKEIBYRKIoyYv/Gyo0MVBR5vXoPeDSpT0GHNsdjymvbVStGmSknWHUhiz5EkKpVMCJeYALilWCGGTl/F1qNnCOoGIxZsYk3iCd64o6FcVyGEEDeUopoxl2mLuUxbjNaj0I6tIbhnGsHdU9HP7g7304+vwXd8Db4Vr6DGVcdc+R4slXugxteTHxmFENfl4knITPlwEjI9qLF17IUJd2oP7JiuT/n29Vj3v1/xOz3sX7iRhk91xyYTkgkh8gk1piRRPRfjXfIUga3fAOBf/1+0E5txdJ2E6iga4QhztxxN2r722msoioLZbE6zLHKX9QdPpBkZmhHDgPplikpiMQtMqkLDsvGUtRskJMSjqhf+zpcuFM2Y+9swaskWxq3eBcDiXUfY8e0CRtx9G7VLFYlU2EIIIW4iiqJiLnEb5hK3Ydz+LvqpvwjumUZg97Q0jy/rp7fjX70d/+p3UGLLhSYxq9wDU4lmKKopcicghMiT9BOpSVuTDTWuemSDuQH2zVuH82ioDE2JxlWJr1kuXR+z3UqlOxqx/cdl6P4gu2et5ta+bXI6VCGEuGEUsw17uy8xJTTAu+Rp0INoBxfimtSYqG7TMMXXiXSIuVaOJm2HDx9+2WWRO5x0erO1n7g8i0nlhXZ1aFg2nldnruWc18/RZDcPTljCP1vV5IHbqqLKjxtCCCFyiKIomIrWxFS0JrbbXkU/t5fAnukEd09DO7KC85XajZRE/Bs+wb/hE5SoBMwVu2Op3ANTmbYopgs/6gYPLMC+6J8E2/4f1vLpR5kJIW5Oht+JfuZvANSitVBM+WtSXj2oseUKo2zPq3pXM7b/uAyAv6evpEbvVihZKHkjhBB5haIoWGs/hlqkJp5ZvTDcxzGS9+P6sRmODt9gqdZb7hkzIP8SiHSKxtiztZ/ImlZVSvLzw+2pVzo0ujaoG/x38Rb++dMfaeoICyGEEDlJLVgRW/3nib5vOTGDDmNv+zmmsh1BvfDbv+FOIrD1a9zTu5IyOgH3b/0J7JqM7kvBv2IYavIu/CuGYRiXK74khLiZaCc3cf5HIFM+rGe7f8EGUg6fAqB4gyok1K6Qad8CZeMp0agqAM4jpziy+u8ciVEIIXKauVRzovusQS3WONQQ9OD5rS+eZf/Cv+IVuWe8RESTtgsXLuSDDz5I0/btt99StmxZihUrxnPPPYemaRGK7uZVv0w8xWIdZDa2UyE0mVb9MvE5GdZNoViBKL7u14pBzW4JX//f9x7jvm/ms+7AiYjGJoQQQqjRxbHWHkx0jznEPnoce6dxmCvdA2bHhU7+ZII7J+KZdR/O0UXRk9YCoCetRUucd0Pi8i55mpRvKpA8UkVL2njF9ksZho5lw3Bc42vhnFAH1+S24bq++rl9OCc1wTmuJr7V74S30U5vxz3jrhtyPkLcDPSLJiEzJeSvpK2u6WlH2T7Y4YrbVL2nWfj139P+uCFxCSFEbqDGlia61xIsNQaG2wLrP0JPWgfc2HvGvCaiSdvhw4ezadOm8PKWLVsYPHgw8fHxtG7dmlGjRvHhhx9GMMKbk0lVGNKhLkC6xO355SEd6oYn0xLZy6yqPNWqJp/3bkFclA2AE04vj0xcypd/bEfT5RcnIYQQkafYC2Ot3p+oblOIHXwCx51TsNzSH6wFL3TSA2m2cc/+B74tX6F7TmZrLObKPYm+bzlKbLkstV9K2/sr6ok1RPVdT0z/TZjLtMX7xysA+Dd9irXOE0T330Rg+zgMfwqGYeBd8hz2ViOz9TyEuJnk50nIEhduJPlgaMBFsXqVKFa30hW3Kd2sBlEJoc/PQyu2h2vhCiFEfqSY7dg7fIO99SjSpSYVFe/KV2W0LRFO2m7fvp2GDRuGl8ePH0+BAgVYvnw5P/74I4MGDWLcuHERjPDm1b5aKT7s0ZSEWEea9oRYBx/2aEr7aqUiFNnNo2mFYvz0cHsalQuNaNYN+HTZXzz+43KpJyyEECJXUSxRWCrfg6PzOGIHHyfqnjmYyndJ39GfjG/hYJxflsA1pQP+TZ+ju45d9/HNpVuixpbOcnsGZ4Ci+0DzYhgGhj8ZNSb1Xke1QNAdSkAbOigqgc1fYC7XAbVg5o87CyEuL5y0VUyoRWtFNphspGs6W8ZdPMo2a3UZVbOJKt2bhhYMg10zVt2I8IQQItdQFAVr3aewNX837QpDRz8uo20hwklbl8tFgQIFwstz5syhc+fOREVFAdCoUSMSExMjFd5Nr321Uvz2RFe+6tOCl1pV46s+Lfjtia6SsM1B8TEORvduyeMtanB+YPOf+5O479v5rNp3PLLBCSGEEBlQTFZMZTtgeE6AYsq4k6GhHVyId/GTOL8qhevnVvg2jEJPOZSzwaYyVbwTLaEZrq9L4fyqJMGDi7A1fRMAa72nCeyejuvHZljrv4DhO0dg9xSs9Z6NSKxC5AdG0Id++i8A1LjqKGbHFbbIOw4s2cS5/aH79ITaFShW78qjbM+r0u02FFPoK/quX/9E8wdvSIxCCJFbGIZBYNfPoFw62tYko22JcNK2TJkyrFmzBoDdu3ezdetWOna88Evk6dOnsdlskQpPECqV0LBsPG0qxtOwbLyURIgAk6rwWPMafNmnJfGpk7+dcvl47Ifl/G/pVoK6HuEIhRBCiLS0xHnox9eCkcncBFHFL1ow0A4vx7f0WZzflMX14+341n2Efm5/ToQKgH58Leq5HUQ/fICYQYdD5REWPQ6AGl2C6B5ziOm3HmvtwXiXPou9xYdohxbjntkT92/90JNlkIEQV0M/tRX0UEIyP5VGMHSdzWPSjrJVlKx/f3IUKUDZ1rUB8J11cmDJ5myPUQghcpML94yX5DUMTUbbEuGkbb9+/fjyyy/p3r07nTp1onDhwtx114UJHdatW0fVqlUjGKEQuUejcgn8+FB7bq9YDAjNtfvVih08OnEZx1M8kQ1OCCGESGUYBt6Vr5L5baaKGluaqD7rsDZ+GbVwtTRrtaMr8S3/F87vKuKc1BjfmvfQzuy6oTEHd0xAS2iOYiuEoqhYagwgeHBxun6BXVNQC1bElFAX7+KncXT8DmvNR/CtfP2GxidEfqNdNAmZGl83coFkswPLtnJuX6jkS3zNchRvWOWq91HtognJdsqEZEKIfCwr94w3+2jbiCZtX3nlFV566SUOHjxI2bJlmT59OoUKFQJCo2yXLFlC9+7dIxmiELlKkWg7/7uvOc+0rokp9Vf7dQdPct8381m++yhPP/005cuXR1EUNm7cGN4us/ZLrVixgujoaOrWrRv+4/GEEsJr166lbt261KhRg7Fjx4a3WbRoEYMHD74h5yuEECIP0vwYKQeBzJ4E0TFSDmEqUgN7s/8Q/cA2ou/fgq3J66hFaqbteXwtvj9exjW2Gs4JdfH9+Rba6e3ZHrJSoAKmpN8xND8Awb0z08VieM/i3zgKW5NQgtYIukOP8ikqRsCZ7TEJkZ9dnLTNLyNtDV1ny3cXRoRd7Sjb8xLqVKRghdDTCCe27Of0rsPZFqMQQuQqWbxnJPX+7GZkjtSBDcPA7Xbz6quv8vbbb6dbHxcXx7Fj1z8xhRD5jaooPNT0FuqXKcrQX/7kWLKHsx4/T/38By0q1mPx0hdp06plmm169uzJkCFDaN68+RX3X61atQwTu++99x6jRo2iQYMG1KpViwEDBuDxeBg+fDgzZszIrtMTQgiRxylmG9F9Vodq2gK6rnP69Gni4uJQ1dB4AcWRgGIOlcBSFAVTkVsxFbkVW5PX0U7vJLh7CoFdU9BPXJhdXj+5Gd/JzfhWvo4aVwNzlR5YKvdELVoL78LHCO6fjeE6hnt6Z7DEEvvgLjwLBmfYDuCZ/wjmit2xVOqOpfYTuI9swD2xPorJghJVHEe7z9Ocl/f3odiavB6uvWlr/AquiY3AZMXR4esbfl2FyE/0ExvDr035ZKTtwd//4syeowAUqV6WEo2rXWGLjCmKQrUet7P6oykA/D1tBU2G9Mq2OIUQIre42nvGm1HEkrZ+v5+4uDjeffdd/vWvf0UqDCHyrLqli/LTQx14bdYaluwK3SAu98RybsluND3t4wMtW7bMaBdXxWKx4Ha78Xq9mEyhiWWGDx/OM888Ex4hL4QQQgCosWUgtgwAiq5jkIQpISF8A345prhqmBq/jK3xy+jn9hLYlZrAPb463Ec/vQ3/n9vw//kf1EJVMFfuQVS36agJ9dOMbHO0H53pcS5OtCpmG/7GH1LoMjFeui9rrUFYaw264vkIIdIy9CDaiU0AqIUqo9gKXGGL3M8wDLaMmR9erv1gh2saZXtexU71Wf/ZTIIeH3vnraf+E3dijck/k7UJIcR513PPeDOI2FWw2WwUL14cq9UaqRCEyPMKOqyMvLcZ/2pXB3PqJHGbj5zmhNPD6sSka9rnnj17qF+/Po0aNeKzzz4Lt7/22mu88847dOzYkQ8++ICNGzeyd+9e7r333mw5FyGEEOJSasGK2Br+i5g+q4h5aD+2lh9jKtkcuJAM0c/uwr/2fVyTGuH8rhLeZS8SPLoK49IJLYQQuYJ+ZidoXgDU+PxRGuHQH9s4/XeojEFctdKUalr9uvZnibJTsXMDADSvn71z1l53jEIIIfKeiI20BRg4cCDjxo3j8ccfl+StENdIURT6N65C3dJFGPLLnxw+60I34MMFmzgblcCzbWphNZuytK9atWpx4MABChcuzKFDh+jatStFixblvvvuo3r16ixbtgwATdPo2LEj48ePZ9KkSUyePJkCBQrw8ccfU7hw4Rt5ukIIIW5SaoGy2Oo/i63+s+jOIwT3TCOwayra4aXhGYeN5P3413+Mf/3HKDGlsFTugblKT0wlmqGoWfu3UAhxY6WtZ1s3coFkk+weZXte1Xua8fe0FQDsnLaCavc2z5b9CiGEyDsiOt64Vq1a+Hw+br31Vt5++22+//57pk6dmu6PEOLKapaM48cH29PhllLhtu/X7mbA+CUcPJO1CYLQ6eoAAQAASURBVFJiY2MpWLAgAKVLl6ZPnz4sX748Xb+RI0fSq1cvChUqxFtvvcWPP/5Iy5YtGTlyZHacihBCCHFZakxJrHWeJLrnQmIGHcHe7gtMZTuCciExazgP49/4f7h/boXz69J4Fj1B8OAiDD2Ybn/BAwuwz25F8MCCnDwNIW5Kej6bhOzIqh2c2nEQgMJVSlL69luzZb+FK5YgoW5FAJITkzi+YU+27FcIIUTeEdGRtn369Am/fvXVVzPsoygKmqblVEhC5Gmxdgsf3N2EcU9asZhCv8lsO3aGf3y7gNe7Nrji9sePH6do0aKoqkpKSgozZ87k4YcfTtNn3759zJ8/n99++43k5GSCwSCKoqCqKk6nzJ4thBAiZ6lRCVhrPYq11qMY3tME9swguHsKwcR5oAcAMNzHCWz+gsDmL1AcRTFXugtL5Z6YyrQF1Yx/xTDU5F34VwzDUi57RskJITKmJW0Mv1bj60UukGxgGAabv5sXXq49sGO2fn5Uu6cZSRv3ArBz6h8Ur1852/YthBAi94to0nbx4sWRPLwQ+c7gwYOZNWsWp5OOoX//Ph7FTOXnRrHzx0/p9uZ6NOc5OnXqRGxsLLt37wbgkUceoXv37tx5553MmjWLvn37YjabCQaD9OrViwcffDDNMZ555hlGjhyJoigULFiQvn37UqtWLWJiYvjxxx8jcdpCCCEEAIo9DuutA7HeOhDDd47gvpkEdk0huH9OuIam4TlJYOs3BLZ+A7ZCmBIaoieF6kXqSWvREudhLt8pkqchRL5lGDraiQ0AKDGlUaPiIxzR9Tm6eicntx0AoFClEpRpkT2jbM8r07IW9rhYvKdTOLh8K+6T54gqWjBbjyGEECL3imjStlWrVpE8vBD5zujRaWe2dvkC/GfuBmYzONxWNaEgI+6+Lbz89deh2bN1Xeehhx7ipZdeuuxMjTNmzEizPHz4cIYPH54N0QshhBDZR7EVxHJLPyy39MPwOwnunx1K4O6bBUF3qJPvLNrBi0siqHhXvkp0uewdLSeECDHO7QN/MpD3SyOkH2XbASWbZzs3WcxU6XYbW8YuwNB0/p+9+46v6X4DOP65IzuRJYkQCZKIRBB7r9izRqlNf6raamlV6VBUW1WqRautlqJ2a7W196oZxAySEAnZeyf33nN+f1xuRIIgi37fr5eX3HO/99znnJtx73Oe7/MN/vsE9f4nLioJgiD8V5RpT9t7cnJyOH78OH/99Rfx8fFlHY4gvDAsTIyY1asxM7o3xPTuYmTXY1MYvGwf/1y8VcbRCYIgCELpUBhbYlRzIOY91mM1NhaznhtRew0BtdkDIyWkmAA0QSvLJE5BeNHdvwiZ0sGv7AIpBtFngom7pH8/bV3NCde2dUrkeTx7N0Oh1F9ECv77BJJWtA4UBEH4ryjzpO3ChQtxdnamVatW9OvXjwsXLgAQHx9PxYoV+e2338o4QkF4vikUCvrWq87qUf7UqFgBgCyNjqlbT/Pp1tNk5mrRSTIB4XEcuBFHQHgcOkku46gFQRAEoWQojMwx8uiLWdeVKO18KOztcPbu/5F95jtkSSRHBKE46V6QRcj0VbZ7DLfrlECV7T0WTraGxc2y4lOJOHq5RJ5HEARBKH/KNGm7bNky3n33Xbp27crSpUuR5bxEUcWKFfH392fdunVlGKEgvDg8HKxZM8qfPnWrGbb9ffEWfRbvpNMPWxmz9gizD11jzNojdPtxO3uv3Sm7YAVBEAShhOlu7UaKPQNIhdwrkXvkfTLWN8+3aJIgCM9GFxdo+Pp5TtrGnAtl3uYlTAxYwoh/vyXJNu9zbHBwMC1atKBmzZo0btyYy5cLT7JKksQHH3yAr68vtWrVYvTo0eTm5gL6hX+bNm1K7dq1mTVrFjX7tQDgTmYCg/43rOQPUBAEQSgXyjRpO2/ePF566SXWrFlDr169CtzfsGHDh/6REwThyZkZqfmsRyO+7NUYMyN9u4SY9GwSMnLyjYtNy2LSpuMicSsIgiC8kGRZJvv4pzzurbAUE0DG2sZkH5mCrMksneAE4QUlyzLS3UpbhVlFFJZVyjiip3dh2W4a29dkap1XqOLkjFKV97tk7NixvP7661y/fp0pU6YwatSoQvexZs0azp07x9mzZwkKCkKpVLJgwQIAFi1axLhx47hw4QIrVqzAsmYlLKvYs/rmQQbYNSElLKY0DlMQBEEoY2WatA0JCaFbt24Pvd/Ozo6EhIRSjEgQ/ht6+rqxemQH1MrCF1m5VyswZ0+gaJUgCIIgvHh0uchpERReZXuXQn9xE1lH7pm5pK+qizZ878PHC4LwSHJGJHJWHABKhwbP7WJ/MYGhxJwLpZa1C9U8aqA2NTbcFxsbS0BAAMOG6ath+/fvT0REBCEhIQX2c+XKFTp06ICxsTEKhYJu3bqxcqW+n7aRkRGZmZloNBokSUKlVnPOMp46Nm44mFpzbcux0jlYQRAEoUyVadLWxsbmkQuPXblyhUqVKpViRILw35GYmY32EQlZGYhJy+JsRFzpBSUIgiAIpUChNsFi8CkshgRgMSQAs0GnyOq8E7NBedssRl3DpPlnoNInZOSUG2Ru6kzWrlFIWWLhXEF4Uvn72fqVXSDP6MLyvF62viM65rsvIiICZ2dn1Go1oF9bwtXVlfDw8AL7qVu3Lv/88w+pqaloNBr++OMPwsLCABg/fjybN2+mefPmTJo0iZSUFI6EnKV79aYA3NgRgCYzp8A+BUEQhBeLuiyfvHv37vzyyy+89dZbBe67fPkyv/76K//73//KIDJBePHFp2cX6zhBEARBeJ4oraqCVVUAFJKETCwqR0eU9y0mpGr6KWrPAWTvG4vuzhEANEG/ow3bjkmbbzGqNfS5rRYUhNImvQCLkMVevEl0QDAAVlXsqd6p/lPv65VXXiEpKYm2bdtiZmZGx44d2b17NwDOzs7s2rXLMHbAgAF8t2A+h3/YwMot61ErVDitbkj7Mf2e7YAEQRCEcq1MK22/+OILdDodvr6+TJ06FYVCwYoVKxg2bBiNGjXC0dGRadOmlWWIgvDCqmhpWqRxtxLTSjgSQRAEQSi/VHa1MH/5AKYdFoOxNQByVjzZu0aQuaU7UsrNMo5QEJ4P9y/q97wmbS8uy19lq1Sr8t1ftWpVoqKi0Gq1gL6Pb3h4OK6urgX2pVAomD59OufOnePYsWP4+PhQu3btAuM2btyIu7s7fn5+/HBgPWM8utLOqQ5fzJ6VbyFvQRAE4cVTpknbypUrc+bMGbp27cr69euRZZmVK1fyzz//MHjwYE6cOEHFihXLMkRBeGE1qOqAk5UZj6sP+uloEJM2HychQ1TcCoIgCP9NCoUS4zpjsBx5BbXnAMN23a1dpK/0JefMN8iStgwjFITyTxd3t9LW2AqFdY2yDeYpxF2+ReSpawBYOttRo0vDAmMcHR1p0KABq1atAvQJVxcXFzw8PAqMzc7OJikpCYD4+Hhmz57N5MmT841JTk5mwYIFTJ8+HYAcbS4O3lVRKhSkpqQQdymsOA9REARBKGfKNGkL+j9sS5YsITExkZiYGKKiokhKSuK3337D0dGxrMMThBeWSqlgcic/gMcmbvdcvUO/X3ez/XK4uKJfDowfP55q1aqhUCgIDAw0bA8ODqZFixbUrFmTxo0bc/ny5YfuIygoCH9/f7y9vfH29mbTpk0ABAQE4Ofnh4+PDytWrDCM379/P2PHji2xYxIEQXgeKC2cMe+xHrPef6GwdNFv1GaRc2QyGWuboos5U7YBCkI5JWXF3138D1QO9VEoyvxj6BO7mK+XbQfeHPcWLi4u3L59my5duhgSs4sXL2bx4sXUrFmT2bNns2zZMsPjXnvtNf7++28A0tLSaNWqFbVr16Z169a88cYb9OrVK99zTpkyhRkzZmBmZgbA1KlT+WDfT6y8cYCeVZpwfbNYkEwQBOFFVqY9bbdv306XLl1QqfTTShwcHMoyHEH4z+noVYVv+jVnzp5AYtKyDNudrMyY3LEeWklm9u5zJGXlkpyVy0d/n2LnlQg+6doAJyuzMoz8v+3ll19m8uTJtGrVKt/2sWPH8vrrrzNq1Cg2bNjAqFGjOH36dIHHZ2ZmMmrUKFauXEmbNm3Q6XQkJiYCMHv2bBYuXEjDhg2pU6cOI0eOJCsrixkzZhg+ZAiCIPzXGdXohdqlHTnHPiE3cBEgI8WdI2NdU4zrT8Ck+UwURhZlHaYglBtS7DnD1yoHv7IL5CnFB4Vz53gQABZOttTo2ojFvZoVOtbLy4vjx48Xet+SJUsAkCQJBwcHLl++nK+P9oMWL16c7/aYMWP434hRbOg7k9zUTG4dOE/Dd3pjZmv1NIclCIIglHNleomzZ8+eODk58frrr7Nv3z4kSSrLcAThP6mjVxV2vNWdXwe35sO2Xvw6uDU73upOx1oudPWpyqbXu9DF28Uw/lBIFP1/3c3m8zdF1W0ZadOmDS4uLvm2xcbGEhAQwLBhwwDo378/ERERhISEFHj8mjVraNiwoSHpq1KpDBfNjIyMyMzMJDs723BBbcaMGUyYMAEbG5sSPCpBEITni8LYCtN2CzF/5V+U9r76jbJE7tnvSF9ZB23Yrnzjsw+OJ21pdVLnK/P19tQlBZOxviXpy71IX9sEXULhsyS0tw9i9mcNMtc0JH1VfdJX1UfW6i+46mIC9Nt+r03ulbxZEtqI/WTtFbMkhLKnu28RMuVz2M82X5XtcH9URmVX+6QyMcKjZxMAJI2O0K2nyiwWQRAEoWSVadJ2x44d9O7dmw0bNtC5c2ecnZ0ZN24cR44cKcuwBOE/R6VU0MjVgfY1HGjk6oBKmdcwwc7chDl9mvFtv+ZUtNAvXpaWo2HG9jO8se4IkSkZZRW2cJ+IiAicnZ1Rq/UfIhQKBa6uroSHhxcYGxQUhLGxMb169cLPz48RI0YQFxcHwLRp05g1axadO3dm7ty5BAYGcuPGDfr371+qxyMIgvC8UDs3w2LIGUxafAkqEwDk1DAyt3Qjc8cwpMxY/TiPl7EYeASFlVu+x2fvewMj3zFYjrqGSaPJZO1+9aHPJVvVwHzIGSyHncNy2DkUav2sl5zTX2PabgEWg0+Rc2Kmfqw2i5wTn2Ha6uuSOGxBeCK6uEDD18/bImSJ129z+98rAJg7WuPevckz7U8nyQSEx3HgRhwB4XHopCcvgqj5UnNQ6N+vX//rOJJOFD8JgiC8iMo0adulSxd+++03YmJi+Ouvv+jcuTOrV6+mXbt2uLi48O677z50aokgCKWrg1cVNo3pTO86eR82T4TF0n/JHtafCUUSVbfPDa1Wy5EjR/j55585d+4cVapU4c033wTA29ubw4cPc+bMGXr16sX777/PggULWLt2Lf379+fVV181LJohCIIg6ClURpg0+QiLYRdQubQ3bNdeW0PG7z7kXlmBqkprlFb5Z0lImbHoYgMw8tbPklB79EdOi0BKLjhL4pGURqDNRNZmo1DqZ0nkHJ+Bsd94FKY2z3RsglAcpHuVtipTlHa1yjaYJ3Rh2X1VtkP9URk/fZXt3mt36PbjdsasPcLsQ9cYs/YI3X7czt5rd55oP1ZVKlK5qRcAGdFJRJ64+tQxCYIgCOVXuegAb2RkRM+ePVm5ciWxsbFs2rSJNm3asGTJElq3bl3W4QmCcJe1mTGf92zMooEtDT1tM3O1zNp9jtdWHyI8Mb2MI/zvqlq1KlFRUWi1+tXLZVkmPDwcV1fXAmNdXV1p0aIFVapUQaFQMGzYME6cOFFg3Pz58xkwYAA2NjZ8/vnnrF+/njZt2jB//vySPhxBEITnksrWE/P+ezHttBRMbAGQsxPJ3v0qmZs6FUjGymkRKC2cUSjzZkkorFyR0grOkgBQpN8ic21j0tc2Iff8j4btJs0+Jef0V2Ru7oJJqznoYgORUm9i5ClmSQhlT85JRUoOBkBZsa7h+/15kBgcScSRSwCYVayAR8+mT72vvdfuMGnT8XzrSADEpmUxadPxJ07cevVtafj62uZ/nzouQRAEofwqF0nb+6WnpxMbG0tMTAzZ2dmiZ6YglEOt3J3ZNKYzL9evYdh2JiKeAUv3sOLk9aea5iU8G0dHRxo0aMCqVasA2LhxIy4uLoaVjO83YMAAAgMDSU1NBfSLQtarVy/fmJs3b7Jnzx7Gjh2LRqNBq9WiUChQKpWkp4vkvCAIwsMoFAqMa7+K5YgrqL0GG7brIvaTvrIucm4KsqR94v2qHBqQ1fsM5oNPY95zE7kXF6O5/of+PjtvLAYcwnJIAOoavcg+MgnTtvPRXF1L5taXydr9P+RsMUtCKBu6+POGr1WOfmUXyFO4uCKvyrb2UH9UJkZPtR+dJDNnTyCFvUO+t23OnsAneg9duVktLCrpLw5FnrxG2p34p4pNEARBKL/KRdI2JSWFZcuW0bVrV5ydnRk7dizx8fHMnDmT69evl3V4giAUwtLEiE+7NuCXwW2oYqNfITtbq+Pb/RcYufIAofGpZRzhi2vs2LG4uLhw+/ZtunTpYkjMLl68mMWLF1OzZk1mz57NsmXLDI957bXX+PvvvwF9pe348eNp1aoVdevWZf/+/fz888/5nmPChAnMnz8fhUKBtbU1Q4YMoU6dOixatIi333679A5WEAThOaW0cMK822rMXtqKwururAddNuQkk7VzKLroUyisqiJlRBmSuLIsI6eFo7QqOEtCYVIBjCvo923lglHNQWjvFFwHIvfcfIw8X0ZhYkPOqS8w674OVZXW5JybX2LHKgiPcv8iZM9TP9ukG1GEH7wAgJm9FZ69mz31vs5GxBWosL2fDMSkZXE2Iq7I+1SqlPretgCyzPUtoq2gIAjCi6ZM56asXLmSP/74gz179pCbm0utWrX4+OOPeeWVV6hV6/nqdSQI/1VNqzmyYXQnfjh0iTUBIcjAxchEXvltL2NbejOqmRdGqnJxfeiFsXjx4kK3e3l5PbQP+JIlS/LdHjBgAOPGjUOpLPy1uZfgvWfGjBnMmDHjyYMVBEH4jzOq3h318EvkHJ9GbuBCkCXk5GAy1jXH2O8dVBXroQlahXHtUWhDNqKwdEFpU3CWhJQRBbJ+4SE5Nw3tzW0Y+f4v/5iUm2jD92LeZzvkpoKkBRSgUIJGzJIQyoYUG2j4+nlK2l5csdfwtc+Q9qifssoWID49u1jH3ePRsynnf9uFpNERsu0U9V7r+kxxCoIgCOVLmWZSRo4cSVBQEO+//z6BgYFcuXKF6dOni4StIDxnzI3VTO7kx7Lh7XCzswRAo5P44fBlhq7Yz9WYZADGjx9PtWrVUCgUBAYGGh4fHBxMixYtqFmzJo0bN+by5cuFPs/+/fvp1q0bvr6+1K5dm8mTJyNJ+tVyb968SdOmTalduzazZs0yPCYoKIjevXuXzIELgiAIQhEojC2RNRkoTO3v2yqTG7gQXUoIOQFfk77ci5zTX2PW+TfDiKw9r6EJ1V9E04ZswnSHP5lrGpCxrjkq144Y+bya73myD72Ladvv9L1xTaxRew0mY1Vdcs//iHE9MUtCKBu6uLuVtgoVSnvfsg2miJJvRnNrv76tg6mtZV5F61OqaGlarOPuMbW1xM3fD4Dc1Exu7Q98wsgEQRCE8qxMk7anT58mJCSEL7/8krp165ZlKIIgFIP6LhX543+deLWZF0p9MRDXYpIZunwfiw5f5qW+fTl69Chubm75Hjd27Fhef/11rl+/zpQpUxg1alSh+7e1teXnn3/m0qVLnDlzhmPHjvH7778DsGjRIsaNG8eFCxdYsWIFaWlpyLLMu+++y4IFC0rysJ8rOkkmIDyOAzfiCAiPE/2HBUEQSolZx8VYjY3B6p0cTFrNBtXd5ExGNHLSNZQO9TF/6R9UFevkPabTEozc9RcejeuNI7v7QcyHnMVyxCVMm89AoVDkew7z3n+hsssrfjBtPgPLEZexHHwSpXX1kj9IQXiArM1GSrgCgNK+Ngr1kyUly8rF3/fC3bVVfAa3Q21q/NT7kmSZwNsJjx1X0cKEBlUdnnj/Xn1bGL6+tunYEz9eEARBKL/KNGnbsGHDsnx6QRBKgKmRinfb12HlCH88HPS997SSzC//BrEoREOS0jzf+NjYWAICAhg2bBgA/fv3JyIigpCQkAL7rl+/viHha2pqip+fH2FhYQAYGRmRmZmJRqNBkiSUSiU///wznTt3pnp18UEV9KsWd/txO2PWHmH2oWuMWXuEbj9uf+LVigVBEISnp1AZYdJoMpbDL6Jy7WjYrg3+g/Tffci9tFQsxCu8MKT4iyDrAFA51C/jaIomJTyWW/sCATCxsaBmnxaPfsAjpGVrmLjxOD8cLnwW2f1kGVKzc5/4OSrWdsPWszIACUHhJFyNeOJ9lKVnnYkXERGBv78/1tbW+Pn55bsvICAAPz8/fHx8WLFihWH7/v37GTt2bEkcjiAIQrEq80aTQUFBTJ48mZdffpkOHTrg7++f71+HDh3KOkRBEJ6Cb2U71r3akTdaeaO+W3YbGp/KiN/3k5qtIUejfwMfERGBs7MzarW+xbZCocDV1ZXw8PBH7j86OpoNGzbQs2dPQP+Gb/PmzTRv3pxJkyaRkpLChg0bePfdd0vuIJ8je6/dYdKm4wUWwYhNy2LSpuMicSsIglDKlDbumPfdhWnn5XltE3KSyd47hswN/ugSr5VtgIJQDO5fhEzp+HwkbS+t2It8dyaSzyttMTI3ear9BMemMGT5Pg4ERwKgALp4u+BkZZZv3L33yQmZOby38Ri5Wt0TPY9CocCrb0vD7Wubn69q25dffvmZZuJZWloyc+ZM1qxZU+C+2bNns3DhQk6fPs1nn30GQFZWFjNmzODrr78u9mMRBEEobmWatF25ciV16tTh+++/JyQkBEmS9Kvm3vfvXr9KQRCeP0YqJW+2rs2aUR3wrmQDgCRDeo6GD7ac4GxE/FPtNzU1lV69ejF58mQaNWoEgLOzM7t27eLcuXOMHTuWCRMmMG/ePA4cOED//v0ZMmQIt27dKq5De67oJJk5ewIprG7r3rY5ewJFqwRBEIRSplAoMPYZgcWIKxjVGmbYrrtziIzVfuSc/AJZp6+804bvxXR7W7Thex+2O0Eod3Rx5wxfPw+LkKXejufmHn2i2biCOV79Wz7mEYXbfjmcYb/vJzxJvwBgBVMjfhjYkjl9mrHjre78Org1H7b14tfBrfl7bFcc7vayPXc7gRnbzzxxtX21TvUxstDvI2zPWXJSM58q7rLQpk0bXFxc8m17kpl4tra2tGrVCgsLiwL33ZuJl52djUqlAvSL606YMAEbG5viPxhBEIRipi7LJ58xYwb169dnx44dVKxYsSxDEQShBHk52bBqpD8rTl7npyP6vmbRqZn8b9VBerrbERUVhVarRa1WI8sy4eHhuLq6Frqv9PR0+vXrx0svvcTEiRMLHbNx40bc3d3x8/PD29ubU6dOERAQwLRp0/JNjfqvOBsRV6DC9n4yEJOWxdmIOBq7OZZeYIIgCAIASnMHzLr+jlGtoWTtfxM5NQx0OeQcn4bm+npM/BeTe2wqytRgco9NxcitU4F+tiVBG7aT7GOfgpSLQm2OaYefUTnUKzAuJ2AumqDfkSUJY3M35J6rwMwOOTuJzK39kbPiUVVphZn/jwBImXFkbRuIeb/dKFRipfsXmS72XtJWUej3Tnlz6fcHq2yfrAevRicxb98F1p7JSy7WcrJhXr/muNjok4oqpYJGrg64mso4OjqgVCpZ8HIL/rfqENlaHdsuh1PN3orXW3oX+XmNzExw79aYqxuOoMvVErrjND6vtH2i2MuTR83E8/DwKPJ+pk2bxtixY8nIyGDu3LkEBgZy48YNUWUrCMJzo0wrbSMjI/nf//4nEraC8B+gVioZ3bwWf/yvI8Yq/a8eGfgnNBF1pWp8Nn8RoE+4uri4FPqGLD09nSFDhtClSxemTp1a6PMkJyezYMECpk+fDkBmZiZKpRKlUkl6enrJHFw5Jssy+64XrfVBcFxqCUcjCIIgPIq6Whcsh1/EuOH7oND/rZQSLpP1Zyuk2AD97dgAdLd2l3gscnYSWTuHYdZlOZbDzmPSeg5ZO4cVGKe9tQfNleVYvHIMi+EXkezqknvsUwA0V1ejdmmH5fALSInX0MVfAiDn8PuYtPpKJGxfcLJOgxR/AQClrScKY6syjujR0u4kcGPXGQCMLc3wernVEz0+Ni2L11YfypewfaluNVYMb29I2D5MbWc7vuzdxHB70eHL7Ap6st60Nfs2N3x9ffMxZDFjFW9vbw4fPsyZM2fo1asX77//PgsWLGDt2rX079+fV199laSkpLIOUxAE4aHKNGlbt25dIiMjyzIEQRBK0dixY2nj50N2cjwxq7/m2rx3AHDoOZpvFy6iYhU3Zn31FcuWLTM85rXXXuPvv/8GYOHChZw7d47Nmzfj5+eHn58fX375Zb7nmDJlCjNmzMDMTN8vbOrUqTRq1Ijx48fz0UcfldKRlg9XY5IZvfoQawNCizR+zp5A3tt4jJNhMWIRHEEQhDKiMLLAtPVcLAafQvmQ6eRZ+8aiS75RonFIKaEoTO1R2dcGQF2lNVJaeL4epQC6+POoKrc0JOR0zv5orq7S36k0QtZmIssS6HJAZYw2bCcKUxvUzs1KNH6h7ElJV/WvO6B8DhYhu7RyL7JOn+j0fqUNxhZFr7INCI9j0LK9BN5JAPQtwqZ1a8Bn3RtiaqQq0j46elVhfDtfw+1Pt57mwt39FYW1mxOVGuqLHtJuxxN1JrjIjy1vqlatapiJBzx2Jl5RzJ8/nwEDBmBjY8Pnn3/O+vXradOmDfPnzy+mqAVBEIpfmbZH+PbbbxkwYADdunWjRYunX5VTEITnw+LFi/PdDk9M57MdAQQAHm/OAsDeyowks7zq+yVLlhi+/vjjj3nttddwdHREqSz8mtODzzFmzBjGjBlTTEfwfEjMzGHR4ctsCrzBk7SplYH91yPZfz2SGvZWvNLQnZ6+bliaPHsl1M6dO5k6dSq5ubmYm5uzePFi6tXLP01y165dTJkyBQCtVktiYiKVKlXi7NmzJCUl0a9fP+Lj42ndujU//qifYhsXF8eAAQPYs2cPRkaiYksQhBeHyrEBFoNOkL1/HJpLv+a7T04LJ2O5B0o7b9TVu6Ou3hOVc4tirVxV2ngiZyegjTyGunILNKF/Q24aUmpYvt6kKseG5J7/CSkjGtnUAfWtzaBJQ85OxMh7GFm7RpGxugFq95dQWlYhc89rmPfZVmxxCuXX/Ql+VTlfhCw9KpHQHfpqdiNLU2q93LpIj5Nlmd9PBbPgwEV0dy94V6pgxry+zfGtbPfEcfyvmRe3EtP560IYOVqJCRuOsXqUP5WtH12pe0/Nvi2Jvlvpe33TMSo39nriGMoDR0dHGjRowKpVqxg1atQjZ+IVxc2bN9mzZw87duwgNTUVrVaLQqH4z87EEwTh+VGmSduvv/4aa2trWrdujY+PD66uroYG4fcoFAr++uuvMopQEISS5Gpnya9D2rLh3A2+O3CRzFwtsWlZvPPnv/T0dWVyRz+szYzLOsznhkYn8cfZUH46eoW0bI1hu6utJR1rVWHZcf1K5PfncRV3b3fxduFsRDxx6dkA3EhI46vdgSw4eIlevm4MauhOjYoVniqupKQkhg4dyuHDh6lduzZHjhxh6NChXLp0Kd+4Ll260KVLFyRJIjY2ltGjR+Pv7w/A6tWrad++PdOmTcPf359Lly7h6+vLxIkTmT17tkjYCoLwYlKo9As5KZQgF5zqLCUGkZsYRO6ZeWBsjdqtC+oaPVBX64bS7NnajylMrDHr8Sc5/35MtiYdlXMzlHY+oMj/8UFdtT0mDd8n869eoFAiO3W8uwM1CiMLzHv+aRibfeg9jBtNRkoOIef0VwCYNPnkueh1Kjw5Kfb5WYTs0qr9eVW2A1pjbGX22Mdk5GiYvj2APVfz2lA1q+bI7JeaYmtu8lRxKBQKPu3agDvJGQSEx5GYmcM7f/7LiuHti3QRvWqr2phVrEBWfCq3/71MRkwSFk62TxVLaRk7dizbtm0jOjqaLl26YGVlRUhICIsXL2bUqFHMmjWLChUqFJiJ17t3b3r27ElmZiaurq7k5OSQkpKCi4sLw4cP56uvvjKMnzBhAvPnz0ehUGBtbc2QIUOoU6cOlpaWrF+/viwOWxAEoUjKNGl74cIFQ1Px9PR0rly5UmBMaSyyIAhC2VEqFAxs4E5rd2dm7jjDsZsxAGy9FM7xmzF83Lk+HWu5oJNkzoTHERoZh3u2goaujqiU4vfDPcduRDN373luJKQZtpkbq3m9pTdDG3lgrFZR29mOOXsC8y1K5mhlxuROfnT0qoJGJ3HgeiTrzoRwJiIegMxcLevPhrL+bChN3BwY1NCDtp7OqB9S6VyY0NBQ7O3tqV1bP8W2devWhIeHc/bsWRo0KPxDXHR0NPv37ze8Qb+3+q8kSeTk5GBsbMzOnTuxtbWlWTMxxVYQhBeT7tZupJiAog3OTUEb/Afa4D8ABapKTe8mcHugdKj3VO+p1VXbo67aHgBZm0Par86o7H0KjDOu9xbG9d5CkiQyr+xAYemCwiT/hT5d9CmkzDhMa/Qk4482mHX5HZDJ2v0qFgMOPnFsQvmni8tL2pbn9ggZ0UmEbjsFgJG5CbUGtHnsY27EpzJx03Fu3ve+a0yLWrzZuvYzvz81Uin5tl9zhq3YT3hSOiFxqUzZcpIFA1o89v2XUq3Cs3czLvy2G1mSCf77BH5juj1TPCXtwVly93h5eXH8+PFC77s3E0+SJMzNzQkPD3/oLDzA0GrtnhkzZjBjxoynC1gQBKEUlWnSNiwsrCyfXhCEcsTZ2pwfX2nF3xdvMXffedKyNSRk5PD+5hPUrWJHVEqmoQoUruF0X7Lxvyw8MZ15+89zMDgq3/beddyY0K4OFS3z+rF19KpCe8/KnAmPJTQyFvfKjvmS30YqJZ29Xejs7UJwbArrzoay9dItsjU6AE7diuPUrTgqVTBjQP0a9K1XHfsi9Hvz9PQkISGBY8eO0aJFC/7++2/S0tIICwt7aNJ2/fr1dOvWDUdHRwCGDRvGyJEjqV+/Pn369KFKlSqMHj2a7du3P9V5EwRBKO9kWSb7+Kfol6AobEEhJUqHuhjVfw9d2Ha0t3ZBTvK9R6OLPoEu+gQ5xz5FYVkFdbXuqKv3QO3aAYVR0aZaSxlRKC2cAcg59Tnqqv4obQpOT743TtZkYnRxLkYNJ+U/Fp2G7KMfYtZtrf62JgMUCkCBrBFTk19EsiyhiwsEQGHlitLMvmwDeoRLq/cjafXvdbxebo1JBfNHjt9z9TbTtgWQmavvt2ppoubLXk1o51m52GKyNjPmh4EtGbZiP6nZGo7eiOabvRf4sLPfYx/r2bsZF1fo+/MG/3OSOqM6oTIq04/9glBAUVqnAYSHh/PWW29x9epVjI2NefPNN3nnnXe4efMmgwYNIj09naFDh/Lxxx8DEBQUxJQpUwok6gXheSV+ewuCUG4oFApeqluN5tWd+HLXWUMi8sKdxAJjY9OymLTpON/0a/6fTNxm5Gj49dhVVp0ORqPL+zBft7IdUzr5PbSPmkqpoJGrA66mMo6ODigfUg3i6WjNp10bMKGdL39fuMX6s6GEJ+k/WEenZvH9ocv8fDSILt4uDGroQZ1H9G2ztrZmw4YNfPTRR6Snp9O8eXN8fHxQqwv/EyTLMuvWreP77783bLOwsGDDhg2G2++99x5TpkwhJCSEWbP0/ZCnTp1a6Js9QRCE55IuFzktgsITtgASckY0xjUHovAZjixp0UUeQ3tzG9qb25AS82awyel30Fz6Vd8bV2WCyqU9RtX1SVyldfWHhpBzfBq6O0eRJS1q5+aYddRXt2Ufn4bSojLGdd8AIHNTF5AlZF0uuqp9MKo7Lt9+cs/Mxch7OEoLJwBMmn9G5pYe+q9bz3nKEySUZ1JyKOTqq1BV5bnKNjaZkK0nAVCbGePzysOrbLWSxIIDF/n9VN4CX54O1nzbrzmudpbFHpubnRXf9mvOG+uOoJVk1p4JoZq9JYMaPrqvq3lFa1zb1OHWgfNkJ6YRfugi1TuW39fgQUVJ5oWFheHu7o67lze5Gi3GRmq2/rWFmp4eIpn3HChq6zRZlunbty+TJ0+mbdu2ODo6EhcXB8CiRYsYN24cQ4cOxcfHh3feeQdLS0veffddfv7557I4LEEoEeUiaXvo0CG2bdvGrVu3AHBzc6NHjx60bdv2qfZ3+PBh5s6dy5kzZ4iKimLz5s306dPHcL8sy0yfPp1ff/2V5ORkWrZsyU8//YSnp2dxHI4gCM/I0cqM+f1bsP1KBJ/8fYrC1tKS0fdjnbMnkPaelf8zrRIkWWbrxVssOHiJ+Ixsw3YHS1MmtKtDD19XlMXYVqaCqTHDmngypLEHx2/GsP5MKIdDopDR99DdeimcrZfCqV3JlkEN3eniUxUTdcFVktu3b0/79voptjk5OVSqVAkfn4JTbEH/NyEnJ4cuXboUev+pU6eIjY2lZ8+etG7dmpUrVyLLMqNGjeLQoUPFduyCIAhlSaE2wWLwKeQs/QdUSZJITEzEzs7OMA1YYeaIQq3vnalQqlG7tEHt0gZaf42UEoY2bBuaG9vQ3T4Auhz9jnU56G7tRHdrJxwcf3cxsx6oq/cosJiZWcf8C6DdY9p8Zr7blsMvGGLMiI0t0IrBpMnH+W4b1eiJUY2eT3lmhOeB9JwsQnZ5zQGkuzOKvPq1xOQhC34lZGQzecsJAsLjDdt61Hbl024NMCvBKtbGbo582q0h07fp26R8vSeQqjaWtHSv9MjH1ezbglsHzgNwffOx5yZpW9Rk3tHQaJQmZpgNn8G97sNv77rGZMmMnb+KZF55V9TWafv27cPExIQBAwYQGxsLgJOT/uLfvdZpGo0GSZJQKpX8/PPPdO7cmerVH34xUhCeN2WatM3NzWXw4MFs2bIFWZaxsbEBIDk5mXnz5tG3b1/Wrl37xAvMZGRkUK9ePf73v//Rr1+/AvfPmTOHhQsXsmLFCqpXr86nn35Kly5duHLlCqamj5/qKwhCyVMoFDhamhaasL1HBmLSsjgTHkeTao6lFVqZuXAnga/3BHIpKsmwzUilZGTTmoxuXgtz45L7la5UKGhZoxIta1TidnIGf54NZfP5MFKycwG4HJ3Ep9sCmLf/An3rVWdA/RpUscn74BMVFYWzs36K7eeff46/v/9DVwD+7bffGDhwYIGFKQE0Gg1Tpkxh3bp1gP73vUKhQKFQiNV/BUF44SitqoJVVQAUkoRMLCpHx0f2bjQ81roaxvXGYVxvHLImA23EfkMVrpyet3BS3mJm3+gXM6vWFXX17sWymJnw35Wvn205XYQsMz6F4L9PAKAyNcZnULtCxwXejmfS5hOGNl1qpYIPOtbjlQbupbL+Sp+61QhLSGPZiWtIMnyw5QQrRrTH08H6oY9xqu+OdTUnUsJiiD1/g6TQKGzdnUs81mdVlGTe3mt3mLXrLJKc/1PCvVl43qnZeIhkXrlW1NZpV65cwcHBgcGDB3PlyhXc3d359ttvqVGjBuPHj2fUqFEsXryYSZMmkZKSwoYNG9i9e3cZHpkgFL+iryRTAj777DM2b97M+++/T1RUFImJiSQmJhIdHc2kSZPYtGkTM2fOfPyOHtCtWze++OIL+vbtW+A+WZaZP38+U6dO5aWXXqJu3br8/vvvREZGsmXLlmI4KkEQikt8evbjBwGfbD3Nb8evEpOaWewx7Ny5k0aNGlG3bl2aNWvG+fPnHzn+1VdfxdnZmeTkZEBfMdC+fXvq1KnDW2+9ZRgXFxdHu3bt0Gg0j40hNi2LT/45xfDfD+RL2LavWZktYzrzTlvfEk3YPsjFxoL3/Ouy++0efNajEd6VbAz3JWflsuzENXr+vIMJG/7l+M0YZFlm2rRp1KpVCw8PD27dusXSpUsBmDZtWr6qh5SUFDZv3szgwYMLfe65c+cyYsQIw1X2mTNn0r17d7p3787nn39ecgctCILwHFMYWWBUoxdmHX7GcnQ4FkPPYdLiC1TOLdDPW7krNwXt9fVk7xpJ+mInMta3JOfUl+hiA5HlR11GFYT8dLF5SdvyWml7ec1BpLt9ab36tsDUNn+LA1mWWRsQwujVhwwJWwdLU34b2o5BDT1KdcHs8e186XC3HVhGrpbxf/5LQsbD3ycrFApq9m1huH1987ESj7E43J/MA/Il8wB0ksycPYHIgJSbQ/CiD7n+w2Ri9v2JJOkrpiPdmrJp02aaN2+eL5n37rvvls1BCQXc3zqtYcOG7N69u9DWaVqtlv379zN16lT27NlD586dGThwIADOzs7s2rWLc+fOMXbsWCZMmMC8efM4cOAA/fv3Z8iQIYaZ3ILwPCvTSts1a9YwcuRI5szJ38vK0dGRr7/+mpiYGFauXFmsH8Rv3rxJdHQ0HTt2NGyztramadOmHD9+nEGDBhXbcwmC8GzuX0TrUWLTslhw8BILD16iaTVHevq60cGryjMnMos6ReueTZs2FZgZsHr1atq3b8+0adPw9/fn0qVL+Pr6MnHiRGbPnv3ImQQ5Wh2rTgfz679BZN2dugfgXrECkzvWo1l1p2c6vmdlaqSiT91qvFTHjYuRiaw9E8ruoAi0kowkw8HgKA4GR1HNzoqBr0/m2+9/xMo0//E+eGHO2tqatLQ0wxSoB93rS3ZPz5496dlTTLEVBEEoKoVCgcqhHiqHepg0+RgpKx5t2E60N7ejvbUz/2JmUcfRRR2/u5iZC+pq3R65mJk2fC+m+99B6/89xtU6l+pxCeWHLMtIdyttFeaOKCyefYGuovQ5vXnzJi+//DI6nQ6tVkv16tVZvnw59vb2Bfqcvjf2bYK3HONOZgJ/RvzL0cEz8u0rM1fL5zvPsv1yuGFbI1cH5vRpWqRFWIubUqHgy16NiUrJ5Ep0EpEpmUzYcIwlQ9pialRwZhJAjS4NOffzNrRZudzYdYb6b/bAuAxifxKPWwfhbEQcMWlZqK1s8flwMWpLa7SZaYSv+464o1txbPMSyUozlvy6ksZu+ll4AwYMMCTzfvrpJ0xMTPjqq69wc3Mry0P9zytK6zRXV1fq169P7dq1iY2NZfjw4bz99ttoNJp8n6E2btyIu7s7fn5+eHt7c+rUKQICApg2bRorVqwo1eMShOJWpknbqKgomjZt+tD7mzZtapgCW1yio6OBvF4o9zg5ORnuK0xOTg45OTmG26mpqYC+d5ckPWyBiBeDJEn6N1/l+DjLe4zlPT4onzH6VbHHycqM2LSsh7ZJMFIpDQtxycCJsFhOhMXy5a6z+NesQk9fVxq7OjxVz9vg4GDs7e3x9vZGkiRatmxJeHg4AQEB+abuAMTExDBr1iz27NnD0qVLDb8bVCoVGRkZaLVacnJyUKvVbN++HRsbG5o0aVLo+ZZlmYMhUXy7/yK3kzMM2yuYGvFmKx9erl8dtVL51K9VSbzWvs62fNmzERPb+7L5fBh/nrtB7N2qlLDENObsPc/3hy7R09eVV+q74+5QoVTjK24ixuIhYnx25T0+EDEWlxKL0cQOtdcQ1F5DkCUtUtQxtGHb0d3cgZR42TBMTr/9wGJm7VBV6466WneU1tWRZZmcY5+gTA0m59gnqKp2KNVKxKL4T7/OxehxMUppEchZ+t6vyop+yLL8TJXa9y6iHzx4MN9F9AsXLuQbV6lSJQ4fPoyZmRmSJPHGG28wY8YMFixYwA8//MCbb77J0KFD8fX1paVRDbQ5GlbfPMjn4z/CxMbCcDy3EtOZtPkEIfGphn2PbOLJ221rP9P7r/s9zetsolLyXb9mjFh5kJi0LC5GJvLp1tN81btxoesZqM1NqNapASF/n0CblcONnQH5qm9LIsbi0LZtWw4cOADoP4NXrlyZWrVqIUkSsWlZACjVRigt9e0h1OZW2Db0J/n8UWjzEqAv6JAkiY0bN1KjRg3q1q1L7dq1OXHiBAEBAXz66acsX768VI7nRfiZLgn3t06bOXMm7du3p0aNGvli6NKlC5MnTyYiIgJjY2O2bduGt7c3KpXKMC45OZkFCxawY8cOJEkiMzNv5mVaWlqpHZN4nYvH8xBjcSnqMZZp0tbFxYWDBw/yxhtvFHr/oUOHcHFxKeWoCvfVV1/x2WefFdgeFxdHdnbRpnA/ryRJIiUlBVmWi9RDrSyU9xjLe3xQfmN8vZEbnx+4+tD7P2xTE3d7S/aFxrI3JJaoNP3PY5ZGx7bL4Wy7HE5Fc2P83R3p4O5ANdvCF5gojI2NDfHx8Wzbto3GjRuza9cu0tLSOH/+fIHfTaNGjeLDDz8kK0v/ZjIuLg6tVkuXLl0YP3489erVo2vXrhgbG/PZZ5+xevXqQqtJw5IyWHzqJmcjkw3blAro7lWJEfXdsDY1IjE+vsDjnkRJv9a9PezoUcOWY+EJ/BMUxfnoFED/mvx57iZ/nrtJ3UrW9PZ2poWrfb6Euk6SuRidzO2EFFzsk6lTyaZcLjJXXn9e7idiLB7lPcbyHh+IGItLqcVoVAs8a4HnRBTpEaii9qKK3Icy5l8U0v2Lme1Cd2sXuYcmIFWoiVTBE3XsGQDk2DMkXPgTybldycX5FMTrXDweF6Pq9gFM7n6dbeFF2kNmzxRVYGAgNjY2ODg4EBsbi5eXF7du3WLv3r3UrVu3wPi0tDQ0Go2hVVVsbCy5ubnExMRw584dcnNyCNl2iiPRF6hrX536gzoY3pMdu5XA3CPXybw7w8lMreL91p60rlbxmd9/3e9ZXufp7b2YuP0C2VqJ3VdvU9FEwcgGhVeNVmzjRcjdvr1XNhzBukXR+/CW1fdiTEyMocBq9uzZtGjRggoVKhAVHcP+K2EAaNNTUJlZoFCpkbQaUi+fxMy5mmEfN6LiCDbKZt68eaxdu5bY2FjS0tJISEggJSWFhISEh87qKm4vws90SZg8eTInT55Ep9PRsGFDvvrqK2JjY5kzZw5OTk6MHDkSgFmzZtG9e3d0Oh02Njb88MMP+V67Dz74gPHjx5OWlkZaWhrvvPMODRo0wNjYmHnz5onX+T4ixvIlLS2tSOPKNGk7cuRIpk+fjo2NDe+99x4eHvreQMHBwcyfP58///yz0ETps6hUSb/SZkxMjOHKzr3bfn5+D33cRx99xMSJEw23U1NTqVq1Kg4ODlSo8PCKsReBJEkoFAocHBzK7Q9OeY+xvMcH5TfGfo6OWFtbM3ffBWLuXl0HcLIy44MOdQ39veq5u/JeJ5nzdxLZejmc3UG3ScvR94uNz8zlj4u3+ePibbwr2dCztitdvV2we8wUMUdHR/78809mzpxJeno6zZo1w8fHB3t7exwd8xY+W7JkCR4eHvTr189wxczBwQE7OztA34/rnokTJ/LJJ5+QkpLChx9+CMAnn3xCdS9vfjoaxJ9nb6C7rxqlkWtFPuhQj5qOD19s4kmV1mvdv5IT/Zv4EBqXyvpzoWy9FG5o83AhOoUL0Sk4WZnR3686/epVI/B2wgOv8+0Cr3N5UV5/Xu4nYiwe5T3G8h4fiBiLS5nE6OgINRoCU5A1Gegi9qMN24EubDty+m3DMGXqdZSp1/Mep1BiHvQtZnUHlKtqW/E6F4/HxZhz4yb3OvZbVWuF2vHZFott2rQpycnJhISEGBYtSk9PJzU1Nd/7MdAvdN2sWTNu3bpFrVq1WLx4Mba2tnz44Ye8+uqrrFu3jsHNe5ARksHphOss+fRbqnrVQCfJ/HjkMr+dyPs+rm5vxby+zahub/VM8RfmWV5nR0dHZhuZ8d7G48jAmvMR+FR1okdt10LHhtapRtzFMDIi4iEqA0e/GiUe47OYOnUqR48eRavV0qxZM1auXIlWbUr3UW8SK5lg37QzGWFXid67HoVSiSzpsKzhi2P7/oZ9LAkIY8W3a5gxfYahDcKnn35Kjx49MDY25tdffy3wvVNSXoSf6ZKwcuXKQrd/8803+W4PHDiQl19+mbi4uELje7D9wcSJE/PlbUqLeJ2Lx/MQY3ExNS1au5oyTdp+/PHHhIaG8ssvv/Drr78aXpR7JdEjR44s0L/wWVWvXp1KlSqxb98+Q5I2NTWVkydP8uabbz70cSYmJpiYmBTYrlQqX/hvJtD3Pyvvx1reYyzv8UH5jbGTd1X8vVw4Ex5LaGQs7pUdaejqWGgFZgNXBxq4OjClkx+HQ6LYeukWR0Oj0Ur6RGhQdDJB0cl8u/8iLd0r0cvXjbaezpioC+8H1qFDBzp06ADk9Vvy9fXNd44OHTrE4cOH2bZtW14cDRrw119/Ub9+3uIbp06dIi4ujt69e9O6dWv9m1CdRO+Bg3AYPpXkrFzD2MrW5rzvr09WlsQH3tJ8rT2dbJjatSET2tXln0u3WH8mlLBE/ZXFmLQsfjxyhZ+PXkEqZOZkbFoWH2w5yTf9mtOxnCVuy+vPy/1EjMWjvMdY3uMDEWNxKdMYTaxQebyEscdL+qmL8RfQ3tyG9uZ2dFHH4f5GRrKEFBuAdGsnRjV6lH6sjyBe5+LxqBjl+EDD12qnBs98HLa2tmzYsIFPPvkkX59TY2PjAvs2NTUlMDCQ7OxsxowZw5IlS5gyZQpVqlRh9+7dZCens3nAl3x3cyNDPdqT4GbES/36ExSXhlHL/hjbOgDQ2duFz7o3KtGFXp/ldW5fswrvd6jLN/v0LSI+23GWKjaWNKhascBYr74tibsYBkDwX8dxbuBRKjE+rSVLluS7fTIslo/+3oeucS/s0a+kbu3bFBvfpg9tnwagaTOEH25IVLwVR7PqTowdO5axY8eWZOgP9bz/TJcH5T0+EDEWl+chxuJQ1OMr06StSqVi+fLlTJw4ke3btxtW93Nzc6N79+6FTncpivT0dEJCQgy3b968SWBgIHZ2dri6uvLuu+/yxRdf4OnpSfXq1fn000+pXLkyffr0KY7DEgShBKiUChq5OuBqKuPo6IDyMVPmTdQqOtVyoVMtFxIzc9h5JYKtF29xOToJAJ0sczgkisMhUViZGNHZ24Wevm7Ud7HPlyS9v9/S559/jr+/Px4e+d/srl692vD1vT62937n3KPRaJgyZYqhT3dGRgYX7iTw879BhEXHY3Q3YWtqpGJ081qMaFLzoQtLPK+sTI0Y0siDwQ3dORkWy7ozoRwKiUSSKTRhC/oUgAKYsyeQ9p6Vy2WrBEEQhP+a+xczM278ERlrGiLFXQDy92fL2jYQxcsHUDs3KZtAhSIpyiJf6enp9O/fnzNnzqDVarl6Na911YOLfL3jqF+E7FqiJZ+PeC/fjKOnlZOTQ0ZGBhqNhtOnTxMREVFg0aIHY1y9ejUffvghU6ZMMcQYHxGNo8YMR1MbOg55iX7vTyDHsiIVmvckZu96qg18h/f86zCssWe5qhIvzLDGnoQlprPh3A00OomJG4+xaqQ/LraW+ca5tquLycK/yElOJ/zgBbISUjGzL/8zRXWSzK/Hglh834V9B0tT5vRpSmJmLnP2BBachdexHokZOXx34AJZGh1RqZmMXXeEAfVr8F77OliYPHwBYKF80kkyZ8LjCI2Mwz1b8dDCHUF4UZVp0vaeunXrPnWCtjABAQGGlQgBQ3n8yJEjWb58OZMnTyYjI4PXX3+d5ORkWrVqxc6dO4tcniwIwvPFztyEIY08GNLIg9D4VLZeusW2S+GGN3ppORo2Bt5kY+BNXGws6OnrSk9fN6raWjJt2jSOHDmCVqulefPmLF26FIBp06ZRuXLlh/bkftDcuXMZMWIETk5ORKZk4NZtCAP79QHAudtwALr7VOXd9nVwqmBe/CehHFEoFDSr7kSz6vpzsfDgJXZciXjoeBl9Re7ZiDjDSsCCIAhC+aC7tRspLvAhd2aRub45xg0nYtJ8Jgq1WanGJjzevUW+Dh8+nG+Rr0uXLuUbZ2RkxJQpU7Czs6Ndu3b57lu0aBHjxo1j6NCh+Hh7MfJ/EViawEd/q/l184JiiXHIkCEcOXKE2rVrM2LECK5evVrgInpkZCQTJkygcuXKtGvXjn/++Yc6deoYYnz9f68hrb3MuCOLmN/kdW7Wq0FkZCSe48ajzUhDrcvl1yFtaOjq8MwxlwaFQsGHnfy4nZTOibBYkrJyefvPf/l9RHsqmBobxqmM1Xj2asqllfuQdRLB/5yk7qhOZRj54yVkZPPx36c4EZbXj7R5dSe+7NUY07QssslhdYe6BEUncjsuCRcHW7wr2aFUgGkVO1q+1pnp2wIICI8D4M9zN/j3RjQzujeiaTXxXvJ5sffanQeS89dwsjJjcie/cjcDTxBKSqknbbOzs3n33XepXbs277zzzkPHLVy4kKCgIBYuXIiR0ZNdEWvXrt0jVyhVKBTMnDmTmTNnPtF+BUF4/rlXrMCEdnV4u40vAeGxbL0Uzp6rtw29Vm8nZ/Dz0SB+PhqEn4s9vd76iHkLF1HBzDjffh71+yMqKgobG5t82z7++GMyc7UsOnyZFSevkWNZDa93vwPAu5INUzr5Ud+l4JS2F11lawvaejg/Mml7z6lbcTRydSj3lS+CIAj/FbIsk338U/QTlh+2CrJM7pl5aEP/xrTTEtRVWpdihMLjhIaGYm9vT+3atQFo3bo14eHhnD17lgYNGhjGmZiY4O/vT1hYWIF9GBkZkZmZiUajQdJkoVTA0mPQsUUdqlevXiwx6nQ6+vfvb7iILssyZ8+eZcuWLYaL6NeuXeOTTz4hNzfXsOjUjz/+aIjxxpELHA06gKXalMy6HsxauAirWg25teZbzE1NWLdyxXOTsL3HSKVkbt9mjPz9ADcS0riZkMYHm0/ww8BWGKnypt56vtSMy6v3I0sywX8fx3eYP8qHtAYrawHhcXz410ni0vWLCysV8Fbr2oxuUYusmGS2DJmNlKvN95hbd/8BKI3V9FnzIb8OacMfZ0P57sBFsjU6IlMyeX3tYVF1+5zYe+0OkzYdL9ACIzYti0mbjpfL1mmCUBJKvUnEL7/8wvLly+nR49H9rXr06MGyZcsK9LQRBEEoDiqlgqbVnPi8Z2P2j+/Fl70a06yaI/enAwNvJ/D5zrN0+H4rH2w+waHgSDS6h30ofThZltlxOZw+v+zil3+DyNHq92FnbsL07g1ZPbLDfzJhe09Fy6LNcvjl3yAG/raXTYE3ydJoH/8AQRAEoWTpcpHTInh4wjaPlBxM5p9tyTrwNnJu0VZMFkqep6cnCQkJHDt2DNAvnpqWllZocvZhxo8fz+bNm2nevDkTBjUhNRv+Og8T3hpdbDGq1Wp+++03QkJCGDBggCHGmTNnGmY99erViwsXLrBz506srKz4/vvvsbe3B2Dsq6+xdec2wjJi6V21GX+YGZFy6QRug95l5uqtxNwMpnPbVsUSb2mrYGrM9wNaYnu3wOBEWCyz9wTmK2KyrGRHlRb6dhKZsSnc/vdKmcT6KJIs8+u/QYxZc8iQsK1oYcovg9swpqU3SoWC7JSMAgnbAvvJ1ZKdkoFSoWBQQw82ju5EI9e899l/nrvBy0v3cPK+Kl6hfNFJMnP2BBbas/jetjl7AtE9rL+aILxASr3S9o8//qB///7UqPHoVSvd3d0ZMGAAa9eufeQCYYIgCM/K3FhNT183evq6EZOWxfbL4fxz8Rah8akA5Ookdl+9ze6rt7E1N6GbT1V6+brhXckmX9VnYT2XrscmM3tPIIG3Ewzj1EoFQxp58HpLH6xMxVX+BlUdcLIyIzYt65ELSgBcj03hsx1nmH/gIn39qvFKA3cqW1uUSpyCIAhCfgq1CRaDTyFn6acgS5JEYmIidnZ2eQsM56SQe2zq3cXKQHP+R7Q3tmLW8RfUbp3LLHZBz9ramg0bNvDRRx/lW+RLrS76x0RnZ2d27doFQOa2VxjyCXz5Ehy+nssv0/tjYmLCV199hZubW5nFmHI0hEm1+gJwoaoj1/etxq33qwxy0nHy588ZuezZYixrLraWfPdyC8asOYxGJ7Hh3A2q2VkyvElNwxivvi24ffQyANc2/4tr2zplFW4BiZk5fPL3KY7djDFsa1rNka96N8He4tlaGLrYWvLrkLasPxPK/IP5q25faeDOu+3rlOiCc8KTOxsRl69f8YNE6zThv6TUfztdvHiRoUOHFmlsixYt+Oeff0o4IkEQhDxOVma82syLUU1rcjUmma2XbrH9cgSJmTkAJGXmsCYghDUBIdSoWIFevq70qO3KxaikAj2XzIxUhrYL97R2r8SkDvWoZm9VykdWfqmUCiZ38mPSpuMoyLf+uOH2sEYeXIhM5EJkIgAp2bksP3Gd309ep51nZYY08njm1gnbt29n6tSpSJKEVqvlgw8+YOTIkQXGzZ07lxUrVpCbm4uPjw/Lly/HxsaGpKQk+vXrR3x8PK1btzZMyYyLi2PAgAHs2bPnidv9CIIglHdKq6pgVRUAhSQhE4vK0dGQtFUB6gGHyT3/Azn/fgLaTOS0cDI3d8XIZxSmbeahMLUtwyMQ2rdvb1gPJCcnh0qVKhVY5KuoNu84THV7qOtmTtNP5nHq1CkCAgKYNm0aK1asKJUYdZKMTpI5cEN/Eb22rSXn1xxEAUgK2KaJxKFKVTZ/PJrebZoVW4xlrb5LRT7r3oiP/zkFwLx9F6hqa0k7z8oAODeuiVUVe9LuJBAdEExKeCzWrmWf8DobEcfkLXntEBTAG619GNPCu9gWnFIqFAxu5EEr90pM3xbAmYh4ANafDeXojWg+695QJP/Kkfi73wuPs+n8TXwr22FmJJLuwour1Nsj5ObmYmxs/PiBgLGxMTk5OSUckSAIQkEKhQLvSrZ80NGP3W/34PsBLens7YLxff3BbsSnsuDgJTov2s77m44XuCJ8f8LWzc6SHwa05IeBrUTCthAdvarwTb/mOFrlX6TG0cqMef2a80EnP1aO9Gf1SH96+roa+rRJMuy/Hslraw4zYOleNgbeeKrWCbIsM2zYMJYvX05gYCBbt25l7NixpKXln8K7Z88eli1bxr///svhw4dp2LAhn3zyCQCrV6+mffv2XLx4katXrxoWcZk4cSKzZ88WCVtBEP6zFEoVJvUnYDn8Aqqq/obtmivLSf+9NpqQLWUXnEBUVJTh688//xx/f/8Ci3wVRVLMLX7cEc2HXUBVsS6ZmZkolUqUSiXp6emlEuPea3cYtfIAmRotsw9dY8zaI3zw/hIU2bkAnHGwJPXKIY6tW4Kng3Wxxlge9PB15fWW3oD+oveHf53kakwyAAqlkpp9WhjGXt9yvAwizCPJMkuPX+W11YcNCVt7CxMWD27DG618ii1he7+qtpYsGdqWKZ38MDXS9/S9k5zBa2sOM2vXOTIf03pBKB1Fra7efjmC7j/uYPmJa+K1E15YpZ60rVy5coHVSB/m0qVLVK5cuYQjEgRBeDQjlZI2Hs7M7dOMfeN7Mr1bQxpULXoPWisTI/74XydaeziXYJTPv45eVdjxVnd+HdyaD9t68evg1ux4q3u+RQZ8K9vxZa8m7BrXnbda++BwXz/c4LgUZu44S+cftvHd/gvcSc54oudXKBQkJycDkJqair29PSYmJvnGnD9/nlatWmFlpU+8d+vWjZUrVwJ5C7FIkkROTg7Gxsbs3LkTW1tbmjVr9jSnJJ/t27fToEED/Pz88PX1LbQaaNeuXfj5+eHn52cY26hRI0C/+nb79u2pU6cOb731luExcXFxtGvXDo1G88wxPg/EeRSEsqO0roF5vz2YdlgMxhUAkDOjydraj8ztg5Ey48o4wv+madOmUatWLTw8PLh16xZLly41bP/5558N4+rWrUvz5s1JTU2lQYMGjBgxIt9+Jr//Dh92BTNjBUrH+kydOpVGjRoxfvx4PvrooxKPce+1O/Rq14IT895HyskiaPZYbq9fgF/oHUBfZXsh8wIrvp+Hg43++684Yywv3mrtQxdvF0BfQPDOn/8Se7ewwL17Y1R3WwGEbj+FJqtsCqSSMnMY/+e/LDx4Cd3d3ruN3Rz443+daFqt8IpXTVYOVzccfebnVir0bco2jO6U7/38+rOhvLx0D6dviV63ZUknyey4El7k8YmZOXx34CLdftzO0mNXSc8R78OEF4tCvr9DeSkYO3YsGzZsICgoCEfHh09BiI2NxdvbmwEDBuR7s1BepKamYm1tTUpKChUqVCjrcEqUJEnExsbieN9Ut/KmvMdY3uMDEePTuJ2UzuJ/g/j74q3Hjl0ypE25mXZV3s7jg54kPo1OYu+1O6wLCCHwTkK++5QKaOtZmcENPWji9vjWCXv37mXQoEFYWFiQlJTEpk2b6NixY74xBw4cYPTo0Rw9ehSFQsHcuXP57rvvSEhIwMTEhJEjRxIcHEyfPn2YPHkyXbt2Zfv27YYk79OSZRl7e3sOHjxI3bp1CQsLo1atWsTFxT1035Ik0aVLF7p06cKkSZP44YcfSExMZNq0afj7+7Nw4UJ8fX0ZPnw448aNK5bE8pMq7e/FF/E8lvefZxAxFpcXLUYp7TbZ+99Ee3ObYZvC1B7TdgtQew1+pnY3xRFfWXmeY8w5O5+cwxMBMO34C8a+r5VaTDpJptuP2wvMemoScoc21/QJoGA3J6av/KBEKjifVEm/ztkaHWPWHDK0lfKpZMvSoW0xN1ZzbNY6QrefBqDZhwPx7Nm0VGM8dzueKVtOGl4rBfB6S2/GPqK6NuLIJU7P30JGTFKRnqPJ+/3w6tvyseMkWWZtQAgLD14iW5s3Q25QQ3cmtCu8121R2mnt2rWLKVOmGG5HR0dTuXJlzp49Wy7baZXF753CzuOQYcP5dOtpdlyJACDteiBRu1YZHqNNT8XIyoaab8/hjcZuzHrvDeLi4rCo5k2Vl8YAYKbNJGnTDxw/fBA7K/NSORZ4vn93lyfPQ4zFpag5xVI/C1OmTCE7Oxt/f39OnjxZ6JiTJ0/SoUMHsrOz+eCDD0o5QkEQhKJxsbWkRXWnIo0tam8m4ckYqZR086nKihHtWTuqA73ruOVrnXDgeiSvrz1M/yV7+PPcjYdOndJqtXzxxRds2rSJW7dusW/fPoYPH058fHy+ce3bt2fSpEn07t2bHj164ODgAIBarcbCwoINGzZw/vx5PvvsM6ZOncqUKVMMK10PGDCA8+fPP/WxFqUS+H6RkZEcPXqUYcOG6c9VCVcCPy/EeRSE8kFp5YJZ778x67oShakdAHJ2Alk7h5H1Tx+k9DtlHKHwpKS4c4avVQ71i3//skx8ejZXopI4GBzJH2dD+eHQJT7depqhy/cVSNgaaXU0uhGpfyxwuJozZyP+G9XcpkYq5r/cgsrW+qTVlegkpm49jSTL1Ox7X4uETf9SWjVckiyz/MQ1Rq86ZHitbM1N+GlQa95qU7vQhG1aZAIHpizl4EfLipywBTj17SYurtyHLEmPHKdUKBja2JM/H6i6XXcmlAFL9xAQnv/7pajttLp06UJgYCCBgYGcPXuWOnXqMGTIEEC004KHn8d3Vu8zJGzVSgVvDh1A648XUfOdb6j5zjeYVa6OS5P2fNOvOdqrJ3ltwEtcvnQJi8wEcmL0F2eubVqCrlkfev+6h5+OXCY1K7csD1UQnlmpd2yuUaMGf/zxB4MHD6ZFixbUqFGDOnXqYGVlRVpaGpcuXSI0NBRzc3PWrVuHu7t7aYcoCIJQZBUti9ZzqajjhKfn42zL5z0b8277OmwKvMn6s6GGHmmh8al8sfMsCw5epF+96gxs4I6LjYXhsYGBgURGRtKmTRsAGjdujIuLC+fOnaNTp075nuett97ijTfeIDY2lhs3buDi4lLg6uipU6eIjY2lZ8+etG7dmpUrVyLLMqNGjeLQoUNPfGwKhYL169fTr1+/fJXAj+oRv2LFCvz9/Q2zWoYNG8bIkSOpX78+ffr0oUqVKowePZrt27c/cTzPK3EeBaF8USgUGNUaiqpqR7IPjkcb/CcA2hv/kH77MKZt5mJUe3SJVN0KT0cnyZwJjyM0Ur/IV0NXR0OyTRd7N2mrVKO0932i/WbmaolNyyI2PYu4tGxi07P0t+/9S88iPj0brfTwBKNVVg5muXlTo2vfjsP8bp/7sIo2aFXK/9RFdHsLUxYOaMnI3w+Qkatl37U7LDx4iXfb18G+VlUSrkaQeP0O8VfCcajtVqKxpGTlMnXraQ6H5PUmbli1IrNfalpgPQMAXa6WK+sOcnHFXnT3TXd3qFONhKsRSA8s9FuADIGLtxN/KYyWU4dgXMhz3M/VzpKlQ9vmq7q9nZzB6NWHGNzQg/HtfA1Vt0978be02mk9L+4/jzEJiajMrfj3VjxKtRHGKiXf9G1GW8/KTPSvy5nwWALOX2Ji2BUOHdyOcyUnQg/qz2M1O0tcrEz4fHA7luw8SpSZJRauNUnL0fDz0SBWngpmSCMPhjX2xMb84a9TYYq6SHF4eDhvvfUWV69exdjYmDfffJN33nmHmzdvMmjQINLT0xk6dCgff/wxAEFBQUyZMoW///77mc+j8OIrk2X2evTowYULF/j666/ZunUrW7ZsMdxXuXJlxowZw+TJk6lRo0ZZhCcIglBkDao64GRlRmxaFoV9jFCgX0yrQVWH0g7tP8vewpQxLb0Z1cyLfdfusPZMCIG39a0T0rI1rDh5nd9PXqedZ2UGN3KniZsjVatWJSoqiqCgILy9vQkJCSE0NBQvL68C+4+KisLJyYnMzEymT5/O5MmT892v0WiYMmUK69atAyAjIwOFQoFCoXjqRU7urwRu06YNp0+fpnfv3ly8eJGKFQv2V5ZlmWXLlvHZZ58Ztt2rBL7nvffeM1QCz5o1C9D39qtXr95Txfg8EOdREMonpYUT5j3Wowl5hez945AzYyA3hey9r6O5/gdmHRajtK5e6GM1N7eTc+xTkCWQtRg3nISxT8EP1VJqOFn7x2GaEESG2gSTem9g7PcOUspNMrcPBk06RrWGYNJE/6FalxhEztEPMe/9V4ke+/Nk77U7zNkTeF9F6zWcrMyY3MmPDjVskRKvAKC090Wh1idHJFkmMSPHkHi9l4SNMSRjs4lLyyLtGftQWmXlMPrgOdQPSerWiE9m9MFzWL9UeCuAF5WngzVz+zTj7T+PIsmw7MQ1qtlb4du3Bce/Wg/A9c3HSjRpe+FOApO3nCQqNdOwbUyLWrzR2gd1IdOfowKuc+rbTaTeV+VqZl+BRuN74+bvR2ZMMtkp+nULZEkiMSkJO1tbFEolsiQRtvccQX8cAVnm9r9X2Db6O9p+ORI7zyoFnut+96puW7lXYvq2AM7dfe+49kwIR0Oj+KxHIxq6OoiLv8Xg/ovo5uYWRMfF4zLkfZRqI8yMVCx4uaWht7FKqaCRqwObF++le/duOFfSz3J88Dw29fVi+qQJHFz7B+sv3WHrpVtoJZmMXC2/HrvK6oAQBjV0Z3iTmtgVIXl7rxr4wZZa/fr1y9dSS5Zl+vbty+TJk2nbti2Ojo7Exem/dxctWsS4ceMYOnQoPj4+vPPOO1haWvLuu++WyxagQvlUJklbgGrVqvHTTz/x008/kZaWRmpqKhUqVHjmvn+CIAilSaVUMLmTH5M2HUcB+RK39+qCJnfyKxf90/5rjFRKuvpUpatPVYKik1gTEMLOKxHk6iRk4EBwJAeCI6lRsQKDG7rz/Y8/MXDgQJRKJZIk8cMPP+Dq6sq0adOoXLkyb7zxBgCdO3dGkiSysrIYOXIkb7/9dr7nnTt3LiNGjMDJSf+mcubMmXTv3t1w39N4kkpggEOHDpGdnU27du0K3V9xVwI/L8R5FITyzcijH2qXdmQfeh9NkH6RQF34XtJX1sG01VcY1RuHQpGX5JFlmaydw7F4+QAqh7pIKWGk/+6NkUc/FMb5P1Rnbu2HccPJpFm30SdPsvQfqnPPL8K43lsY1RpKxsraGPu9A0aWZB98D7MOP5XuCSjH9l67w6RNxwtcoI5Jy+L9Tcd51yeT/rJ+KvqZzCosWbG/SNWxRWVrboKjlRmOlqb6/+/+c7LU/6+OTODo/rOP3IdakvF8wkq7F0FL90pM6eTHV7sDSb12liE/TMalghlZt+Po5twA5X41Dd/uhamNJQBhYWG4u7tTp04dtFotarWajRs34u7u/kSVg7Iss/J0MAsOXDR8D9iaGfNl7ya0rFGpQJyZ8amc+eFvwvbmtdlQKBV4vdyaeqO7YGyhn7VmUckWi0q2gL7/pTbWGLv7+l9W9HalchMvjny2mtzUTNIjE9g5diFNJ/XHvXuTx54vNzsrlg5tx9qAEL4/pK+6jbhbdTuwfjV2fvW5uPj7jO5dRF+2eh0rb8ucO3uGsJVf0+CDhfwyojt1q9jnGy/LMuvWreP77783bHvYecxJjObK8q9x1mhx7TSQk+nGaCWZzFwtvx2/xpqAEF5p4M7IpjWxt3j0TMiiVFXv27cPExMTBgwYQGysfhG7e58B7lVVazQaJElCqVTy888/07lzZ6pXL/xCpCA8qMyStvezsrISyVpBEJ5bHb2q8E2/5g9Un+grbCd38qOj16Ov7Aslz7uSvnXCe/512RR4g/VnbxhWUr4Rn8qXu85hZWrFyPmrGNTAHRdbS8NjZ86cmW9fFy9ezNck/8Fpu/c+wNzTs2dPevbs+UzxP0klMMDSpUsZOXIkKpWqwH0lUQn8vBDnURDKP4WpHWZdlqGuOZDs/W8gp0WANpPsgxPQXP8D045LUNnl/cwqFArknGQA5NxUFKb2oMr/oVoXsQ+FygS158tw90O10uJuT3qlEWgzQdLoq3UVSjQXfkbt1umh1b3/NTpJZs6ewEJnFN1z89pRuFtseDDJiQu6xCLt20StxNHSLF8iNu+2PkFb0cIUY3XB38P3S0hKe+T99yj/oxfRBzX04GZ8Gp9+Pooar32GU3UP3oy9w6ivx9PIzpPQ7aepPaS9YbyVlRVnz54tsCBQUSsHU7NymbYtgAPBkYZt9V3s+fqlpjhVyL84lKTVcW3zMc4v2YkmI699hYOvG03e7//YCtnCVG5aix6/vcfhqb+TcDUCXa6WY7PWE3vxFk3e7YPK5NF9Y1VKBcOaeNLaoxLTtgUQeDsBGVj2zz6iroZgWd0bEBd/n1ZgYCARt++wJExHWGIa5i4emNlUZKyXZYGELejPY05ODl26dCl0f486j1v/3sGyE9fYdP4mGp1EtkbHipPXWX8mlJfrV2dUMy8cLAu2zyhqS60rV67g4ODA4MGDuXLlCu7u7nz77bfUqFGD8ePHM2rUKBYvXsykSZNISUlhw4YN7N69u3hOpPCfUC6StoIgCM+7jl5VaO9ZmTPhsYRGxuJe2TFfnzehfLAzN+G1Ft6MbOrFgeuRrAkINkx/S8vWsPJUMKtOBdPGw5khjTxoWq1gUrYsODk58csvvxSpEjglJYVNmzY9dNGzkqgEvqeovb/uefXVV/n9999JSEjAzs6uxFdUfl7OoyAIYFS9G+phF8n+90M0F/TJIF3kv2Ss9sOk2QyMG76PQqnGrPs6srb2ByML5JwkzHtuRKHK/6Fal3AFhZkD2TuGYBp3mSw7D8zazkNpXQPj+uPJ2vUquRd/wbjB+8g5KWhCNmLed1dZHHa5dDYirsAiXw+qqbxp+DpYrgY8vjrW0cqMCqZG5eLv7H/BpI51+UylQpedQWq2hpXZWVgamWKkVHF98zF8BrVF8ZjV2otSOXgxMpHJW04QmZLXDuF/zb0Y16Z2gXYIcZdvcWreRhKv5y08aFzBnAZv9sCjR5PHxvMolpXs6PLj2wQs3ML1LccBCPnnBInXb9P2i5FYOts9dh9udlb8NrQdawKC+f7QJTTW9mQlJzLk29X8r3s7uruYiou/T0Fhacet23dQXw3C1NEFq+wklGnxdGzWsNDxv/32GwMHDnyq8+hsbc7HXeozukUtlh2/xsbAG+TqJLK1OladDuGPszfo71eDV5vVzHdBoagttbRaLfv37+fYsWM4ODiwadMmBg4cSEBAAM7Ozuzalfe3ZMCAAcybN48DBw7w008/YWJiwldffYWbW8n2lBaebwq5tJaLfMGkpqZibW1NSkpKgQVoXjT3V5Qpn+EPZ0kq7zGW9/hAxFhcRIzPrrTjC4pOYu2ZUHZcDidXl3+V4Rr2Vgxq6EGvOm6GBSj0i7CU/+R8aZ9HWZaxt7cv0PsrLi6u0Nk0mzZtYvv27SxdutSQtP3hhx9ITExk2rRp+Pv7s3DhQnx9fRk+fDjjxo0rkwU6xM/LsxMxFo//coza24fI2vMackqoYZvSsSGmHRaTc/h9TJp/htqlDbro02T+/RIWwy+gNMv7UJ1z9ltyjs/AbOC/JOgcsI3ZjObyb1gOOV3guTK3DcSk8cfI2fHkXvgZVCaYtpyFskLpfKguj6/zjsvhfPj3qUeO+cXkI7yVocgoSB8aiYNtxcdWxxanhGu32T76u8eO6770Pey9XEohokcrq9f5n+07efmVV5DVxuiyMhjbfAjNNdYA+M99jSrNvQkLC8PT0xM/Pz+ys7N5+eWXmTp1KiqViqioKEaNGkVsbCxvvPEGvXr1Yvjw4ezevRulUsmagBC+3X/B0A7B2tSYL3s1prWHc744clIzOffzNoL/OQn3pSM8ejah/hs9DK0aHqeo5zF0RwAn5/6JLle/MJ2xlRmtpg2lSnPvIp+7sIQ0pm8L4MD2v4g7uAkUSoyUCqZ8+CGfvDO20Iu/lStX5vz581haWhaIcdasWTg7O/Pqq68CsHXrVqZMmQLoL/7euxBcGkrz+zE4LoU31h4h+Nhe4g5uwkitpoq1GZ9+8glDhgx56Hncv38/jRs3LhDfk57HuPQslp+4zoZzN8jW5i1oZ6RS0rduNf7XvBbO1uYEBAQwZMgQrl+/bhjTuHFjZs2ala+qesOGDSxcuJCDBw8SGxuLhYUF1tbW5OTk5Cs02LhxI6dPn2b27Nl4e3tz6tQpAgICWL58OStWrCims/to5fHvy4OehxiLS1FziqLSVhAEQfhP865ky8wejXivfR02Bd5k/dlQQ0XRjYQ0Zu0+x/eHLvFS3Wq42lqw9Pi1QhdhEW0wir6ickxMDLNmzWLfvn0sXbrUsF2sqCwIQmHULm2xHHaenOPTyD03H2QJKfYMmeuagok1qkr6xaVUlRqjsHJBij2H0i3vQ7XSyhWVY31U9rUhNhZ1rWHkHHgbWadBocr7UK0J3ojSugYqRz/SV/hgMfgkupgAco5Px6zL8lI+6vKjouWj+z6q0OKuuKX/2taLKg5OpRFWPjHnQkr9OZ83Wq2Wj6d8QBUnB+KyJXKVKn49thpfv1exMjLj2uZ/qdLcm+zsbHx8fNBoNGRnZ/Pjjz8iyzIzZswgOzub5ORkcnNzSUhIYMKECcybN4+ffl3ClI8+wdi1FpW6DMXY1oF6VeyY06cZle6rXpQlidAdAZz9aSs5yRmG7bbuzjSZ1B/HOiXTksS9WyNsPSpzeOpy0u4kkJuWxf7JS6k7qhN1RnVCqXp8cqiavRW/DWvH6lpV+KFhG3K0+gv9f6aD8d5APvp0GmZGeekVa2trMjIyDEmoB5VEO63y7lJkIm+tP0pKdi629VrRuGN3Fg9qk+93zINtyaytrUlLSyv0HMKTn0cHSzM+6FiP/zX34veT11l3NpRsjQ6NTuKPczfYdP4mL9WtRi93uyK11OrWrRuTJ0/mzp07GBkZsX37dry9vfMlbJOTk1mwYIGh6jYzMxOlUolSqXzhq6qFZyeStoIgCIKAfhrn6Ba1GNmsJgeuR7I2IIQzEfEApOVoWHU6uNDHxaZlMWnTcb7p17zcJG711cBxhEbG4Z6tKJVq4KL2/gIYM2YMc+bMKVCBW95WVC6L8ygIQuEURuaYtvkGtecAsveMRkq8ArIOshNJX1kH826rUJjaISWHorTN/6FaXa0b2UemIKXfAYzQhe1AaeedL2ErZyeTG7gQ87479be1maBQgkKJrPlvf6huUNUBR0tTYtOzC72/uuI2xgp9BaPSsX5phoYuR8OZH//h2sZ/S/V5n0fnzp3jypUrnDt3DuyrMPyHP7kw+y0Cs2NobVSNO8evkhaZQPXq1Tlx4gQmJibExsYycuRIVqxYwYwZM/L1tHV1dWXgwIGoHavy7oTm1HjjS3QZqcTsXc+n3yzk7ba+GN2XDE0KjeLkNxuIuxhm2KY2M8FvTFe8+rVEWcKV2Xaelem+5D3+nbWW20cugyxzYdlu4i/fotX0oZhYWzx2HyqlghFNatLG3Zlp205z/k4iMrDqdAiHQ6KZ2bMR9V0KLkgmwJnwON75818y7lY713a25adXWmNtVvB9YmmwtzDlPf+6jGzmxcpT11l3JpTMXC1aSWZj4E3+uhBG5zc/pG//lzExUj+0pZaFhQU///wzvXr1QqPRYG9vb2jVcM+UKVOYMWMGZmb63rlTp06lUaNGGBsb5yteEITCiKStIAiCINxHrVTSqZYLnWq5cC0mmbVnQth26Ra5usK7Cd3bOvWfU5y/UwMbUxOsTI2wMjGigqmx/mtTI6xM9F+blPCHkr3X7jywKF7pVAMXtffXkiVLcHV1xd/fH0nK346iPK2oXFbnEYrWG/jixYuMGzfOUHnSvHlzfvzxR8zMzEq8N7AglCW1c1Mshpwh59QX5AZ8DZIWOSWEjHXNUJhWxLTNPJQVXMk+Pg2lRWWM676BwsgCsw4/kf13b0y1GnLN7TDrvjbffrOPTsGk2XQUav2HapMmn5CxpjGojDHrtKQsDrXcUCkVtPFwZkPgzQL3KQBPZVje2FJM2qaExXBk+kqSQqNK7TmfZ1WrVkWSJM6fP8/w4XXp72rKBSDF0xuis0CWCf7rOC79m2BrawvoKwKvX7+Oo6MjkDcjJi4ujsTERLy7D6bHuI+QlUpM7BxRaLOo4mDBe/51Dc+ryczmwm+7CfrzCPJ9bajcOvjR6O3emDtYl9o5MLYyo92Xo7i85iCBv2xHlmQiT11j2/++o80XI6jo7Vqk/VSzt2LZsPasOh3MosOXyNFKhCel8+rKgwxt7MnbbWtjZqQWF3/vOhoaxcRNxw3VyY1cK7Lw5ZZYPGJBuIzoJLJT9NXYsiSRmpSEOinX0OfY1NoCi0q2zxybnbkJE9rVYWSTmqw6HczaMyGk5+iTtyG2XpiPmEn32q681qIW1ez1xQYPVgN37tyZjh07PnRa/+LFi/PdHjNmDGPGjHnm2IX/BtHT9imJnrblS3mPsbzHByLG4iJifHblMb791+/w3sbjxbIvY5UyXxK3YHLXCCtT47v/P3C/idEjewTuvXaHSZuOF1jh+97Hg5KsBi5q76+hQ4dy+PBhw2ISt27domrVqvz111/Ur5/3Yf/UqVMsWLCA1atXl/qKymV5HovaGzg4OJisrCx8fX2Jiorivffew8fHhxkzZpS73sDl8Wf6QSLG4lHaMerizpO1ZzRS7FnDNqWNB6Ydl6B2aVPm8T2N8hijJMv0/WU3YYlpBe5zsjLjhyqbqHRrGQDm/fagdu1QovHIskzItlOcnr8FXXYuAEojFbIk50sKPkhprKbPmg+LJdHzrMrqdf7444+ZM2cOSqUSnU5H5yGjSXZvh+Nv32NnZEHnms0xGlWHaZ/N4ObNm+Tm5lKpUiXOnTuHo6OjoadtQMAZqrfoSKavPxF/fE+Fus1JP7mLKnYV+H35Mho1aoQsy4QfvEDAwr/IjEsxxGDlUpEmE/tRuUnhi3c9iWc5j9FnQzgyfSXZSfpKeqWRisYT+uL5UrMnWhwvLCGNT7ee5kJkomGbq60lL9V144+zN/It4ldeW2qV5Pfjnqu3+fCvk4Y+x61qVGJev+aYGj38/WxGdBJbhsxGuluVW5iS+nlOzcplTUAIqwKCScvW5D2fArr6uDKmRS1qVCyY/ymPv7sfJGIsX0RPW0EQBEEoJjka3eMHFVGuTiIhI4eEjJynerypWnVfcjcv+WthrGbHlYgCiUbQVwMrgDl7AmnvWblEqjyqVq1apN5fq1evNnwtSRIqlYrAwEDs7PJWcS7LFZV1ksycPYFldh6haL2BPT09gbxz2LhxYy5fvgyI3sDCf4fKoR4Wg06Qe+Ybck58BrocpOQQMje0w6jum5i2mo3CuOBCiMKTORISZUjYNqxqzxutfPItxpm94Wvu/ZUs6Urb3PQsTszdwK19gYZt1tWcaD1zOMbmpvkq8xKTkrCztS32yrznlVar5dixY+zfvz/fjJjBXQdg2qwX3pEJaNMyqWXiwpUrV5Akidu3b/PFF1+wfPlyJk+ejLOzM/NXrOODzSeISM4gcs08nLuPoF3VCsQp4jEzNcXBwYHU2/Gc/nYTkaeuGZ5faaymzoiO1B7cDtUjKixLS6UGHvT4bSKHp/1O3MUwJI1O377hUhhNJ/VHbVq0afvV7K1YPrw9q05f54dDl8nV6atuvz90ucDY8thSqyT9dSGMGdsDuJuvpXMtF2b1bpKvbUZhslMyHpmwBZBytWSnZBT7z3QFM2PeaO3D0MaerD0TwspT10nN1iDJsP1yODsuh9PZ24UxLb3xvFslLiqqhZIkkraCIAiC8BiPW4Tlnkkd6lLFxoK0bI3+X07u3f/1t1Ozcw1fp+Xkkp7z6DekhcnW6shO1xH3kN6CDyMDMWlZLDh4kY5eVahRsQKWxfihycnJiV9++YWBAweiVCof2vurKObOncuIESNwctIvZjNz5kzD6r9z584ttpgBtJLE7aQMQuJSCIlP5XRYbL6qmAfdO49nI+Jo7OZYrLHAk/UGviczM5OlS5fy1VdfAeWvN7AglCSFUo1J4w9Ru/che89r6KKOAaC58BPam9sw67AYdbUuZRzl823lfT3dRzT1opGrA66mMo6ODigUMrq4cwAoKlRDYWr3sN08s7jLtzg6YxXpUXkVjZ69m9Fo/EuGBNu9BI4kSWhjjbH7D1RrFVVgYCCRkZG0aaOvQm/cuDEuLi50tNexu7EX/KX/2Tm8Yh9e3ZtgqlZibGzMqFGjGDt2LB988AF/nrvBnL3n0egkUi6dwNKxMovfGcpbL3Xg1KlTnDx2grcHv8Yg0wb5km6Vm9WiyXt9sapSvvq9mjtY0/n7tziz6B+u/nkEgBs7A0gKvkObL0dRoYj9aVVKBSObetHGw5mp/5zmUlRSoeNK6+JvebAmIISv9wQabr9UtxrTuzV8bo7ZytSI11t6M7SRB+vOhvL7yeskZ+UiA7uCbrMr6DYdvapQr4o9q04Hi0WKhRIjkraCIAiC8BgNqjrgZGVGbFpWoRWYCsDRyowhjTyf6M2oTpLJyNGQmqMhLV9CV3879SHJ33u3Mx5ThVCYFSevs+KkvoVBpQpmeFS0xt2hAu4V9f9qVKyAufHTvT0YPHgwgwcPLrD9wd5f94uKisLGxibftpJYUVmSZe4kZxASl0pofAqhcamExKdyMyENzSOm0z7M1H9O07FWFZq4OdLQ1aHYEuBF7Q18T25uLmPHjqVTp0707dsXKF+9gQWhtKjsamE+4BC55xeR8+/HoM1ETgsnc0s3jHxGYdpmHtro05jufwet//cYV+tc1iE/F4Kikzh9Kw7QT/lu4+EM93XXk5KCQaOvblU5lEyVrSxJ+h6kv+4wtD8wsjSl+eSBuPmL32NF9bAZMb4+PjRr7cjqQxewSU5Higjnkx+2MLBPK25ExrB9+e941/Zlyl8n2RV0GwBdVgaZAbv59+B+PJ0rkpmZSXRAMCdm/0H09TCkWvqetuaO1jSe0JeqbXyfqOVAaVKqVTSe0IeKtd048fUfaLNySQqNYvvo72g5dTBVW/sWeV/V7Sswvl0dXl97+KFjSvrib1mTZZmlx6/mqzQe0siDDzrWQ1lOvwcexcLEiNHNazG4oQd/nA1l+cnrJGXqZ8vtvXaHvdfuFHjMf62iWihZImkrCIIgCI+hUiqY3MmPSZuOo4B8idt7bz8nd/J74uoBlVJBBTNjKpgZA49ftfhBWkkiI0dLWnYuJ8Ji+Xzn2cc/6D7RqVlEp2Zx9EZ0vu1VbCwMSVz3ihXwcKhAdfsKj+w/Vl7IskxUaqYhKav/P4Wb8Wlka4uvzUV0WharToew6nQIKoWC2s62NK3mSGM3R/xc7J96wbmHVUKdO3cuX29g0LeRGDRoEI6OjsyfP7/Q/Z06dYrY2Fh69uxZ6r2BBaG0KZQqTOqPx6hGT7L2vo4uYj8AmivL0dzcgcLYEmVqKLnHpmLk1qncJpHKk5Wn8qpshzfxRKlQIN2ftL1bZQsl0xohKyGVo5+vITogLw4HXzdaTR+GpXPJVfW+iB43IybLIQ2bZCURmXGsmvkOP881RpZkzKpUp2qPV7l2N2ELYHHmH1b98C2ezhXJiE1moK8/bXt0Qq1QMtqjMwqVEu9X2lB3VGeMzE0eEVX5Ub1jfWzdnTk0dQWpt2LRZGRz8KNl1B7mj99rXVHe/buekJBAhw55fZszMzO5ceMGsbGx2NnZkZiRNxMq9vBfJJ09BLKEiUNlqvYfh8rMAm1WOq8NeAkpK/2FWjBUlmXmH7zI8hN56xu83tKbt1r7PNHvW03Gk80mKw3mxmpGNfPilYbubDh3g2XHr5GQWXirs/9SRbVQ8kTSVhAEQRCKoKNXFb7p15w5ewLzTZ93LMMpUGqlEmszY6zNjOlrbcEv/wY9tBoYwNbchNHNvbiZkEZoXCqh8amk5WgKjLuTnMGd5AwOh+StyK1UgIuNpT6R61ABj7v/V7OzeuTiaA/zrP2/ZFkmNj2b0LttDe4dT2h8KplFrEBWKRS42VnePR59xXF1eyveXH+UuEecR7VSYVhQA0Any1yITORCZCK/HruKiVqJn0tFmrg50MTNER9nW9RFnJ5b1N7AWq2WQYMGYWdnx5dfflnoh6Gy7A0sCGVJaV0D83570FxeSvbhSZCbClkxyFkxAEixAehu7RZtEx4jJjWTXUERAFibGtOrjluBMbr7F4Er5qTtnRNX+feLteQk3/19pVDgO8yfeqO7GBJowpN51IwYTWY2a3vNoL6dO74OnvzcoSE5RvnTBaZqFV/2bkLHj15G0uq4svYg53/bRd0sO+o2GAWAY70aNHm/H7Y1nEvjkIqVTfVKdP91Ase/+oNbB84DcHnVfuKvhNN6xjDM7Kywt7cnMDDQ8JhvvvmGQ4cOGXrz32uplRZ8nqQzB/B46ytUJmbE7N9I9O61VHnpNZIDj2BXuSYrvv+GSSNf4dKlS/j6+jJx4kRmz579XCZsJVlm1q5z/HnuhmHbe+3rMKpZ0Recy05OJ2j9Ya7++fBK5bJmZqRmeJOa1LCvwFt/HH3ouHsV1aduxdK8ulOJxPK4Cwj3mzt3LitWrCA3NxcfHx+WL1+OjY0NSUlJ9OvXj/j4+BfqAsKLRCRtBUEQBKGIOnpVob1nZc6Ex+ZbhKU8XEEvSjXw1K4N8iWXZVkmNi1Ln/R8TOJTkiE8KZ3wpHQOBEfmPa9CQVVbSzzutVi4+7+bndVDF5rYe+3OA8nvh/f/kmWZxMycu20NUgmJSzHEeP+qvo9iSDgbks3WeFSsgJudZaEJ5ymPOY9f92lGE1cHAiLiOBkWy6lbcdyITzWMy9FKnAyL5WRYLHAZSxM1jVz1Cdwm1RzxqFjhoRUnRe0NvH79ejZt2kTdunXp1KkTarWali1bsmjRIsO+SrM3sCCUNwqFAmPf11C7dSVr35vowrbluz9z13AsBh5DZetRRhGWf2vPhBouUA1sUAMzo4IfHXVxgYavVY4NiuV5dRotgb/s4Mrag4ZtZvYVaDltCM4NPYvlOYSClKYmXHN1pFbwHYx0Ej634zhXPX/i1dLEiPaelYk9f4OT324iOTTv4q6JjSUNx/WiRteGz3UVu5G5Ka1nDsfhz2qcWfQPsk4i5mwI2/73LW0+H4Fjner5xt/fUx7yWmrFRd/ColotVCZmAFh51efGkhlUeek1FEo1MUkJjPr9ADG347gal0bEjh3P7YKhWkli2tYAtl0OB/Tvlz7p2oAB9WsU6fFZiWlcWXeQ65uPoc3KLfLzJlyNwN7L5WlCfmap2UWLc+LGY3T3daOrtwsNqjoU62eGx11AuGfPnj0sW7aM48ePk5WVxa+//sonn3zCokWLWL16Ne3bt2fatGn4+/u/EBcQXjQiaSsIgiAIT0ClVORbhEVZDhK29zxpNbBCocCpgjlOFcxpWaOSYfv9LQb0iVL9/zfiUwu0GNDJMmGJaYQlpuXr66VWKnCzszIkce9V5gbHpjB5y8kCVayxaVm8v+k441r7YGNukq/3bNITvIG/v7XDvUTyk7Z2KOp59K9ZBf+a+q/j0rM4FRbHyVuxnAqLJSo10/C49BwtB4OjOBis/3BrZ25CEzdHmlbTJ3FdbPK3xihKb+ChQ4cydOhQJEkiNjYWx0IW2ymJ3sCC8LxRWrlg7DeOrAeStmTFk7HCC6NaQzFu8hEqO++yCbCcyszVsiFQXzFnpFLySkP3AmNkWUa6W2mrMK+E0uLZKytTb8dzdMYqEq5GGLZVae5Ni48HYWpr+cz7Fx7ubEQcF2wrUAv93/KGNyK5Y2uVd8USkFPS2fbpClIOXczbqFBQ86Xm+L3eDZMK5qUcdclQKBR4D2yDvZcLh6etJCshlaz4VHa//SMN3+5NrZdboVAoOHbsGElJSfn+tt67iP5G4GkSTuxCk5aE2tKG5PNHkHKy0Gam4d6iI+d/n0fwD5Op4NOYzw6HErtmDms2bEaW5ecq6Z2r1TH5r5McuK6/oK9SKPiiV2O613Z97GMz41O4vOYgwX8dR3ffzC+FSmnoX/0op77bhImVeZn0ti7qIsWZGh0bzt1gw7kbOFia0sXbhS7eValT2a7YX+cHLyDcc/78eVq1aoWVlRVZWVl069YNf39/Fi1ahJGREZmZmUiSRE5ODsbGxuzcufO5vYDwIhJJW0EQBEF4gRRHNbBCoaCytQWVrS1o7ZH3IfzeYl6hhl6x+mTuzfhUch94c62VZENFbFHcS+IuOnKlSOMrVTC7Lzlr/cyLqD3oSc+jg6UZPXxd6eHriizL3E7OuFuFq6/ETbqv71liZg47gyLYeXfacWVrc5pWc6Spm74nblE/CAiC8HiyLJNzfBooVCA/2NdaRnN1FZqrq1F79sek8ceoHP3KIsxyZ8uFMMNshm4+VXGwNCswRk4LR85OBIqnn+2N3Wc49c1GNHd/XyrVKhq81ZNaA1o/V0ms51VMeCz9Aq4abttk5zLi34v5xshAyn237bxcaDqpPxW9H5+gex451qtBj9/e48iMVcScC0XWSQQs2ELcpTCaTxnI0qVLGTFiBGp1/vceHb2q8PMHY3gnNZaw32eDQol17SYAzOrTnB71a7KjWxN+PRbErcR0Irctx7JZD978ZRO5J7dSvWIFvpv1OX5+fmVw1EWXmavlvY3HOBEWC+gv8Mzt04z2NSs/8nEZMUlcXn2A4K0nke6b2aU0VuPZqynVOzVg9/if8t1XGFkrcXj6ShonplLr5dbPfkBP4HGLFIO+nYgsy+TcfY8cl55tWA+hsrU5XX2q0tW7KjUdrZ/5d1xhFxDuadiwIT/++CPR0dEoFArWrFlDWloaiYmJDBs2jJEjR1K/fn369OlDlSpVGD16NNu3b3+meITiI5K2giAIgvCCKalqYOXdVghVbS1p55n3hlwrSdxOyjAkae+1MAhLSMvX+/VpVLQwNVTM3p+ctTIt+elaT3seFfedp5fr10CSZULiUgyVuGfC48i474NIZEomm8+Hsfl8GADuFSvok7jVHGlY1eGhx/qsfYGfVlF7qO3atYspU6YA+h68iYmJVKpUibNnz4oeakKp0d3ajRQT8JhRMtrgDWiDN6Cu3hPjJp+gdm5aKvGVRzpJZvXp/AuQFUa6rzXCs/Sz1WTmcOq7TdzYkfc6WblUpPVnw8ts6vN/kbVOIukxf7Pv/YUxsjSl/phuePZpgfIhrZBeFGb2Fej43VgCf9nB5TUHALi1L5Coq2H8sW09pwMK//3S0asKl9YtMlz8zYyJYMblw7zUsBYAveq40b22Kwv/+IdvNZlUqNWQkMWf4jrwHaJlmS6DRrJh605auVd6ooRecHAwdevWNdzW6XRoNBpCQkJwd8+rmP/yyy/54osvDLdzc3MxMTEhMzOTmzdv0qBBA7KysvDw8ODSpUsABAUF0bx5cyIjI9Eq1bzz578E3k4AwNRIxYL+LWj2iP6taZEJXFq5nxs7TiPdN3NLZWJEzT7N8RncDvOK1gD0WfMh2SkZAMiSRGJSEna2tiiUSmSdjsvrDhG+/zzIMqfnbyErIQ2/17uV2gWeorQl+7J3E1pUd+JQcCQ7gyI4GhpteF8cmZLJb8ev8dvxa1S3tzIkcKvZWz1VPA+7gADQvn17Jk2aRO/evZEkiQEDBgCgVquxsLBgw4YNhrHvvfceU6ZMISQkhFmzZgEwdepU6tUr/WpmQU8kbQVBEARBeCZqpZJq9lZUs7eiw30tGDQ6iYikdEOrg6M3orkUmfTY/fWp68ZLdavhXtEaazPjkgy9VCgVCmo62lDT0YZhTTzR6CSuRCVx6lYsJ2/Fcv52Qr5K5XvJ7zUBISgV4FPJVt9Kwc0RP5eKmBqpnqgvcHErag+1Ll260KVLF0MLh9GjR+Pv7w8geqgJpUKWZbKPfwoogcKm2ipRWFZG1uVClr5STHtzK9qbW1G5dsSkySeoXdqWZsjlwoHgSG4n65Mlzao5UtPRptBxuri8RchUDk+XtE28fpsj01eRGhFn2FajayOaTOyLkbmYdVCaajnZElaEcc7NatHyo1cws69Q0iGVG/eqvivWduPYrHVoMrLZc/YIldW2mEXmQK3CHxcbE00jVycqShmMnf4dkydPzne/pNPy9+LvOLllLReSNAz6SQsoQKEgLS2Nt//8F59Ktoxt5U1bD+ciJSQ9PT3Jyspr7dSzZ09Onz6dL2EL8Mknn/DJJ5/o45AknJycDNPhJ0+eTP369dm/fz+2trZs3ryZvn370qNHD7755huyUfHmmsNcjUkGwMrEiB8GtsTPpWKhMaVGxHFp5T5u7DqTr+2B2swYr74t8R7cFjPb/MlKi0q2WFSyNcSnjTXG7r5WUG0+G06giwOXft8LwKWV+8hKSKXZ5AGltlBhUdtpdavtSrfarqRm5bI/OJKdVyI4FRaLTtYncG8mpPHTkSv8dOQKtZxs6OpTlS7eLlS2tij0eR+Unp7OH3/8wenTpx865q233uKNN94gNjaWGzdu4OLiQoUK+X+GT506RWxsLD179qR169asXLkSWZYZNWoUhw4detLTIxQTkbQVBEEQBKFEGKmU1LhbGQsuNHJ14LU1j18RuKevGw2qOpR8gGXESKWknos99VzsGdPSm2yNjvN34g2Ll12JTuJesZMkw6WoJC5FJbH0+DWMVUqq2lkSGlew7URsWhaTNh3nm37NSzxxe7+H9VC7X3R0NPv372fZsmUAooeaUDp0uchpERSesEW/XdJiOeoq2qBV5ATMQU6/rX9o+F4yw/eiqtwKkyafoHLr/MJO0U+IDCbs57wqqopoOGCto0fKAkY0aZVvbMTV44Su7IudOoloSUtkkkwrDwW3E7TErrDCRKUhxqINXSbvBiD49FbubBlDuy+j8u1HlmWu/nmEsz9tRdLoK+7UZiY0ndSfGl0alvARC4Up6myS+mO6/acStvdzbVsHmxqVOPTJcg5dWEs7pzocnvY73pfb0ODNnsyY+ZlhwVCAzp07I0kSWVlZjBw5krfffjvf/u4tGFrZuRKVneH3Rd8y/r1JJGXl4NRpCABXopOYsOEYXk42jG3pTfualVE+we+iPXv2MHHixEeOOXv2LPHx8Ya/5cbGxmRmZqLVatHpdJibm/PFF19gZWVFr1eG8r9VB7mZkAaArZkxPw1qjffdBOv9UsJiuPj7XsL2nkO+r4rbyMIUr/6t8B7YGlObp+tVrVAoqP96N8zsrTg9fwvIMqHbT5OdlE7rmcMxMjN5qv0+qSdpp1XBzJg+davRp241EjKy2Xv1DjuDIjgbEW8YczUmmasxycw/cJF6Vezo4l2Vzt4uhbaouWf9+vXUq1ePWrUecvUAiIqKwsnJiczMTKZPn17gAoJGo2HKlCmsW7cOgIyMDBQKBQqFgvT09Cc9LUIxEklbQRAEQRBKxeP6fynQVye8yAnbwpgaqWhazYmm1fRTClOzczkbkZfEvb8vcK5OKjRhC3lT8+bsCaS9Z+VSaZXwqB5q91u/fj3dunXD0dERQPRQE0qFQm2CxeBTyFn6Kk5JkkhMTMTOzs5QraUwc0RpYoOx39sY1XkdTdDv5JyejZyiX4RLF3mUzC3dUDo10lfe1uiFQlH06eBSVgKZGzvmbdBmIqXcwGpsDArT/NXpUmo4WfvHYZoQRIbaBJN6b2Ds9w5Syk0ytw8GTTpGtYZg0kS/yKAuMYicox9i3vuvZzlN2Ff2xH6mfvHEC3cS+OOLLtRPC6GSjxctauRNdZYkiZjVHVF4vo5j1ylY/NMIK5M7YGLDtd3fYOI2hIajvkf1pRWpCZFY2lYiasurVB+8Md/zZSenc2zWeu4cy+thbuflQuvPhlPhIZV6glBeVKjqQNfF47H1rMLNXWcACFp/mPgr4Xz4+fuG6f0AFy9ezLdg6IMXfh5cMLR3r1707tULSZY5GBzJ4qNBhmrWazHJTNx0nJqO1rze0psOXlUem7xdvHgxWq2W6dOnP3LcRx99hJOTEz4+PgB89913NGjQACsrK5o0aUL9+vUZOHAg+48H8Oqqg9y5W4nvaGXGL4NbU/2BJH5SaBQXV+zh1oELIOe94zO2NKPWwNbUerl1sS1aV6t/K8xsrTj6+WokjY47x4PYO+Fn2s8Z/dQJ4Sf1NO207C1MeaWhO680dCcmNZNdV2+z80oEl6PyZqSdv5PI+TuJzN17nkZuDnT1rkpHryrYmOdPSC9dupQxY8bk2zZt2rSnuoDg5KT/nT9z5ky6d+9uuE8oOwpZlp+t2dx/VGpqKtbW1qSkpBQoK3/RPGpl6vKivMdY3uMDEWNxETE+u/IeH4gYn8Xea3eYtOk4UHj/r9KuEn2c8nAe49OzOX23lcLhkCgSMnIe+5hevq70quNGncr2xbY4W2FGjx6Nvb09c+bMeegYnU6Hh4cH33///UOTu++99x4dOnSgSpUqxdJDrah9d8PCwnB3d6dOnTpotVrUajUbN27E3d2dmzdvMmjQINLT0xk6dKjhw3VQUBBTpkzh77//fqrYnlZ5+F58nPIeY1HjkyUtmmtryT31FVLS1Xz3KSvWxaTJx6g9+qNQPvkU3Jwz36C7fRjzl/J//8iyTMbaxhg3nEyidRv9BY6sOJQWTmQfnoSyYl2Mag0lY2VtLAafBiNLMjd3w6zDTyitqz9xHA/zwZYTvBLQhkXJPeg15lv61cvb96ktc5ACvqDJzGRiwy9jvkX/86lyac+Ra1moravRctSPhH7jRLV3IzixagJSbgadJ+Yda/TZEI7OXE3WfRejfAa1xW9sd1RGxfe76kX5XixNCddus330d48d133pe+Wm13BZnkdZlrm+5TgBC7YY+rOa2lnR5rPhONXPa0XwLDHKssyhkCh+ORrE5ej87aXcK1bg9ZbedKrl8tCLtDVr1sTGxoZTp0499DkkScLExIR3332Xr7/+utAYGzRoQEv/ThyIk7i25TcAfAe8ztZvplLFJm8Kf+L121xYvpeIw/kXsDOxNsf7lbZ49W+FscWTtz0pyjmMPhvCwY+WocnIBqCCqwMd5r2OpbNdoeOLW3F9L0YkpbMrKIKdV24THJdS4H61UkHz6k508a7K/9m77/CoyuyB49+ZSTLplfRKQgihl0hTkC4qIKIoygq4gqLYZQ0qsqi/FVwr7Cqgsi6woihYAEV6byFAgEAILb333iYzvz8mDMQEMpCETMj5PA9PZua+986ZSzJ35txzzzu0oxe2auPbSZni+86ftYYYm4qxOUWptBVCCCHELWNs/y9xRTtbS0M/tE2nE5mz/tpfvi7bEJ3IhuhEzJQKQj2c6O3bjt6+7ejl067J+gQb00MNYPfu3VRUVHDPPffUu7w5eqgZ23cXwM7OjmPHjtX5kvD5558za9YsJk+eTOfOnXnhhRewtbXl5ZdfZunSpTcVl2gdFEozLEKfwDzkcTQXfqIi4h9os08CoM0+Sdnvk1A6hWBxxxuYhzyGQmX8l+aq6P+gvvP9Oo9XJ21HoVJjFvwwZOr76yptaqpcleagKQVtFei0oFBSdXIpZv4jmzRhm5JfQtzub3BwriY+8C/c38Wv1vKChMOY6WzYMa89jqRTUKLDww6CeveiZ++/cG75SM584EWex0RccpKxTNtM//npAGg11Zz8ZgunVm43VN6pHW25861JeA8IbbLXIMStolAoCHlwIC4hPuyeu4LSzHzKc4vY+vJSujw+FN+7u6FQKNBptRTm5WGWV4mi5vhi6WBj6Nfa0HMMCfbi7g6e7LuYzrL9MZxKzQX0/e/Dfz3M0n1nmDEwlHs6+2B2VZIrPT2d8+fP89tvv133ORYvXoxWq+WFF16od/k333xDRlY2J33uJObfkwiY+gZe9tac+PJdvL/Wt1PIOp3AqRVbSTkQU2tdSydbOj82hI7jB2Ju3bztCjx6d2DUv2exY/ZXlOUUUpiYxR8z/8Xwj2fg1MGr4Q2YCF8nW6YPDGX6wFAuZBXwx5kkNsckk5inb1Gg0erYezGdvRfTsVApGdTBk9Ghvgzq4IFVE574EqZD/leFEEIIcUvdSP8vUVs72xurUNFodZxKzeVUai4rDp8DoIOrPX18XelVk8h1t7t2n7TrMaaHGsB//vMfHnnkEVSqulWJt6qHmjF9d//sct/dqqoqtFotSqWSpUuXMmrUKNq3b7pEmTBdCqUK844TMQt+GE3cRioO/wNthv6kiTYvlvIt06g49A7qO8IxD52Kwuz6SQlN6gF0FXmYBdatOK/OOYPCypXyTY9jmXWaMucOWN39MUqHQCx6vUjZ5iepPPUlFr1fQ1dRQNWFdVg/uLlJX+/qyAvcU7WBX9MCefTxUNR/msxHV60hxD6DwsHraFd6hCO/LKCkElRuvfAI7InHP65MJrZjri8eo5dw7LfPKDr8GaUFFaSfHomlTn9izqNPMHe+/TjW7W7vKxbF7a9dZz/u/88r7HvnW9KOnENXrSV61XaiV22/5jpKCzPGr55jVOIW9MnbQR08uSvIg4NxGSzbF0NUSg6gn8TqzQ0RLN13hqfvDOXeLr6YKZXMmTMHOzs7wyXu17J48WL69++PhUXdE7qlpaW8+PLLBD39Lvlllei01bR3sWf28O5M+LyKzJNxnPzvFtIiztVaz8rFni6ThxI8rj9mlrduQlnnYC9GL32B7a9+SWFSFmU5hWye9TlDFjyJR+8OtyyOptLB1YHn73Zg1uAuxKTn80dMEptjkkgv1Bc9VFZr2R6bwvbYFKzMVQwN9uKezr7cGeiBuap2lWq1VsfRxCwupmYRVK6Qz92tiCRthRBCCHHL3Uz/L2FcX+B2tpa8OKQrUck5HEvKNkwWctmFrEIuZBWy5thFAHwcbfRVuL7t6OPbDj8nW6MmXDKmh1pBQQE///wzO3bsqHcbt6KHWkN9d0tKSujXrx/l5eU8/PDDzJ07F5VKxYsvvsi0adNYtmwZs2fPpqCggLVr17Jly5YmiUu0HgqFAvPAsZi1H0N14jYqIv5BdYp+UkVdYRzl22dScfg9LPr8DYuu01GY19+rser0csxDn0ChrOcrmE6DJmkHVo/sp7DaFauMnyn97VFsHz+C0sYTmwl/GIaW/vYIloM+ojp5J5Unl4JKjeWd76O097/p11hUXsXGiGOs9k3n6fw3WNU7qM4Yq3bBxJ+zp3+/B8j/+Rt6+oCVOWgcOnF1rfHeVX+j0tKHzoMe5cTbas4dm0mRNgr3oM2UXpxOz+mj6TJ5qKHqUJgGSwcblBZmaCs11xyjtDDD0sgZ7dsSS0dbhn00Q19N/t+tDY7XVmooLygxOml7mUKhYGCgBwPauxORkMnSfTGGSawS84qZu/EIy/adYfqdoaxbt46JEyfWWn/w4MH4+PiwevVq/TqJicTFxfH111/X+3xD7rkXy45h6Jz1J1u63z+JvR+/zt6P4NFeI9n83L9rjbd2c6TrX4bR4f6+qG7gsv2mZOvpzD1LnmfH35aTE5NIVUk521/7krvmTcZ/6M21XGppCoWCzp5OdPZ04uWh3TiRksMfZ5LYEpNMbqm+ZVZZVTW/n0ni9zNJ2FmaMyLEm9GhvoT5u7LrfNqfrnCLxV2ucGs1pKftTZKetqbF1GM09fhAYmwqEmPjmXp8IDE2FYnx5txoX+Dc0gqOJ2VzLCmLY0k5nM3IQ3udT38uNmpDK4U+fq4Euzo0qhqjpffh9fruVlRUUFBQQLt27YiNjeWFF15g1KhRdWZVBpg4cSJvvfUW2dnZLFmyBLVazYIFC/D3v/lE2Y1o6f1oDFOPsSnj0yTvoSLifaoTayfxFdZuWPR+FYvuz6KwsDM8rqsspugrL2wei0DlXLc6ver8Wiqj/oXVQzvJzMzE1cmGkiWO2L1QXqv9QtX5dVRnHMHyroUUr+iMzWOHqc6IpOrMCqzu+e9Nv54Vh89xbMUzjLU5zIFhe5l3b586Y4rzM0lc5IP9Y/ux3f0IR6LjcbVT0OPdKkN/3/yMBM78qwddX47lzDd7cSh+lEPbn6bC6iy+wcfo+9xJXLs2/99MW/pdbEol6XmUF+gnmdJpteTm5eHs5HTDl/XfKqa4H2N+2EPk4oYnB2yq3sBHapK3kYlZtR73crBm+sBOjOsWUKfq8s/q24/bYlMI/+UQmpoPDHe2d+N1/3bErtpB5sm4WuvbejrT9YnhBN4b1qS9qa8XX0OqyirYO28VKQdrWjYoFPR95UFCJtzZ5PHdbIyNpdFqiUzI4o+YJLbFplBUXlVnjK2FGcX1nIiRuSRanvS0FUIIIYS4Dd1oX2BnazXDQ7wZXvN4SUUVJ1L0VbjHkrI5lZpLZbXWMD6npIKtZ1PYejYFAFu1GT2929HbT99OoYuHExZmxk3A1NKX4zXUd1etVuPm5oZWq8XJyYknn3yS77//vk7S9vLkZD179iQ0NJSIiAgiIyOZN28eK1asuBUvRZgYM5/BmPkMpjo9goqI99Fc0k+2pSvNpGLfHCqOfIC610tY9HwBhaUTVefWoHLtUW/CFsAs4F7K94ajLU4BzKmO34TSObRWwlZXnk9l1GKsH9RX3eo0paBQgkKJrurm24lUVWtZHXme120P80vxAF7tG2xYtnnBYFR2Pox4fjW2jm4UdXuHslV3k1FdhrUF4BBca0K2iC/uxTLwZXa9spKCuHTKnEO4Y/BXVOtUqAZ/fksStuLm2Xg4GZKyWq0WTaYFzm0gedKU3HoEGjXuwPvf4Rjoia2HM7Zezoaf1m6ON5T4vMPfjTv83TiamMWX+2M4FK/viZ1aUMq7m47x1f6z/HVACOO7Bxh97N5wKoG//xZJtU4HOh0TLFSEbT/O3pjEWuPsfNrRbcpw2o/qg9LIbd8q5lZqhix4kkP//JGLvx8BnY6IT36iNLuQnjNGG3VFkakzUyrp396d/u3deeue3hy4lM4fMcnsPJdCWZV+crz6EragP+mvAP65NYqhwV7SKsGESdJWCCGEEKKVaUxfYBu1OQMDPRgY6AFApaaa6LS8mmrcbKJSsimuuPIhv7hCw75L6ey7pJ9QSG2mpJuXM7189EncHt4u2NRzGeS22JQWvxyvob67mZmZODk5oVKpqKio4Oeff6ZXr161xuTn57No0SI2b9b3Dy0tLUWpVKJUKpu0765onVQefbEe9wvVWSeoiFiA5vyPgA4q8qg4NJ+KYx9j0WMWmsRtWHR/tta65QfnobTxwqL7TBTmNlgNX0L5+nFYaqqotHbG6r7vao/fF466/99RmOn7UKv7vkXJ6jtAZYHVyPovbzbGttgU0gvLeNVpJYPCPGjvcqXi55439tQa22/CG1SG9aP8pxEAmPcYaVim0+kI6LWcyMW/UF2pb8tiWzIO2wHj6TCm322RJBGiqeRfTCf/YnqdxxVKBVbtHLD1dK7554StpzM2l5O67RzqTZD28XNlmZ8rUcnZLNsXw4G4DADSCkv5x+bjfH1An7x9sEd71GYqQ1W1VqsjJj2X5Kw8fFwzSM4v5ZvDsVibm+FeWMro5EwsM/LIveq5HALc6TplOAHDeppcsvZqSjMVA954FCsXe0OP4eiV2yjLKaT/3x426dhvlLlKyd3BXtwd7EVZlYZ9F9NZHXnB0D6jPjogo6iMY0lZ3OHvduuCFTdEkrZCCCGEEK1QU/UFtjBT0btmUrKn0FfHnsvMN1TiHkvKNvRMA6jQaIlMzCYyUf9FQKVQ0MndUd8T10/fVuFoUjazfzpYp+9uZlEZs386eMsux2uo7+6+ffuYN28eKpWK8vJyRo4cyVtvvVVrfHh4OPPnz8fKSp8omzt3LmFhYVhYWLB8+fJmfw2idVC59sD6/u+pzp1P5ZGFVJ39FnTVUFlE5ZGFYGaNNvsU2uJUlLb6mcwtB7xbaxtm/qOw9h1xzUtDrUYsq3XfotsMLLrV/v2+UTqdjlWHr0wiNKVfxwbX0WYeN9xWuepPclQUlnLonz+SuOukYZlTkCeD3nkChwD3RsUoRFui0+oozcynNDOfzBOX6ixXqJTYuDliY0jq1vzzcMLWy4UeXs4smTSIkyk5fLk/hr01ieGMojIWbIni6wNnmRbihWLhmjr9ixNqfk7hSiXm1RyDPOk2ZQR+Q7qjbKDlgqlQKBT0euY+rFzsOLLoV9DpuPhbBOV5xQx+94lbOlHarWJlbsbITj5oqrXXTdpeFpuRL0lbEyZJW2FycnJyGD58uOF+aWkply5dIjMzE2dnZ8PjxcXFPPTQQxw9ehSNRsPZs2cNy+Li4pg0aRLFxcVMnjyZN998E4CYmBjCw8NZv379rXtBQgghRCuiUioI9XAi1MOJyXcEo9PpSMgt5lhyNscSszmWnE1KfolhfLVOx+n0PE6n5/G/I+cN26ivbe6tvhzvwIEDdR57990ribIJEyYwYcKE6/ZQW7asdqJsxowZdRLBQlymcu6E1T3/Rd1/HhVHPqDqzH9BWwWaUiqPf0blySWYd3kSdVh4oyYOayrHk7M5nZ4HQIi7I3f4udY77upep8qLB7hcn1ZU6k3eb4eJ+mozZdkFhvEhE+6kz6yxLTYZkRCm7p4lL6C2t6I4LY/itFxK0nIpTsulOF3/s+Kq4+zVdNVa/bi0XDLqWa40V2Hjrq/O/YunMw86WLI3r4RDBWUUWKvJ0ulYseMkU64z4RzUTtg6d/Sm29SR+A7q0monEOz08CAsnezY/3+r0VZVk3LgDNteXsrQD55CfZtOrtfO1tKocR9vP0lsZgFPDehEgItdwyuIW0qStsLkuLi4EBUVZbj/0UcfsXv37loJWwBzc3PCw8NxdnZmyJAhtZZ9/vnnzJo1i8mTJ9O5c2deeOEFbG1tefnll1m6dOkteBVCCCHE7UGhUBDgYkeAix0TerQHIKOw9EolbnI2F7IKa61TfZ2Zzi5fjvev3afoF+COq60lbnZW2KnN5fJpcdtQOgRiNWIZ6n5zqTz6IZWnvobqcqiuoOrkUqqiv8a801+wuOMNVE7BDW+wmaw8fN5we0rf4Hr/BkvS8/jl8YWGqrwRo7dhYwtarYJNr/2BTnflEmMLOysGvPEofoO7NX/wQrRiKgszHPzdcfCvvxK9qrSC4vRcStLyKE7LuZLcTc+lODWXyuKyetfTVlVTlJxNUfKVCsugmn8AVUolJUaeTHEIcKf3s2PwHhh6WxyfA4b3RO1ow+43vqGqtIKs6AT+eO7fjPj4aZOaYK+p9PZ1xd3OisyisnpPpF+mBdafSmBjdAL3hPoyfWAnOrg63KowRQMkaStM3vLly1mwYEGdx9VqNcOGDSM+Pr7OMnNzc0pLS6mqqkKr1aJUKlm6dCmjRo2iffv2jY7J2GpggI0bNzJ79mwqKyvp0aMHK1aswN7eXqqBhRBCtFru9tbc28WPe7v4AZBfWkFUzeRm22NTSL5GhdDVvjl0jm8OXbks29JchZutFW52lrjaWuFmZ4WbrRWudvqkrqutFW62lkZPpGKslp4sTdzelHa+WA5ZjMUdb1F57BMqT34BVSWg1VB15r9UxazErOMk1H3fQOXSBU3iNix3vIBm2L+wCBjVrLEl5Bax63wqAK62ltwT6lvvuPKCEkPCVqWqxNpG369Wq1Wh012punMK9mbowiexcb/9kh9CGMvSwQalhVmd1gNXU1qYYdlAdae5tRqnQE+cAj3rXV5ZVGaoytVX6tYkd9PzKE7NRVNWUe965lotjtdY9meOT92Dz52djRrbWnj2CWbU57PYMfsrynKKKEzIZNPMxQz/+Gmcgurf162VSqng9ZE9mf3TQRRQK3F7+f49nXw4EJ9BUXkVWh1sOpPEH2eSGNHJmxkDQwlxd2yR2MUVkrRtg1pTwvHAgQPk5eUxZsyYG1rvxRdfZNq0aSxbtozZs2dTUFDA2rVr2bJlS5PEZWw1cHFxMU899RQ7d+7E2dmZ9957j/fee48PP/xQqoGFEELcNhyt1QwJ9mJIsBeDgjyYvnpPwyv9SXlVNYl5xSTmXX9yLycrC1xrErr6ZK5lrZ9udlY4WatRGlEVZAqTpYm2QWnjjuWgD7AIe53KqEVUHv8XVBaATosmdjWa2NWogh5Em3cWZeF5Kg/Mxdx/5E1Xt+l0WtBqrvzTadD96f5v+07ir0hGiZYnQgNQZh1FU7Ps8jidVoMiIx1PrwsoFFpcPeK5HJKZmQZX9wSyMgIA6P+3hyRhK9o8Gw8nxq+eY2gnotNqyc3Lw9nJydBawNLBptGVnRZ2VjjbeeMcXPdYpdPpqCwsNSR0r/5Xkp5HQXI2aKobfI6C8qpGxWiqnIO9uWfJC2x/9UuKkrMpyy5k86x/M3ThX3HvGdTwBlqRESHefDRhwJ8+64DbVZ91iiuqWHP0IqsizpFXVokO2Ho2ha1nUxgS7MnTd4bSxdP52k8impUkbdug1pRwXL58OVOmTMHM7MZ+VT09PQ2zPANMnDiRjz/+mJ07d7JkyRLUajULFizA379peoldqxp406ZN9OrVi06dOpGZmcmzzz7L6NGj+fDDD5u9GlgIIYRoCcZcjudoZcHTd4aSU1JOZlE5WcVlZBaVkVlcRnHF9fvs5ZVVkldWybnMgmuOMVMqaqp1r6ravTqxa2vFmfQ83lwf0eKTpYm2RWnlguWAd1H3fo3KE59TeexTdOU5AFRf/NkwTpsZScnKzmBuW5Nkrb5m8vXP99Fe/2/osr8Af7nc8jAaSqLrH2cG9B1Y93GdDkK7HCArwx9QoFDdPjOxC9EYNh5OhqSsVqtFk2mBcz0905uLQqFA7WCD2sEGl051K+j37DxBwtsrG9yOo9XtN0nXZXZeLoxe8gI7Xv+anJgkqorL2fbqlwz6+2T87u7e0uE1qREh3gwN9uJoYiYXUzMJ8nKrdVWRrdqcpwZ24rGwDvx4/BIrDseSU6Kvxt51Po1d59O4K9CDp+8MpYePyw09d0VFBa+99hqbN2/G0tKSHj168L///a/WGK1Wy+uvv84ff/yBRqOhd+/e/Oc//8HS0lKuTkaStgLTTTgWFxfzww8/cOTIkUZtZ926dQQFBdGzZ09CQ0OJiIggMjKSefPmsWLFikbHeb1q4MTExFqJ4YCAANLS0tBoNM1eDSyEEEK0hIYuxwN4+94+10yIllZqDAncrKJyMmsSulmGn+VkFpWhuU7fXI1WR1phKWmFpTcc/62eLE20TQq1A+q+b2LR6yUqTy2j4siHUFZ7WiFtXmwLRWcchQKcnDNqVdsKIUxfqIczCUaM63SbV85bOtkyctGz7Hl7JamHz6Kt1LB77kr6vjqBkAcH3nDCsby8nMGDB7N06VIsLCxMKuGoUioI83PFz1KHm5sryno+21hbmDG1X0ce7R3ETyfi+OZQLJk11bn7LqWz71I6/QLcePrOUMKuMWnln82ZMweFQsG5c+dQKBSkp6fXGbN8+XKOHTvGsWPHUKlUTJkyhcWLF/P666/L1clI0rbNM+WE45o1a+jRowedOnW66W3k5+ezaNEiQ9VtaWkpSqUSpVJJcfH1L780VmupBhZCCCFuFWMux7sWawszw8Rn16LV6cgrrTAkcK8kdctrJXjzyipvKv7Lk6UdS8riDn+3m9qGEMZQmNug7v0qSodgyjY8UM8IJajMQWkGCjMUSrOa2yr9T2XNYwozw/1a4+q5r0XFzguZlGp0aFExMtQfGyvLuttRmqFQmFGaW8rZtftp3+EEVlbFXN2xQatVXFVtK4RoDepL2DVmXGtmbq1m6Ad/5eDCH7j0RyTodER8vI6ynEL+G/2H0QnHyMhI8vLyePvtt1m0aBF/+9vfWm3C0dJcxeNhHXi4Z3t+ORnPN4diSS3QnwQ/HJ/J4fhM+vi24+k7Q+kX4HbNNj4lJSUsX76c5ORkwxgPD486406cOMGIESOwsLBAq9UybNgwFi1axOuvvy5XJyNJ2zbPlBOOy5cvZ8aMGbUemzdvHl5eXsycOROA7t27k5WVRWFhIb1792bYsGG1zn6Fh4czf/58rKysAJg7dy5hYWFYWFiwfPnym47tsoaqgf38/Ni6davhfnx8PJ6ennX2d3NXAwshhBC3WkOX4zWGUqHAxcYSFxtLOl1nkoxKTbU+sXu5aremgvdEcjZRKbkNPs8HW6N4akAnhnT0wsq8aT82G1vBM3v2bP744w/MzMyws7Pjm2++oWPHjiZVwSMaR6fTURHxnj4Rq7uqz6RChdKtFzaTDjfpzO3rT8bz9qlIAIaHePPY/QOuO744NpmCgvNYW9cteFAqdYZqWyFE69BUk6XdLpRmKga+NQkrFztOf7sTgMjlv/PV8a9JSU0xOuGoUCgYPXo07777Ln/7299afcLRwkzFI72DeLBHezZGJ/D1gbOGiWaPJmXzzPd76e7lzNN3hnJXkEed49TFixdxdnbm/fffZ9u2bVhZWTF//vxa8ysB9OnTh2XLlvH888+jVqvZsGGDYbJ5uTpZkrZtmqknHA8cOFDnsXfffbfW/ZMnTwL6LzWZmZm4udWuhlm2bFmt+zNmzKiTCG6MhqqBR48ezaxZszh79izOzs4sWbKESZMm1RpzK6qBhRBCiJZgzOV4zcnCTIW3ow3ejrW/eB5JyDRqsrTzWYXMWR+BjYUZo0J9GNvVn16+7Yya5KwhxlwyuH79evbv38+JEydQqVS88cYbvPXWW/z444+ttoJH1FWdsAVtRmTdBbpqtBmRVCdswSzgniZ5Lp1Ox6oj5w33n+gbbMxKhHY5gE4H9f3qX+5ti+7aLUuEEKbj6snStFodMem5JGfl4ePqRKiHM0qlokkmS2tNFAoFvZ8dg5WLPZGLfyWjPB9rzHn2vsmcq8rA2tr6ugnH5557jqqqKn788cfbLuForlLyYI/2jO3mz6bTSXx94CzxuUUAnEzN5fkf99PZw4kZd3ZiSLCX4TOSRqMhISGBzp07s3DhQo4fP87IkSM5ffo07u7uhu1PmzaNhIQE7r77bqysrOjfvz979ug/o8nVyZK0bdNae8KxJD2v1qychXl5mOVVNumsnA1pqBrYzs6Or7/+mgkTJlBRUUH37t1ZubJ20/fmrgYWQgghRG3GTJamVCjQ1iShSio1/Hwinp9PxOPtaMOYLn6M7eaPr5PtTT2/sZcMKhQKKioqKC8vx9ramuLiYnx8fABafQWP0NPpdJQffBtQAtp6RigpP/g2Nv6jmqTa9nB8pmESv66eTvT0bnhSGbWdOdXWRfUmbEGfyLWyLkJtZ97o+IQQt8bVk6W5hHgbCqBu1WRppir0kcFYOtvxbfhnZFcUYp+r4x/Dp+H0eB/uf3DcNROOQ4cOxczMjNGjRxsK3263hKOZUsnYbv7c18WPrWeT+epADBeyCgE4k57HK+sO0tHNgRkDQxnRyRs/Pz+USiWTJ08GoFevXrRv355Tp07V2ocKhYL58+czf/58tFotX375JV26dKnz/G316mRJ2rZhrTnhWJKexy+PL2zwko7xq+c0a+LWmGrgcePGMWbMmGseCJu7GlgIIYQQtRkzWdo/x/fDyVrN+lMJbD2bTGnNZ46U/BKW7Y9h2f4Yevq4MK6bPyM7+WBvafws28ZeMjh27Fh27tyJh4cHdnZ2uLm5sW/fPuD2qeBp86or0RUlUX/CFkCLrigZqivBTN3op1sVcaXKdkq/jkYlgi0cHNi6byoqClCqVPR5+QHKNJU42NsbSm+t2/li6133xIMQQrQ27Uf0YvwHzzJv5CoGunYi61Q8lcvK8Pfxu2bCcd68eWRmZrJjx47bPuGoUioY3dmXUaE+7DiXylf7YzibkQ/AucwC/vbLIQJd7Jg+MJRhw4axefNm7rvvPuLi4oiLiyM0NLTW9srLyykrK8PJyYns7Gz+/e9/849//KPWmLZ8dbIkbduw1pxwLC8ouW7CFkBbqaG8oKTFLuswhUpgIYQQQtTP2MnSwvxceWNUT3aeS2X9qQQOx2egrcnyRiXnEJWcw8ItUQzt6MXYrv4MCHTHrIFKJWMvGYyMjCQ6OpqUlBRsbW156aWXePbZZ/n2229vuwqetkphpsbmsQh0ZVmAvuVXbm4uzs7Ohs/dCis3FE2QsL2YXci+S/o2HF4O1gy/zoSAV7uwMYLifEvAkuAHBuB+93gyMzNxkqo8IcRtquuI/tx9512crcqks7kHF86e43xMLF7Wta9OuJxwdHBwICcnh3/+85+89957tcbcrglHpULBiBBvhnf0Ys+FNL7cH0N0Wh4Al3KKeHNDBC6DHyf87+/xeng4KqWSZcuW4e3tzfTp0xk3bhzjxo2joKCAIUOGoFQq0Wq1TJs2jbFjx9Z6rrZ8dbIkbYVoBqZSCSyEEEKIazN2sjQrczPu6+LHfV38yCgq4/fTiaw/lcClbP1lgZXVWjbHJLM5JhkXGzX3dfFjXDd/Oro51vu8xl4yuHLlSoYNG4ajoyNarZZHHnmExx9/vM72bqcKnrZIaecLdr4AKLRadGSiaoaE6P+uqrJ9PKxDgycXALSaas7+uNdwP/SRQU0akxBCmKrlK//LtL9MYXX0dnRV1UxrP4xT767jH9YLeXTa5DoJx8rKSl555ZU2l3BUKBTcHezF4A6eHIzL4Mv9MRxPzgEgx8wO5YTX8HKw5qkBnRjXPQCAr7/+2rC+u7s7MTExwJW5iv6sLV+dLElbUS9TqBLVaqqpKCylPK+Y8vxiKvKLKc8voTyvmIL4upN11Cfq6z+wdXfC3FqNmbX6yk8rda3Hrn5cpTZvdM+w1lAJLIQQQogbnyzN3c6KJ/uHMK1fR2LS89kQncCm04nklVUCkFNSwaqI86yKOE+ImwNjuvlzfxc/XGwsDdto164dw4cPb/CSwcDAQH7//Xdmz56NmZkZW7durXPZZXNW8FRUVPDaa6+xefNmLC0t6dGjB//73/9qjdm1axf33nsvISEhgL6K+PDhw9jY2BAZGcn06dOprKwkPDycqVOnArBjxw7WrFlT50uYaD45JeVsjE4AwFZtxoM9jOt7nLQnmpIMfeWU94BQHPzd0Wqv1cpBCCFuH4GBgew5sI+yvCJ2/m05OWeTqCwu477KAHo66t9DLyccrzUxOrSdhKNCoWBgoAcD2rsTmZjFsv0xHEnQX0WSWlDKe38c48v9Mfx1QAgP9miP2kzVwhG3DpK0FXU0V5WoVlNNRUGJPvGaX0x5Xu1ErOF2zbLKorJGz0KbejDmhtdRqJSYWVnoE7lWdZO6V5K9loZx5n+6X5KZ36i4hRBCCGHaFAoFnT2d6OzpxKvDurP/UjrrTyWw+3wqmpr+CbGZBcRuP8lnO04xMNCdsd38GRLshdpMxdKlS3nqqacIDw9HeY1LBmfNmkVMTAw9evTA3NwcJyenOlU5zVnBM2fOHBQKBefOnUOhUJCeXv9J85CQEKKiogxfWi/HsnDhQhYvXkyfPn3o1q0bU6dOpaysjPnz57N+/fpGxSZuzA/HLlJZrU+2TujRHlu1cZOGnVmz23A79NHBzRKbEEKYMisnO0YufpY9c1eQGhGLtlLDnrkr6PvqQ3QcP6ClwzM5CoWCO/zduMPfjePJ2Xy5L4YDcRkAZBSVsWBLFF8fOMvUfh15uFcgVub6tGS1VsfRxCwupmYRVK6o98qntkiStqIOY6tEy3ILUZqrDJWwf06+VtQkXy8vqywsvUWvoHF01VqqisupKi5v6VCEEEII0QqYq5QMCfZiSLAX+aUVbI5JZkN0AqdScwGo1unYezGdvRfTsVObMyrUh7Hd/NmxY0edq3uuvmRQrVbz1VdfAVyziqe5KnhKSkpYvnw5ycnJhhg9PG5soilzc3NKS0spLy9HpdJX1MyfP5+XXnoJR0fHRscojFNeVc2aY5cAUCkUPB4WbNR6WdHxZJ/WV+c6BXni0ce49YQQ4nZjbq1myAd/5eDCH4jbfBSdVsfhj9aSE5tM8AP9QaeTOWzq0cunHUsmDeJUai5f7Y9h94U0ALKKy/lo+0mWH4xlSt9g3O2sWLQr+qo5BmJx/9McA22VJG3FTdv09OJm2a65tRq1oy2WjjZYOtleddsOS0cb1I62VBSUsP+91Q1u6+5/TMPG3RFNWSVVpRVUlZbXvl1aQVVZ5ZXbpRU1y8v1t2uWN7biVwghhBBtg6O1mkf7BPFonyDic4rYEJ3AxugE0gv1X0SKKqpYFxXHuqg4fB1tGNPNnzFd/fFxtGnhyGu7ePEizs7OvP/++2zbtg0rKyvmz5/P8OHD6x3bu3dvVCoVDz30EK+//joA8+bN45lnnqGkpIQPP/yQqKgoLl26xAcffHCrX06b9tvpBPJKKwAY2ckHTwdro9Y7833tKtvGtg8TQojWTGVuxp1vTcLKxY4zq3cBcGHDIS5sOFTveJnD5opuXs4snngnMel5fHXgLNtjUwDIK61g0a7oetfJLCpj9k8H+WjCgDaduJWkrWh25tbqPyVfL9+uJzHraIvKiMu1cmKTjXpuGw8nXEJ8GhW/TqdDU15pSOBqahK6+gRvxZXbpRVUlel/FqfnkXrobIPbPrLoF3o9fS9uPQLlg7AQQghxmwlwseOFu7sya3AXjiRksTE6ga1nkymrqgYgKb+EJXvPsGTvGfr4tmNsN39GdvKpc+l6S1wyqNFoSEhIoHPnzixcuJDjx48zcuRITp8+XWuytN69e5OcnIyDgwOJiYmMHj2agIAAJk2aRGhoKHv27NG/hupqRo0axapVq/juu+9Yu3Yt9vb2fPLJJzg5yRfa5qLV6WpNQPZEX+OqZYvTcknacwoAS2c7Akb0bpb4hBCiNVEolfR5biy6ai0xa/Zcd6zMYVNXqIcTn0wYwPmsAr7ef5Y/YpKuOVYHKIB/bo1iaLBXm22VIElbcdMcAz2w9XIxVL9aOl1JxBruO9gYlYS9UZYONigtzBrsu2vp0PiqFYVCoe9la6XGysh1cmKTjUraZp2MY8vzX+AS6keXx4fgO7gbSlXTzhQshBBCiJalVCjoF+BGvwA33hjVi+2xKWyITiAiPpPL1/IcTcrmaFI2C7YcZ1hHb8Z286d/gDs7z6fyz61Rt/ySQT8/P5RKJZMnTwagV69etG/fnlOnTtVK2trb2xtu+/j4MH78ePbt28ekSZNqbe+zzz5j4sSJODo68t5773Hy5ElWrVrFZ599xjvvvNNsr6Ot238xnUs5RQD09m1HVy9no9Y7u3YvuprezCET7kRlIV8bhRDisvaj+jSYtBXXFuzqwAfj+3FXkAdzNx655jgd+j64x5KyuMO/7iRvbYEcfcVNG/jWY42uYr1ZNh5OjF89h/KCEgB0Wi25eXk4Ozm1yh4yOTGJ7Hl7JXbeLoQ+ejdB992BmaVFS4clhBBCiCZmbWHG2G7+jO3mT3phKb9FJ7L+VALxufrEWoVGy6YzSWw6k4S9pTmF5VV1tnErLhls164dw4cPZ/Pmzdx3333ExcURFxdHaGhorXFpaWm4u7ujVCopKipi27ZtPP3007XGxMXFsXXrVjZt2kRhYSEajQaFQoFSqaS4uLhZ4hd6q47ceJVtZUk5FzYcBkBlYSYT7QghhImqqKjgtddeY/PmzVhaWtKjRw/+97//1RqzY8cO5syZQ3FxMdXV1YwbN44PPvgApVJJXFwckyZNori4mMmTJ/Pmm28CEBMTQ3h4eLNPGmpmZPVsdhueb0hK+kSrdbn1gUuID84hPtgHuuNcc98lxKdFE7aXK4GvR2lhRthLD+AU5Gl4rCglh4hPfuKnh/+PE8s3U57XuC8yFRUVPP/88wQHB9OtWzf+8pe/1BkTHx/PkCFDcHJyYsSIEbWWRUZG0rNnTzp37syKFSsMj+/YsYNnnnmmUbEJIYQQbZ2HvTVPDezEL0+P4n9Th/Fo7yAcrjppW1/CFvSVJzr0lwxWa5uv7/7SpUv58MMP6datG+PHj2fZsmV4e3szffp0wxe5devW0a1bN3r06MHAgQMZPHgwTz75ZK3tvPTSS3z22WcoFAocHBx4/PHH6datG59//jnPP/98s8Xf1p3NyOdwfCYAfk623N3By6j1Lmw8TFVND9zA0WFYOto2W4xCCHE72zNvJadWbKMwObtZtj9nzhwUCgXnzp3j1KlTfPTRR3XGODk58f333xMdHc3mzZs5ePAgK1euBODzzz9n1qxZnDx5khUrVlBUVIROp+Pll19m0aJFzRLz1drZWjbpuNuRVNoK0QxupBK408ODSDtyjtOrd5Ieqa+GqMgv4eQ3Wzi9eidB991B50l3Y+fd7objuPpNXKFQkJ6eXmeMvb09//d//0deXh5vvPFGrWULFy5k8eLF9OnTh27dujF16lTKysqYP39+k511M+bsYHx8PNOmTeP48eP4+vpy8uRJw7LIyEimT59OZWUl4eHhTJ06FdAnltesWVNnVm0hhBDC1CgUCrp5OdPNy5m/jejBngtprIw4R1RyznXXyygqY/yXf9DZwxk/Z1v8nW3xc7LF39kOB6vGX7ETGBjIzp076zz+9ddfG24///zzhsSrVqslMzOzTp/+P39mmD9/PvPnz290fOL6ru5lO/mODkb1A9Rqqjn7417D/dBHBjdLbEII0RYUp+QQ9dUmor7ahHOIDwHDe+I/rAe2Hsa1qrmekpISli9fTnJysuG46+HhUWdcr169AP0x+vL37fj4eADMzc0pLS2lqqoKrVaLUqlk6dKljBo1ivbt2zc6xob09nXF3c6KzKIy6jsFrQDc7Kzo7eva7LGYKknaijpuZb/Y25mNh5Oh2ler1aLJtMDZzQ2lsnaBu0KhwKtvCF59Q8g9l8zp73aRsOMEumot1RVVnPv5AOd/PYjv4G50eXwo7Tr7GfX8xr6JOzs7c9ddd7Fjx446yy6/iZeXl6NSqQD9F62XXnoJR0fHG9kd19QaEstCCCHErWKuUjI8xJtKTXWDSVuAxLwSEvNK6jzuaGVhSOD6GZK5+vvW0p/0tpdZVMamM4kA2FuaM65bgFHrJe2JpiQ9DwDvAaE4BLg3sIYQQghj5MYmkxubzLEvNuLa1R//YfoErnU7h5va3sWLF3F2dub9999n27ZtWFlZMX/+fIYPH37NdTIzM1m3bh0bN24E4MUXX2TatGksW7aM2bNnU1BQwNq1a9myZctNxXSjVEoFr4/syeyfDqKAWonby6cZXx/Zs81OQgaStBX1uN36xbYmzh19GPT3v9DrmfuIWbOHCxsPoymrRKfVkbjrJIm7TuLWM5Aujw3Fe0Anw/9HfW7mTfzP5s2bxzPPPENJSQkffvghUVFRXLp0iQ8++KApXm6rSSwLIYQQt5qxlwL++UvOZfllleSX5XIyNbfutm0s9VW5NUlcPyf9bV9HWyzNVTcVb7VWx9HELC6mZhFUrqCPn1ub/pLV0r4/egFNTeuMib0CjU7Ux/yw23BbqmyFEKJxhv7zKQriM4jfHkVubLLh8azoBLKiE4j813rcewQSMKInfnd3x9LJ+HY0Go2GhIQEOnfuzMKFCzl+/DgjR47k9OnTtSYMvaywsJApU6bwt7/9jbCwMAA8PT3ZvHmzYczEiRP5+OOP2blzJ0uWLEGtVrNgwQL8/f0bsReub0SINx9NGPCnSVf1FbbNPelqayBJW1EvY6tERfOw9XDmjpfG0/3JUZz75QBn1+6jvGaCksyoS2RGXcIhwJ3Ojw2h/cje9c7oe6Nv4vUJDQ1lzx79rJjV1dWMGjWKVatW8d1337F27Vrs7e355JNPcHK6uQR+a0gsCyGEEC3B2EsG1z8zmrTCEhJyi0nILSYxr5jE3CIScotrffm5WnZJOdkl5RxNyq6zTQ97a0ObhauTut6ONpir6v8cuC025U9ftmJxly9bLaa0UsOPxy8B+kleJvXpYNR6WdHxZEUnAOAY5IlHmHETlwkhRFtj7NXJToGe+AzsTJfHh1KYnE3C9ijid0SRfzFNP0inIyPqIhlRF4n49Gc8+nQgYFhPfAd3Q21vfd0Y/Pz8UCqVTJ48GdC3QWjfvj2nTp2q832/qKiI++67j3vuuYdXXnml3u2tW7eOoKAgevbsSWhoKBEREURGRjJv3rxa89s0hxEh3gwN9uJoYiYXUzMJ8nKTk781JGkrhAlT21vTbcoIOj96N5e2HOXMd7soTMwCoCA+g4ML1hD15SY6TRxExwcGYGFnZVj3Rt7EjfHZZ58xceJEHB0dee+99zh58iSrVq3is88+45133rmp19caEsui7QgICECtVmNlpf87euONN3j00UdrjdFqtcyePZs//vgDAHd3d7766is6dOjQ4rOvCiFuL8ZeMmhprqK9iz3tXezrbKOsSkNSXjGJucUk5NUkdXOLSMwrJqekos54HZBWWEpaYSmHaiawMsSjUODtaGNos3A5oZuUV8z7m4/XSSxnFpUx+6eDfDRhgCRub7H1p+INk9jd29kPt6s+H15PzJo9htudHx1cpzexEEIIvZu5Otnepx3dpo6g29QR5Melk7AjivhtURQm6b/f66q1pEWcIy3iHIc/Wodn3xAChvfEd1AXzK3rXn3Trl07hg8fzubNm7nvvvuIi4sjLi6O0NDQWuOKi4sZPXo099xzzzUnE8/Pz2fRokWGqtvS0lKUSiVKpZLi4sZNjm4slVJBmJ8rfpY63NxcUUrCFpCkrRCtgkptTvDY/nS4vy/JB85wZvUuMk/GAVCWU8jxpb8RvXIbHcb2J/SRQdi4Oxn9Jm6MuLg4tm7dyqZNmygsLESj0aBQKBr9Jt4aEsuibVmzZg09e/a85vL169ezf/9+jh8/Tl5eHl999RVvvvkmP/zwg2H21cmTJ9O5c2deeOEFbG1tefnll1m6dOmtexFCiNtGYy8ZtDI3o6ObIx3dHOssK66o0idzc4tIuJzYrbldVJPwu1q1Tqev4s0rZt+lhmPXoU8u/3NrFEODvaRa5hap1upqTUD2RF/jqmWL03JJ3K2f6NXS2Y6AEb2bJT4hhLhdNObqZMf2Hjg+NZruf72HvAupxG+PIn57FCVp+pZGWk01KQfOkHLgDCoLM7wHhBIwvBfeA0Mxs7wy0ejSpUt56qmnCA8PR6lUsmzZMry9vZk+fTrjxo1j3LhxLFq0iIiICEpKSvjxxx8xMzNj4sSJvPXWW4bthIeHM3/+fEPxyty5cwkLC8PCwoLly5c35W4TN0iStkK0IgqlEt+7uuJ7V1eyouM5vXoXSXujQaejqrSCmDW7Obt2LwEjetHlsSFGvYmXlpbSsWNHKioqKCgowM/PjyeeeIIFCxYYnvell17is88+Q6FQ4ODgwOOPP063bt2wtbVlzZo1N/16WkNiWTSN26WKVaFQUFFRQXl5OTqdjsLCQnx8fICWn31VCHF7aq5LBm3V5nT2dKKzZ+0rUXQ6HflllSTWtFr4c1K3rKra6OfQARlFZRxLyuIOf7dGxSuMs/tCKkn5+sqvfgFuhLg7GrXe2bX70NX0wA2ZcGe9rbeEEEI0LYVCgXOwN87B3vR65j5yYpKI336chB0nKM0qAKC6UkPi7lMk7j6FmZUFPnd2IWB4T7z6dSIwMJCdO3fW2e7XX39tuP3WW2/x1ltvodVqyczMxK2exPKyZctq3Z8xYwYzZsxohlcsbpQcjYVopVy7BjDk/WkUJmZxZs1uLm46grZSg65aS9zmo8RtPopX3xBWf7gMjz7BtS5xu/pN3NramuTk5Ou+if85ITZ//nzmz5/fJK/D1BPLoum0hirWKVOmoNPp6Nu3LwsXLsTV1bXW8rFjx7Jz5068vLywsbHB19eX3bv1k7a09OyrQojb1628ZFChUOBkrcbJWk0PH5day3Q6Hdkl5TVtForZeT6FPRfSG9zmN4disVWb08ndsckvuTfmpOA333zDokWLAH1rpvT0dAYPHsxPP/1kMicFm8rKw+cMt6cYWWVbWVLOhQ2HAFBZmNHxgQHNEpsQQohrUygUtOvsR7vOfvSZNZbMU/EkbI8iYecJyvP0RUiaskritx0nfttxzG0t8R3UlYDhvfAMC0ZpdnMTiQrTJklbIVo5ez9X+v/tYXpMv4fYdfuJ/Wk/lYWlAKRGxJIaEYtzR286PzYE/6E9TO7N3Jizgy2dWBa3RktXse7Zswc/Pz+qqqqYO3cuU6dO5ffff681JjIykujoaJKSkigvL+fTTz9l5syZ/O9//zOJ2VeFEKI5KRQKXG2tcLW1IszPFV8nG6OStvsvZbD/UgYdXO0Z183/hvqsGqOhk4JPPvkkTz75pOFzxIgRIwytmW6n1janUnM5npwDQKCLHQMDPYxa7+JvEVSV6nsct78n7IZmLxdCCNH0FEol7j0Cce8RSNiLD5ARdZH47VEk7j5l+K5fVVzOpU2RXNoUidrBGr+7u+M/vCfuPYNQ1kwcWpKeV6vvbmFeHmZ5ldfsuytMjyRthbhNWDnZ0XP6aLpMHsrF349w5vvdhp44uedS2PfOtxxf9juhjwymw5h+mFur5U38NmFMlRHAqVOneOGFF8jIyECj0bBgwQIefvhhIiMjmT59OpWVlYSHhzN16lQAduzYwZo1a+pcLnOzTL2K1c/PD9AniF9++WU6duxYZ8zKlSsZNmwYjo6OZGZmMmXKFEaPHl1nXEvOvtqSWsvvohCiafT2dcXdzorMorI6E5HV50JWIZ/sOMVnO0/RP8Cdcd38GdLRCyvzW/eV5NixY2RmZjJu3Djg9mptsyriSpXtE307ojSiqlmrqSbmx72G+6GPDGqW2IQQQtwcpZkKz7COeIZ1pO+rE0iPPE/89iiS9pwynHCrKCjl/PpDnF9/CEtnO/yHdse9ZxD73luNtlJz7W1bmDF+9Rz5zm/CJGkrxG3G3EpNp4fuouMDA0jcfZLTq3eRG5sM6M+0RS7+lZPfbCFgZB8ubDiEtsq038QlsWychqqMSktLeeCBB1i5ciUDBw4kLS0NMzP9IWDhwoUsXryYPn360K1bN6ZOnUpZWRnz589vsstCTb2KtaSkhKqqKhwdHQH47rvv6NWrV51xgYGB/P7777z66qsA/Pbbb3Tt2rXWGFOYfbUlmfrvohCi6aiUCl4f2ZPZPx1EAbUSt5fThe+NCaNco2XjqQSiUvRVoFodHIjL4EBcBjYWZozs5MPYbv709m1nVKLxzxo6KXi11atX85e//AVzc3Pg9mltk1pQwrazKQA4Wau5v6ufUesl7Y02nOT36t8Jx/bGVecKIYS49VTm+knJvAeEUl3xMCmHz5KwPYqk/WeoLq8EoDy3SH8F7rr9DW5PW6mhvKCkWb9PG1PUsWvXLu69915CQkLQaDSYmZlx8OBBrKys2nxRhyRthbhNKc1UBAzvhf+wnmQcv8jp1TtJPXQWgMqiMs79tK/BbdyKN/HrKUnP45fHF8rZwSawevVq+vfvz1133YVWq0WlUhm+1F6uMiovL0el0rfPmD9/Pi+99JIhidlYpl7FmpGRwUMPPUR1dTU6nY7AwEBWrlwJUKu/8qxZs4iJiaFXr14oFAq8vb3rfFCQ2Vevr6V/F4UQTWtEiDcfTRjAP7dGkVFUZnjczc6K10f2ZESINwATewWSkFvExuhENkYnkFqgv7yzpFLDLyfj+eVkPF4O1ozp6s+Yrn74O9sZ9fzGnBS8rKSkhF9//ZWDBw8aHrtdWtt8F3mBap0+bT6pdxBqI9thxazZbbjd+dG7myU2IYQQTU+lNsdvcDf8BnejqqyClAMxxG+PIuVQzHW/P7eEhoo6AEJCQgxXw1zdDrGtF3VI0laI25xCocCjdwc8encg71IaZ77bRfzW42g1xs3+XJyWg7mNJQqlAqVKiUKlQqFSoFSpUKiUNY9fud2UE4yUF5Q0eMBp6cSyqWioyujMmTOo1WrGjBlDcnIywcHB/Pvf/8bd3Z158+bxzDPPUFJSwocffkhUVBSXLl3igw8+aJLYWkMVa2BgIMePH6932dX9ldVqNV999ZXMvnodpvy7KIRoHiNCvBka7MXRxEwupmYS5OVGHz83VH+aMM3f2Y5Zg7vw7KDOHE/KZv2pBLaeTaak5lifWlDKl/tj+HJ/DD28nRnbLYB7Ovlgb2Vxzec25qTgZT/++CMhISF07ty53uWttbVNcUUVP52IA8BCpWRi70Cj1suKTiArOgEAxyBPPMKMm7hMCCGEaTG3UhMwvCcBw3tSWVJO8t5ozq0/SNbJ+AbX1VYZlxdoKW29qEOStkK0IU6Bntz51mP0nHEvx5f9Ttzmow2us2fuyht6DoVKiUKl1Cd4lVfdrrlvuH2Nx5U1SWGFSommrPJmX2qbYkyVkUajYdu2bRw6dAgPDw9efvllnnvuOdatW0doaCh79uwBoLq6mlGjRrFq1Sq+++471q5di729PZ988glOTjeXGJcq1rbD1H8XhRDNR6VUEObnip+lDjc3V5TKa5/EVSoU9PFzpY+fK3NG9WTXuVQ2RCdwMC4DbU2PhRMpuZxIyeWDrVEMCfZkTFd/7gz0wFx15USZsScFL/vmm2947LHH6l3Wmlvb/HwijuIKfeJ7TFd/XGwsjVov5ocrVbahjwxu0hPvQgghWoaFjSWBo8NwaO/B70992uD4rS8twWdgZ3wGdcG7fyhqe+smj8mYNkYXL14kLCwMrVbLjBkzmDVrFkCbL+qQpK0QbZCNmyOhjww2Kml7o3TVWnTVWrRNvmVxLcZUGfn5+TF06FC8vb3RarU8/PDDhpmzr/bZZ58xceJEHB0dee+99zh58iSrVq3is88+45133rmp+G6XKlbpr9wwU/9dFEKYHitzM+7t4se9XfzILCpj05lE1p9K4EJWIQBV1Vq2nk1h69kUnKzV3NfZl7Hd/Onk7mj0SUGA2NhYoqKi+Oabb+qNo7WeFNRotayOvGC4/5e+xlXLFqfnkrjrJACWzna0H9m7WeITQghh2qorqkjYeYKEnSdQqJS49wzCd1AXfO/q2iTfbYwp6ujduzfJycnY2dkRFRXFtGnTcHV15ZFHHmnzRR2StBVCXJfPnZ0xt7VCp6lGp9Whrdaiq67W/9TWJGhrErWG29d6vGZZvberbz7Ne+b7XXSbMqJNTp5hbJXRI488wvLlyyksLMTW1pbt27fTvXv3WmPi4uLYunUrmzZtorCwEI1Gg0KhaBVVRs3NVPorG9PI/+DBgzz77LMAlJWVcffdd/Ovf/0LtVrdrI38W8vv4o3uw6qqKnr37s2XX34pkyEI0czc7KyY2i+EKX07cjYjn43RCfx2Oom8mtmx80or+DbyAt9GXiConT3juvmzec8B3Oys6mzr6pOCoO+VV1BQQGZmZr3P3Vpb22yPTTH0B74r0IOgdvZGrRe7dh+6mrLmkAcHorKQr4VCCNEWmdtaUlVcDugLsNKPnif96HmOfPYLzh298bmrC76DuuLUweumrsgwpqjD3l5/7NJqtXh5eTFp0iT27t3LI488UmtcWyzqkKOzEOK6uv/1HlxCfJr9eXQ6HTqtribBq08Q58Yms/XFJQ2uG7/1OPFbj+PaLYDgcQPwH9odM8tr97+7EcYkeHbs2MGcOXMoLi5GoVAwZMgQFi1ahFKpJC4ujkmTJlFcXMzkyZN58803AYiJiSE8PLzRzdONrTLy8/PjzTffZODAgSiVStq1a1en2uill17is88+Q6FQ4ODgwOOPP063bt2wtbVlzZo1jYqzIaZexWpK/ZUbauTfo0cPjhw5gkqlIj09neeee44vvviCV155pVkb+bem30Vj96G5uTkajYaxY8eyZMkSXn311TY/GYIQt4JCoSDUw4lQDydeHtqdA5fS2RidyM7zqVTVnOS9mF3IpztPsWjXKfoFuDO2qz/DQrywMm87X290Oh0rD58z3J/Sz7gq28qScs5vOAzoTzh2HD+wWeITQghh+kZ88jTVlRqS9kaTtPc0xak5hmW551LIPZfCyf9swcbDCd+7uuIzqAvuPQJRGjHhpbFFHWlpabi7uwNQXFzMb7/9xlNPPVVrTFstMGo7n2qEECZNoVCgUClApURV89ZkbmRPtsuyTsWTdSqeI4t+JvCePgSP7Y9TB69Gx9ZQgsfJyYnvv/+ewMBASktLGTJkCCtXruSvf/0rn3/+ObNmzWLy5Ml07tyZF154AVtbW15++WWWLl3a6NiMbT0A8MQTT/DEE0/Uaj9wtT8nnObPn8/8+fMbHWNDTKWK9XZhba3vQ6XVaqmsrKSsrMxwVrw5G/nfDr+Ll13ehwCVlZWUl5ffkn0ohKjLXKXk7mAv7g72orCski1nk9lwKoGoFP2XSq0ODsZlcDAuA+vNZozs5MPYrn708XNFeVVFULVWx9HELC6mZhFUrqh3orTWJiolh+i0PAA6ujnQ19+tgTX0Lv4WQVWJvqoq8J4wLJ1smy1GIYQQLcPSwQalhVmD37GsnO2x8XDCvWcQfZ4fR/6ldJL2RpO87zQ5Z5MMY0vS8zi7di9n1+7Fws4K74Gd8b2rC179QjC3rv97u7FFHevWrWPJkiWYmZlRXl7OpEmTePLJJ2ttqyULjFqSJG2FaKOMfRO3dLC5hVHdnNDHhpB66CwFcekAVBWXE7tuP7Hr9tOusx8dxvUnYFhPzK3VzfL8V58ttLS0pGvXriQk6GdjvpzgqaqqQqvVolQqWbp0KaNGjaJ9+/bNEk9rc7NVrDqdvjK7urKK6goN1VUaqis1VFdWoa3UUF1RddVj+serKzX6ZVfd14+vrnW/ukqDtuLKuIrC0ubeDUYzppF/fHw8DzzwABcuXOD+++/nueeeA6SR/2U3sg8vXrzI8OHDDe0SZB8K0XLsrSx4uFcgD/cKJDG3mI3RCWyITjC0Byit1PDryXh+PRmPl4M193fxY0xXfy5kF/LPrVFkFJXVbCkWdzsrXh/ZkxEh3i33ghqpVpVt345GXbaqrdYS8+New/3QRwY1S2xCCCFalo2HE+NXz6l1NWNuXh7OTk7XvJpRoVDgFOSJU5An3aeNpCQzn+R9p0naF0360QuGloaVRWXEbT5K3OajKM1VeIZ11LdRuKsLVi5X2vQYW9Tx/PPP8/zzz9cq6PjzMa2lizpaikKn0+laOojWqLCwEAcHBwoKCgz9N25X15sUyFSYeoymGt+fL0lv6E28JeIztgLT2t2RrOh4Lmw4TPz2KKorqmqNM7dWEzCyN8Hj+t9Qu4eAgADs7e0bTPBclpqaSs+ePdm4cSN9+/YlLS2NadOmkZmZycyZMxk7dixPPPEEW7ZsMVTp3Qqm8n+t0+moKq2goqCEysJSKgpKyDmbTNRXmxpc16qdPei4knyt0hj68ZkK37u70X5ELzx6d0DdDCc8EhMTazXyP3XqVJ1G/pdptVri4uJ49dVXeeyxx5g0aVKt5Vc38t+9e/cta+Tf0r+LN7IPQX+8f+SRR5gyZQqPP/54rWUttQ+vZqrHl6tJjE1DYrzGc+p0HE/KZkN0Altikilp4CTgZZe/Cn40YYBJJW6N3YdJecWMXfoHOsDV1pJNz92HuarhfZ6w6yR75q4AwKtfJ4Z/fON9e+V3sfFMPT6QGJuKxNh4ph4ftI0YK4vLSDl0lqS90aQeOmu4YuPP2nXxx/euLvgO7oqDv/stjbE1MTanKEnbmyRJW9Ni6jGaenxgujHeTIKnsqiMuK3HOP/rQfIuptXZpnOID8Fj+xMwshcWDbRguJEET2FhIcOHD+f+++9n3rx59e7HiRMn8tZbb5Gdnc2SJUtQq9UsWLAAf39/o/bHzWiu9gNaTTUVRaVUFuiTrxWF9f2suV1zv7KwFK2muile1i2lMFOhu5G4FQqcg73wCAvGs08wrt3bY27VtJXeaWlpdOzYkaKionqXX/6b3rFjB9999x0bNmyotfzjjz/GxsaGKVOmEBYWZmjkHx8f32yN/E2tFUZD+xD0+/HLL79k48aNbNy4sdayltiH9cVniu/dV5MYm4bE2LCyKg27zqex4VQ8B+MyaOi8ngL95GebnrvPZFolGLsPF2w5zvdHLwLw4t1deWpgJ6O2/8ez/yLrVDwAwz99Gq87QpotxpZk6jGaenwgMTYVibHxTD0+aHsxVldpyDh+kaQ90STvP01pVkG94+x9XfEZpJ/IrF1nf5T1nFxs6YKOlmJsTlHaIwghTJqNh5PhTVqr1aLJtMC5gQONhZ0VIRPupOODA8mJSeL8hkPEbzuOpqwSgNzYZA7HruXo5+sJGNGL4LH9cQn1rfeyQmNmuwQoKipi9OjRjBs37pqzTa9bt46goCB69uxJaGgoERERREZGMm/ePFasWHFD++VGGNt+IC8ujYqiq5KsBaV1E7EFpVQW1vwsLrvuNpuahZ0V5taWKM1VqNTmqCzMUFnofyrNzfT3DY+boTI3Q2lx1f2rxqsszFFe83FV7fvmKnLPpfD7U58aH6xOZ2jcf2b1LpRmKly7BuiTuGHBuHTyNap5/9WMbeR/4cIF/P39UalUVFZW8ssvv9C9e/daY1qqkX9LT+h2o/vQ3NycyspKNm3aZDL7UAhxbVbmZtzb2Zd7O/uSVVzGsn0x/Hj80jXH64CMojKOJWVxh5H9YE1BYVklv5yMB8DSXMXDvQKNWi/rdIIhYesY6IFnWP2faYQQQojrUZmb4dU3BK++IfR9bQK5sckk7okmeV80+ZfSDeMKk7I4s3oXZ1bvwtLJFp87O+M7qBseYcGYqc1NrqDDFEnSVghx21IoFLTr7Ee7zn70eX4c8duOc379IXJjkwHQlFVyYcNhLmw4jFMHL4LH9qP9qD5Y2FkBxid4iouLGT16NKNHj+att94iMzOzzpj8/HwWLVrE5s2bASgtLUWpVN6SBI9OqzVq3M6/LW+2GFSWFqjtrVE7WKO2t6n1U1NRRcz3uxvcxojPZt5Qa4uWcMcrD1KSnkf60fPknk+FmotZtJpqMqIukhF1kRNf/4G5tRq3nkF4hgXjERaMY3uPBnsRGtvIf8eOHSxevBiVSkV5eTmjRo3i7bffrrWtttrI/2b2oUajYcCAAcydO7fWttrqPhSitXC1taKPb7vrJm0vS8kv5Y7mu+Clyf0YdYnyKv3VHw90C8DBysKo9WJ+2GO4HfrIYKN64AohhBDXo1AocOnki0snX3o9fS9FKdkk7dX3wc06GWdoZ1eeV8yFjRFc2BiBytICr74hOHXwatGCjtZA2iPcJGmPYFpMPUZTjw/aVow5sclc2HCIuC3HqCqtqLVMpTYnYHhPOoztR5G1locffrhWgmfRokUEBATUSvD84x//YP78+XTp0gUAjUbDpEmTaiV5nnnmGR599FGGDRsGwFdffcUnn3yChYUFy5cvJyws7KZfz9XK8orIv5hG3sU08i+mkX8pnbyLaWirjOvx1yCFAgtbS9QONjX/6iZh1fY2WNT8tHS0QW1vjUptfs1N5sQmG1XFet/yV1osaXszZ4HL84vJOHaRtKPnSY88R1HNTOf1sXS2w6NPBzz76JO4th7OjY7ZFP6mNRVVFKVkU5SURWFSNlnR8STvO93geqGPDMKtZxB23u2w9XJu8tYSN6Il9+M333zDX//6V37++WfGjx9fZ/mHH37IihUrqKyspHPnzvz3v//F0dGRvLw8JkyYQHZ2NoMGDeKLL74AICsri4kTJ7J161bMza/9N9nUTOF3sSESY9MwtRiPJGQyffWeBsc5Wlkwe3gP7u/qh7KFE5kN7cOqai33fvE7WcXlKID1z4zGz9m2we0Wp+fyy6ML0FVrsXSyZcLaudc9NjcmRlNg6jGaenwgMTYVibHxTD0+kBivpTy/mJQDZ0jae5rUiNg6884YqyW/BzYXaY8ghBDX4BLig0vIw/R+bizxO6K4sP4Q2WcSAaiuqOLi70e4+PsRHNp7sPqNRQSODkNtb11rG1fPdvnWW2/x1ltvAbUPhldbtmxZrfszZsy4ZhsFY2jKKymIz9AnZy+lkXdB/7M87+ardj16d8DWu11N4tUaS0cbLC4nYh30yVcLO+t6exHd7m5m9lVLR1v8h/XAf1gPQP+FOT3yPGmR50k/er7W/1V5bhHxW48Tv1U/u6qdTzs8+uhbKTTXpGZNRauppjg9ryYxe+VfUVI2JZn5hmrjGxHzw15ifrgyu7mVix22Xi7Y+bTDzrsddl4u2Hq7YOftgtrB5rasFouPj+err76if//+9S7funUr33zzDQcPHqSsrIyvvvqKt956i88//5xvv/2WoUOHMm/ePIYNG0Z0dDRdu3bl1VdfZeHChbc0YStES+rt64q7nRWZRWVc750ov6ySuRuPsDryArOHd6eP37UnHG1pf5xJIqtYP/nLkI5eRiVsAWLX7jPM+h0y4c6bTtgKIYQQxrJ0tCXovr4E3dcXTXklaZHnSNp7muT9p6nIL2np8FoFSdoKIdosc2s1wWP6ETymH3kXUjm/4RCXNh+lqubLUEFcOpGLf+XY0t/wH9Kd4HH9cesReEsTRDqtlqLU3Jrq2VTyL6WTfzGNopRsw6Um16VQYO3qQGlmfoNDe88a2yJnMC0dbFBamDVYxWrZwonLm+mvfDVbD2c6jOlHhzH90Ol05F9KJ/2oPombcfwimrIrVd9FydkUJWdz/teDdSY1c+sRiJll/ZfC/rmRf2FeHmZ5lY1u5K/TainNLjQkYw2J2eQsilJyDImA5lKWU0RZTpGhF+PVzG0s9Ylc7yuJ3MuJXWs3B8NrvxHNtR+NpdVqmT59Ov/617947bXX6h1z4sQJ7rrrLuzs7CgrK+Pee+9l2LBhfP7555ibm1NaWopWq6WiogILCwv++OMPnJycrpkEFuJ2pFIqeH1kT2b/dBAF1ErcXr7f2cORM+n5AJxJz+Ov3+5meIg3rwzthq+TcQnRW0Wn07HqyHnD/Sl9g41ar6q0nPMbDgP642nw+AHNEp8QQghxLWaWFvje1RXfu7qirdaSHR3PufWHiNt8tKVDM2mStBVCCMCpgxd9X5lA72fHkLDzBOfXHzIkiLSVGuK2HCNuyzHs/VwJHtufwHvDsHTUf5lrqgRPeV4xeZcutzWoaXEQl0F1eaVRr0HtaINTkCeOgZ44BnniFOSJQ4A7hYlZNzaJ1i12M1WsrZ1CocCp5v8o9JHBaDXVZMck6itxj54nOzoBrUbfr7DOpGbmKly71J3UrLGN/HU6HRUFJTXJ2GwKE7Ou3E7ONvr38DILWyvsfF2x93PF3qcddr6u6LRa9r+7usF1e0wfjU6rpTglh6LUHIqSs69ZRV5VUk7uuWRyzyXX+3ptPZ31iVyvdlcldV2w9XRBZVH3Y5ApTIjwySefcOedd9KnT59rjunTpw9ffPEF6enpKBQKVq9eTVFREbm5ufzlL39h6tSp9OrVi/Hjx+Pt7c1TTz3F77//3izxCmHKRoR489GEAfxzaxQZRVcm0HSzs+L1kT0ZEeLNobgMPt5xknOZ+tmvt8emsOdCGo+HdWD6wE7YX+NE2a0WkZBFbEY+AF08nejl086o9S5sjKCqRH9COvCePlg52TVXiEIIIUSDlColbj0CUVlaSNK2AZK0FUKIq5hZWhB07x0E3XsH+ZfSOb/xMJc2HaGy5oteYWIWRz/fwPEvf8d3cDd87+rCgQVrbijBo6mooiA+g/yLqeRdTCf/kj5JW5ZTZFSMKgszHAI8cAzywCnIS/8z0BNLZ7tWe5l4Y6tYWzulmQq3bu1x69ae7k+OoqqsgswTcTWVuOfIO59qGKutqn9SM3s/V6Ma+Rdn5FJeUEJRsr7PrL56Vp+grbwqoWEMldrckJC193XF3vfK7fraFuTE1k2s1sd7QGidqu+q0nKKUnIpTs2mKCVH/y85m6LUHEoz8uqtPNdWaihMyKQwoe7kgCgU2Lg5XknkeukrdLXVmhadECE6Opp169axZ8/1+3AOHTqU2bNnM27cOLRaLRMnTgTAzMwMGxsb1q5daxj7yiuvEB4ezoULF3j//fcBmDt3Lj169GhUrNfrubt582bCw8MN99PT0/Hy8uLYsWMm13NX3P5GhHgzNNiLo4mZXEzNJMjLjT5+bqiU+veo/u3d+f7JEfx6Mp5/74kmp6SCqmotKw6f49eT8Tw7qDMP9wrErIWPSasizhluP9G3o1HHfG21lrM/Xmk3EzpxcLPEJoQQQoimJ0lbIYS4BsdAD+548QF6P3MfCbtPcmH9YTKiLgL6xFnC9igStkc1uB1tpYZT/9tORX4J+ZfSKEo2vrWBnZfzlcrZQE8cO3hi593uhvrKtpb2A+IKcys13v074d2/E3DVpGaR50g/er7WpGZVpRWkHDhDygHjtr1l1hc3FItCpdQnNOtJzFq3s7+h9gON+V00t7bEOdgL52CvOsuqqzSUpOdRlJJDcUo2hTU/i1JyKE7Nobq+59PpKMnIoyQjj4xjF4x+Dc1t7969xMfHExysv+w5PT2dp59+mrS0NJ599tlaY5977jlmzpxJZmYmly5dwsfHp85EBhEREWRmZjJmzBgGDRrEqlWr0Ol0TJs2jd27d990nA313L3nnnu45557AP2JmKvvS89d0RJUSgVhfq74Wepwc3NFqVTUWT6hZ3vuCfXhP4diWXn4HJXVWvLLKlmwJYo1Ry/y6rDu3BXk0SInSC9lF7L3YjoAnvbWjOzkbdR6yfuiKU7LBcCrbwiOgR7NFqMQQgghmpYkbYUQogEqtTmBo/oQOKoPBYmZXNhwmIubjtxQ8/Tzvxy87nK1gzWOQV76xGyQhz5R294Dc2t1Y8Nvk+0Hbjd1JjVLyyXt6HnS65nU7GbZuDth59uuJjGr/2fn64qthxNKM1Wjtw/N97uoMjczxPxnOq2WspwiilL0fYKLalouFKfkUJSSfcPVxZcl7IhCp9XiFORVb5uFm/Xss8/WSs4OGTKEl19+uU4lK0BaWhru7u6Ulpby97//nddff73W8qqqKsLDw/n+++8BKCkpQaFQoFAoKC6++d8ZY3ruXi01NZV9+/axatUqAOm5K0yajdqcF+7uykM927N4VzSbziQBcCmniOd/3E//ADdmD+9BsJvDLY3rf1f1sn0srIPRVb9nvr9yciZ00t1NHpcQQghxs6S4qGGStBVCiBvg4OdGn1lj6TnjXpL2nuLM97vJiUkyen2lhRmOAe44Bur7mTrW9KC1cmne1gZtvf3A7cbW09kwid7lSc0u/h5BzJrrX1IP+v7NziE+2Pu0w95Pn5i1826H2S2aSfxW/y4qlEqsXR2wdnXAvWdQneUVhaX6hG6KPpGbfSaB5P1nGtzu6W93cvrbnSjNVTh18MKlky/tQn1xCfXD3s/thqrhjTVv3jy8vLyYOXMmAKNGjUKr1VJWVsbUqVN5/vnna43/8MMPmTJlCu7u7gC8++673HfffYZlN8uYnrtXW7FiBcOGDcPNzQ1Aeu6KVsHLwYaFD/Tj8bAOfLT9BCdS9NWqh+IzeeQ/W3mwR3tmDe6Ci42lUdu7XjsRgMTERGbNmsW5c+fQ6XQ8//zzvPjii8TFxfHwI49yJikNxx6DaD/qESb0aE9MTAzh4eGsX7/+ms+ZfSbR0J/fob0Hnnd0vOH9IIQQQjQXKS5qWJtM2s6fP5933nmn1mMhISGcPXu2hSISQrQ2KgszAob3ws7H1ahJvno9cx++g7pi59OuyaoWhYArk5q1H9XHqKTtgDcerdMvti1T21ujtvejXagfoO+7a0zS9jJtVTU5MUnkxCRx7mf9Y2ZWalxCfHAJ9dUnczv7YePhdFMnZnbt2mW4/e6779ZadurUKbRaLZmZmbi5udXZ/ptvvlnr/pgxYxgzZswNx3A1Y3vuXqbT6fjmm29qfe66VT13hWgK3b1dWPHEUDbHJLNo1ylSC0rR6mBdVBx/nEniqYGd+Msdwaivc2xvqJ2ITqfjwQcfZM6cOTz00ENkZmai0+nbKH3++ed0u+dBqqwCif3sFZ6b9Ty2ajNefvllli5det3YY9ZcqbLt/OjgVtv3XgghxO1Liouur00mbQG6dOnCtm3bDPfNzNrsrhBC3AKefUNwCHBv6TCEEE0k9LEhlOcWkROTSGFiVq1lmrIKw2Rxl6kdbWqqcf30ydxQ31Y5g/uN9NwF2L17N+Xl5QwZMqTe7TVXz10hmpJCoWB0Z1+GdvTif0fOs/zAWUoqNZRUali8K5q1xy/x0pBu3BPqUycxakw7ke3bt6NWq5k4cSJarRbAUCGvUKk4cC4JdVd/0Ol4LKwDS5cuZdSoUbRv3/6aMZek55Gw6yQAlk62tB/Zuyl2hRBCCCFuoTabqTQzM8PDQxrxCyGEEOLGtR/Ry1CxXFlURk6svto252wS2TGJlGYW1BpfkV9C6qGzpB66clWPjbsTLqFX2io4h/hgYcSl1iXpebUuIyvMy8Msr/KWXEZ2Iz13AZYvX87UqVNRqepWITZXz10hmovaTMVTAzoxvnsAX+w9w09Rl9DqILWglPBfD7M68jyzh/egu7eLYR1j2omcOXMGV1dXJk2aRGxsLB4eHvzrX/+iQ4cOdBo5gaXPPYNm3ybunjgFazSsXbuWLVu2XDfWs+v2oavWJ4A7PjgQ1S1qgSOEEEKIptNmk7bnz5/Hy8sLS0tLBgwYwIIFC/Dz82vpsIQQQoibIo38m8bN7EcLOys8wzriGXalX2RZTiHZNUncnDOJZJ9NorKwtNZ2SjLyKMnII7GmGg6FAgd/N1w6+RpaKzh38KqVbClJz+OXxxc2GN/41XNuef+vP/fcLSgo4KeffuLEiRP1jm+unrtCNDcXG0veHt2bSb2D+Gj7CQ7FZwJwIiWXJ1bu5N7Ovrw4pCu5SXFGtRPRaDTs2LGDQ4cOERoayocffsikSZM4cuQIG+MKCHxyLgAfTx3GSy89w8cff8zOnTtZsmQJarWaBQsW4O/vb9heVWk55zccAvTvBx0fHNhMe0IIIYQQzalNJm379evHf//7X0JCQkhLS+Odd95h0KBBREdHY2dX/6WKFRUVVFRUGO4XFhYC+kueLl/GdLvSarXodDqTfp2mHqOpxwcS482ysLcyKsFjYW9lMnGb4n68mqnHB6YZo5WbA+O+fZ2KfH0FplanIy8vDycnJ5Q1l+uqHW2wcnMwmbhv5/2odrLFe2Ao3gNDAX3PyuK0XHJiksg9m0R2TBJ551PQlFVeWUmnoyA+g4L4DC79EQmA0kyFY5AnLp18cAn1w8zS4rrvNwDaSg1leUVY3YLZ7Xfs2KF/Tq2W+fPnG24D2NnZUVRUhFarJSsrq87+mjNnTq3x9913nyFpe/Xjt4Ip/i7+mcTYeE0dX1A7O7545E72Xcrg0x2niMstAmDTmSR2nEshIPUYcXF124mkpKTUqlj38fGhV69ehIaGotVqefjhh3njjTfYdTaRS9n67xw9vV04u387gYGBdO/enS5dunDo0CEiIyN5++23+e9//2vY3vmNEVQVlwPQfmRv1A42Tfp/Yur/z2D6MZp6fCAxNhWJsfFMPT6QGJtKa4ixqRj7Gttk0vbee+813O7evTv9+vXD39+fH374gaeeeqredRYsWFBn8jKArKwsysvLmy1WU6DVaikoKECn05lsM2hTj9HU4wOJ8aYpYOCip6gqKgP0MRYXF2Nra2uI0dzOihJFFSWZmS0ZqYFJ7sermHp8YMIxKgAnC6DmpKJCjcbB3BCjBtP5PYQ2uB/NwLqbF9bdvPChH7pqLSUpORRcSKfwQhoFF9IpTsxCp7nyIU6rqSY3Npnc2GTO/3rI6KfKzctDk2lx4zE2kbKswjrviwW26bXeF61c7Zs9ju+//55XXnmF//znP7U+/wEkJiYyffp0/aQXGg3+/v58+umnODs7k5iYyMyZMykpKWHChAm89NJLAJw7d45//OMfrFixotlj/zOT/Xu5iqnH2Fzxhdgp+ffYbmyKzWDl8QQKKzRUaLTEuvWk05ylTO3tz6gO7kx8+CFmzJjBvffeS+ZV7yFhYWEkJCRw4sQJ3N3dWb9+PcHBwayMOG8YM9xLzcdzP+a7774jMzOToqIicnJyKCgoICcnx7A9XbWWM9/vMqznNqJLredqCqb+/wymH6OpxwcSY1ORGBvP1OMDibGptIYYm0pRUZFR49pk0vbPHB0d6dixIxcuXLjmmDfeeINXX33VcL+wsBBfX19cXV2xt2/+Lx0tSavVolAocHV1Ndk/HFOP0dTjA4mxUdzcDDcvV5SZXIxXMdn9WMPU4wOJsalIjICnB4R1Mdytrqgi72KaoT9uztkk/URnNTPJG+vCNzux9XbB0sEGtYMNaseanw42WDraonawRu1gg/I6M97frJKMPLa/tLzBKxDGffs6Nu7N18IhPj6eH374gf79++Pg4IDbVe/VAA4ODhw8eBArK/2VEDNnzmTJkiUsWrSIDz/8kBdffJHJkyfTtWtX5syZg62tLVOmTGHJkiV1tnUryN9L4zV3fE95ePBIv1C+PhjL6sgLaLQ68sqq+Gz/BX47l0W5FsPv4t///nc8PT0N7USWLVvGk08+iVarxdramn9+8TXzIjIA8HG0Ye+a//J///d/hjYIb7/9Nvfffz8WFhZ89dVXht/JxN2nKKvpqe3ZtyOBfTo3+es09f9nMP0YTT0+kBibisTYeKYeH0iMTaU1xNhULC0bnscCJGkLQHFxMRcvXuSJJ5645hi1Wo1ara7zuFKpvO1/mUA/a66pv1ZTj9HU4wOJsalIjI1n6vGBxNhUJMbalFZq3LoG4NY1wPBYZUk5ubHJ5MQkkhoRS/rRa59kviwnRj8xWkMsbK0MCV1LxysJXn1i96okr6MNlg42mNtaoqhpE3EtlYVlRrVwqCwsw87T5brjbpZWq+Xpp5/mX//6F6+99lq9/39WVlaG29XV1ZSVlWFjY4NSqcTCwoLy8nKqq6vRarWYmZnx5Zdfcs899xAUFNQsMRtD/l4ar7njc7C25LXhPXikdxCf7TzFttgUAM5nFcADr7Kz2p1eecW89957tdYbPXo0o0ePRqvVkpmZyedHEg3LJt8RzOPPfllr/DPPPMMzzzxT5/ljf9xruN350bub7XWa+v8zmH6Mph4fSIxNRWJsPFOPDyTGptIaYmwKxr6+Npm0nT17NmPHjsXf35/U1FT+/ve/o1KpeOyxx1o6NCGEEEKIWixsLPHo3UH/L6wjvz/1aZNtu7K4jMriMoqSs40ar1Apr0ru2hoqeS2drlTyVta0RWhJn3zyCXfeeSd9+vS57rjKykr69u1LQkICnTp14t///jcAL774ItOmTWPZsmXMnj2bgoIC1q5dy5YtW25F+OI24Otky8cTBnAsKYuPtp3kdHoeAHsupHHgUjoTewUx865QHK3rFoXklFby+xn9SRc7S3PGdw8w6jmzzySSeTIOAIcAdzz7hjTNixFCCCFEi2iTSdvk5GQee+wxcnJycHV15a677uLQoUO4urq2dGhCCCGEEI028l/PYePmQHleCRUFJVQUFFOeX0JFfgnlBSVU5BdTUVBieKyy2LhEq65aS1lOEWU5xvXhagnR0dGsW7eOPXv2NDjWwsKCqKgoysvLmTFjBl9++SXh4eF4enqyefNmw7iJEyfy8ccfs3PnTpYsWYJarWbBggWGS9WFuJbevq78b9owfotOZPHuaDKLytBodXx39AIbTyfwzJ2hTOrTAXOVkmqtjqOJWfzngL61AsDDPQOxtjDuK1vMmt2G26GT7m6wKl4IIYQQpq1NJm2///77lg5BCCGEEKLZmFursfNuh513O6PGazXVVBSWUp5fTEV+yZWEbkHJnx67kuxtqAVCQyI+XodXv064dvWnXWd/LOysGl7JCHv37iU+Pp7g4GAA0tPTefrpp0lLS+PZZ5+tdx0LCwsmTZrEnDlzCA8Pr7Vs3bp1BAUF0bNnT0JDQ4mIiCAyMpJ58+a1yIRkovVRKhSM7ebPiE7erDx8jv8ciqW8qpqi8io+2n6SH45dYlhHLzadSSLjT5Xqvk42Rj1HSUYeCbtOAqB2tCVwZO8mfx1CCCGEuLXaZNJWCCGEEKI1snSwQWlh1uAkX5YOxiV6DOuYqbBytsPK2c6o8TqdDk1ZZU0VbwnleTXJ3IIS8i+mcfH3Iw1uI/tMItlnavp2KhQ4+Lvh2i0A1y7+uHYNwN7PFcVN9DN79tlnayVnhwwZwssvv8z48eNrjUtISMDV1RVra2u0Wi0bNmygW7dutcbk5+ezaNEiQ9VtaWmpoc9acXHxDccm2jYrczOeuaszD/Zoz792R7PhVAI6IDGvmP8ePlfvOu9tOoaDlZoRId7X3fbZdfvQVWsBCHlwICq1eVOHL4QQQohbTJK2QgghhBCthI2HE+NXz6G8oAQAnVZLbl4ezk5OhgSnpYMNNh5OzRqHQqHA3FqNubUaW0/nWstyYpONStrWotNREJ9BQXwGFzYcBsDCzop2NQlc1y7+tOvih7m1cTPtXsu8efPw8vJi5syZnDx5krfeegvQT1wWGhrKF198UWt8eHg48+fPN0xaNnfuXMLCwrCwsGD58uWNikW0XW52Vrw35g4eD+vAh9uiOJqUc93x/9waxdBgL1TK+tsdVJVWcH79IUB/0qbjgwObPGYhhBBC3HqStBVCCCGEaEVsPJwMSVmtVosm0wJnN7dWN8vu0H/+laqSCrJOJ5AdnUDu+RRDpSBAZVEZqYfOknroLAAKpQLH9h606xqgT+R29cfOp12DfTt37dpluP3uu+8abo8dO5axY8cC+v2YmZmJi4tLrXWXLVtW6/6MGTOYMWPGTb1eIf4s1MOJmXd1YcZ31+6/rAMyiso4lpTFHf5u9Y65+HsEVcXlAASO6m10xbwQQgghTJskbYUQQgghRJMxtoWDU6AXNh5OtK/pvakpryTnbBJZ0QlkRceTFZ1ARf6VFgQ6rY68i2nkXUzj/K8HAVA72uircGuqcV1CfTG3UjfvCxSiCeWUlBs1Lru4/nHaai0xP+w13O/0yOAmiUsIIYQQLU+StkIIIYQQosncbAsHM0sL3HsG4d4zSL+eTkdxag5Zp/QJ3KzT8eRfTEOn1RnWqcgvIXn/GZL3nwFAoVLiFOSln9ysq761gq2nc73VuCXpebViLMzLwyyv8pa2mRCina1xLT+uNS5532mKU/XtFTz7dsQp0LPJYhNCCCFEy5KkrRBCCCGEaFJN0cJBoVBg590OO+92BI4OA6CqtJzsmCSya5K4WdEJVBaWGtbRVWvJPZdM7rlkYn/aD4Cls51+crOaSc6cO/lSkVfML48vbLAaePzqOc2WuB01ahTp6ekolUrs7OxYvHgxvXr1qjXmm2++YdGiRYb7iYmJ3H333fz888/ExcUxadIkiouLmTx5Mm+++SYAMTExhIeHs379+maJWzSt3r6uuNtZkVlUhq6e5Qr0PXB7+7rWu37MD1daK3R+9O7mCVIIIYQQLUKStkIIIYQQolUwt7bEs08wnn2CAX01bmFSFtnR8WSeSiD7dDz5cRmgu5L+Ks8tImlvNEl7owFQmqmw82l33YQtgLZSQ3lBSbMlbX/44QccHR0B+Pnnn5k2bRonTpyoNebJJ5/kySef1Mej1dK5c2cef/xxAD7//HNmzZrF5MmT6dy5My+88AK2tra8/PLLLF26tFliFk1PpVTw+siezP7pIAqolbi9XB/++sie9U5Clh2TSOaJSwA4BLjj2Tek2eMVQgghxK0jSVshhBBCCNEqKRQKHPzccPBzI+i+vgBUFpeRfSaRrOh4fUXumQTDJE0AWk01BfEZLRWyweWELUBBQUGDE6odPnyY7Oxsxo0bB4C5uTmlpaVUVVWh1WpRKpUsXbqUUaNG0b59++YMXTSxESHefDRhAP/cGkVGUZnhcTc7K14f2ZMRId71rhez5kqVbeijgxv8HRJCCCFE6yJJWyGEEEIIcduwsLXCq28IXjVVhzqtloL4TH07hVPxZJ1OoDAh06htpR09j62nM2p762aJdcqUKezcuROA33///bpj//Of//Dwww9jbm4OwIsvvsi0adNYtmwZs2fPpqCggLVr17Jly5ZmiVU0rxEh3gwN9uJoYiYXUzMJ8nKjj59bvRW2ACUZeSTs1Fdmqx1taT+qz60MVwghhBC3gCRthRBCCCHEbUuhVOIY6IFjoAfBY/sDkH7sAltfXNLguse/2MjxJb/h0skHz7COeIQF49Y1AJXavEliW7lyJQArVqwgPDz8monbkpIS1qxZw4YNGwyPeXp6snnzZsP9iRMn8vHHH7Nz506WLFmCWq1mwYIF+Pv7N0msovmplArC/Fzxs9Th5uaK8hoJW4DYdfvRVWsBCHlwIGZN9DsphBBCCNMhSVshhBBCCNGmmNtYGj9YpyMnJomcmCSiV21HpTbHrUd7PMM64hkWjFMHLxQ3MMFafaZOncrMmTPJycnBxcWlzvIff/yRLl26EBJSf8/SdevWERQURM+ePQkNDSUiIoLIyEjmzZvHihUrGhWbMD1VpRWcW38QAKW5io4PDmzhiIQQQgjRHCRpK4QQQgghRD0CRvYi/1I6+RfTDI9VV1SRFnGOtIhzAKgdbfDoE4xnWDCeYR2x9XRucLv5+fmUlpbi5eUFwC+//IKLiwvOzvWvu3z5csOEZPVta9GiRYaq29LSUpRKJUqlkuLi4ht6vX82atQo0tPTUSqV2NnZsXjxYnr16lXvWJ1Ox4gRIzh69Ch5eXkAxMXFMWnSJIqLi5k8eTJvvvkmADExMYSHh7N+/fpGxddWXfw9wtCnuf2oPlg527VwREIIIYRoDpK0FUIIIYQQoh6dJw3BJcSHspxC0o9eIC3yHGmR5yjNLDCMqcgvIWF7FAnbowCw83bB846OeIR1xKN3h3r74RYUFDBx4kTKyspQKpW4urqyceNGFAoF06dPZ9y4cYYJx2JjY4mKimLjxo2UlZXV2VZ4eDjz58/HysoKgLlz5xIWFoaFhQXLly9v1Ov/4YcfDBOm/fzzz0ybNo0TJ07UO/bTTz8lMDCQo0ePGh77/PPPmTVrFpMnT6Zz58688MIL2Nra8vLLL7N06dJGxdZWaau1nP1xr+F+6KODWzAaIYQQQjQnSdoKIYQQQog2xdLBBqWFGdpKzTXHKC3MsHSwAcDKxZ72o3rTflRvdDodhUlZpB85R1rkedKPXaCqpNywXlFKDkUpBzn3y0FQKOrth+vv709ERES9z/v111/Xuh8SEkJRURFarbbepO2yZctq3Z8xYwYzZswwel9cz+WELegTzQpF/T1WT58+zS+//MLy5cv58ccfDY+bm5tTWlpKVVUVWq0WpVLJ0qVLGTVqFO3bt2+SGNua5P2nKUrJAcDzjo44BXq2cERCCCGEaC6StBVCCCGEEG2KjYcT41fPobygBACdVktuXh7OTk6G/rSWDjbYeDjVWVehUODg54aDnxshD92FVlNNztlk0iLPkR55nqzoeLSaav3gW9APt7lNmTKFnTt3AtQ7UVpVVRUzZsxg+fLlqFSqWstefPFFpk2bxrJly5g9ezYFBQWsXbuWLVu23JLYb0cxa/YYboc+encLRiKEEEKI5iZJWyGEEEII0ebYeDgZkrJarRZNpgXObm4obzCJqjRT4drVH9eu/nSfNpKq0goyT1zSV+FGniOvEf1wS9LzaiWWC/PyMMurbDCx3JRWrlwJwIoVKwgPD6+TuH3nnXeYMGECoaGhXLp0qdYyT09PQ69dgIkTJ/Lxxx+zc+dOlixZglqtZsGCBfj7+zfra7hd5JxNIvOEfh87BLjj1a/+iemEEEIIcXuQpK0QQgghhBBNxNxajfeAULwHhAJQlltEeuR5o/vheoR1xPOOjtj5tGPT04sabOEwfvWcZk/cAkydOpWZM2eSk5ODi4uL4fHdu3eTmJjIv//9bzQaDUVFRQQGBnLkyBFcXV0N49atW0dQUBA9e/YkNDSUiIgIIiMjmTdvHitWrGj2+G8HZ9bsNtwOfXTwNdtVCCGEEOL2IElbIYQQQgghmomVs12tfrhFSdmkHYnVV+Iev0BVcd1+uOd/PWjUtrWVGsoLSpolaZufn09paSleXl4A/PLLL7i4uODsXLsaeO/eK5NiXbp0iV69enHp0qVaFcv5+fksWrTIUHVbWlqKUqlEqVRSXFzc5LHfjkoy80nYoZ8ETu1oQ/tRfVo4IiGEEEI0N0naCiGEEEIIcQsoFArs/Vyx93O90g83Npm0I/X0wzWSpryiWWItKChg4sSJlJWVoVQqcXV1ZePGjSgUCqZPn864ceMYN26cUdsKDw9n/vz5WFlZATB37lzCwsKwsLBg+fLlzRL/7SZ23T501VoAQh4ciJnavIUjEkIIIURzk6StEEIIIYQQLUBppsK1iz+uXWr64ZZVkHkijrQj50jef5qi5OwGt7Fl1hdYudhj7+uKna8r9r7tsPd1xd7XFVsvF1QWN/dx39/fn4iIiHqXff311/U+HhAQQGxsbJ3Hly1bVuv+jBkzmDFjxk3F1RqNGjWK9PR0lEoldnZ2LF68mF69etUZt3z5chYuXEhVVRUjRoxgyZIlmJubc2jfASaFP0WVRsMY/348/OB8AHbs2MGaNWvq7F8hhBBC3B4kaSuEEEIIIYQJMLdS492/E979O9F+VG9+f+pTo9YryymkLKeQjKiLtR5XKBXYeDhj79MOOz9XQzLX3tcVazdHlKobm3RN3JwffvgBR0dHAH7++WemTZvGiRMnao2Ji4vj7bffJjIyEoVCwdNPP82XX37JrFmz+Pvf3mSy/xDa27rz99g1LHO2o6ysjPnz57N+/foWeEVCCCGEuBUkaSuEEEIIIUQr5dTBi7KcQsrz6vaG1Wl1FKfmUJyaAxG1K2CV5irsvK9U5dpdVaFr6Wxn9CRXJel5lBeU1DyflsK8PMzyKlHU9LS1dLC5JROlmbLLCVvQt52ob9+uXbuWcePG4eHhQWZmJs888wwLFy7k2ZnPUpqSR6WdM5VaDZb2NgDMnz+fl156qda2hRBCCHF7kaStEEIIIYQQrdSANx7FJcSHyuIyipKzKUzMojDpyr+ipCyqSuv2vdVWVVMQn0FBfEadZebWan2rBZ922Ptdbrvgir2PKxZ2VoZxJel5/PL4QrSVmmvGp7QwY/zqOc2SuC0vL2fSpEmcOXMGKysr3NzcWLJkCR06dKgz9oMPPmDFihUolUpsbW1ZvHgxffv2JS8vjwkTJpCdnc2gQYP44osvAMjKymLixIls3boVc/PG94+dMmUKO3fuBOD333+vszwxMRF/f3/D/YCAABITE0k5cIb72/Xivxe3obVU8fGyRURFRXHp0iU++OCDRsclhBBCCNMlSVshhBBCCCFaOQtbK1w6+eLSybfW4zqdjvK84itJ3KuSukUp2Wir6k58VlVaQW5sMrmxyXWWqR1tDX1zzSwtrpuwBdBWaigvKGm2atunn36ae++9F4VCwb///W+mT5/Orl27ao2Jioriiy++4NSpU5SWlrJlyxaef/55IiIi+Pbbbxk6dCjz5s1j2LBhREdH07VrV1599VUWLlzYJAlbgJUrVwKwYsUKwsPD603c1ufMmt14W7vwVrdHGfbhdDz6dmTUqFGsWrWK7777jrVr12Jvb88nn3yCk1PbrmgWQgghbjeStBVCCCGEEMLEWDrYoLQwa7CK1dLB5rrbUSgUWDnbYeVsh3uPwFrLtNVaSjPza1fmJmZRmJxNSXouOq2uzvYq8ovJyi8m61T8Tb2upmRpacl9991nuN+/f38++uijOuMUCgVVVVWUlJSgUCgoKCjAx8cHAHNzc0pLS9FqtVRUVGBhYcEff/yBk5MT/fv3b/KYp06dysyZM8nJycHFxcXwuJ+fHxcvXulJHB8fj5erO5lRlwBwCHDHq18In3z6KRMnTsTR0ZH33nuPkydPsmrVKj777DPeeeedJo9XCCGEEC1HkrZCCCGEEEKYGBsPJ8avnlOrX2xuXh7OTk5N1i9WqVJi6+mMraczXn1Dai2rrtRQnJrzp1YL2RQmZVGWU3hDz1MQn4FzsJch7uayaNEiHnjggTqP9+jRg1deeYWgoCAcHR2xsrJiz549APzlL39h6tSp9OrVi/Hjx+Pt7c1TTz1ldCVsQ/Lz8yktLcXLywuAX375BRcXF5ydnWuNe+ihh7jrrruYN28eCoWCZcuWMcCjMxTol4c+Mpj4hAS2bt3Kpk2bKCwsRKPRoFAoUCqVFBfX7WkshBBCiNZNkrZCCCGEEEKYIBsPJ0NSVqvVosm0wNnNDWUzJz8BVBZmOAS44xDgXmdZVWk5hUnZpB4+S9SXmxrc1v73VnP03+vx6huCV/9OePUNQd1AhfCNev/997lw4QLbt2+vsywuLo6ffvqJc+fOYWZmxo8//sijjz7Kvn37sLGxYe3atYaxr7zyCuHh4Vy4cIH3338fgLlz59KjR4+biqugoICJEydSVlaGUqnE1dWVjRs3olAomD59OuPGjWPcuHEEBgbyzjvvMGjQIKqrqxk88C56JOkrcdWONrS/pw8TJj7EZ599hkKhwMHBgccff5xu3bpha2vLmjVrbio+IYQQQpguSdoKIYQQQgghjGZubYlLiL69gDFJW4DyvGIubT7Kpc1HUSgVtOvsh1f/ULz7d8K5o3ejqnA/+ugjfvrpJ7Zt24a1tXWd5evWraNbt254eXmRmZnJtGnTePHFF6msrMTCwsIwLiIigszMTMaMGcOgQYNYtWoVOp2OadOmsXv37puKzd/fn4iIiHqXff3117Xuz5gxg6eeeorMzExSforkzGr9xGUdxw/ETG3O+vXra42fP38+8+fPv6m4hBBCCGH6JGkrhBBCCCGEaDZuPQPJjU1GU1YJgE6rIys6gazoBE58/Qfn5UVoAACsmUlEQVSWTrZ49et0pQrXvm7i9Vo++eQTvvvuO7Zt24ajo2O9YwIDA/nmm28MLQQ2btxIx44dayVsq6qqCA8P5/vvvwcw9L9VKBS3vPWApqySC+sPAaA0VxHy4MBb+vxCCCGEMA2StBVCCCGEEEI0m7AXHsCxvQeZJy+ReugsKYfOUhCfYVhenlfMpT8iufRH5A1V4SYnJ/Paa68RGBjI0KFDAVCr1Rw+fJh58+bh5eXFzJkzefDBBzly5Ah9+/ZFpVLh4ODA6tWra23rww8/ZMqUKbi769tBvPvuu4ZJzj788MPm2C0GJel5tXoXx208QGVxGQBefUPQVlU36/MLIYQQwjRJ0lYIIYQQQghxwywdbFBamKGt1FxzjNLCDEsHG1QWZniGdcQzrCN9nh9HcXquIYGbfvT8TVXh+vj4oNPp6n3ed99913BboVCwYMEC/vGPf5CZmYlbPX2B33zzzVr3x4wZw5gxY254n9yokvQ8fnl84TX3YfL+M6QeOcf41XMaNemcEEIIIVofSdoKIYQQQgghbpiNhxPjV8+pVSWam5eHs5OToTrW0sGm3mSjrYczHccPpOP4gVRXasg8eYmUg2dJPdxAFW4Xf7z6dWqSXrimoLyg5LpJbwBtpYbyghJJ2gohhBBtjCRthRBCCCGEEDfFxsPJkEzUarVoMi1wrqeS9XqursLlhXEUp+WSevgsKQdjSDt6geryq6pwT8WTdSpeX4XrbIdXvxC8+3fC8476e+H+ufVAYV4eZnmVDSaVbxVNRWWLPbcQQgghTJskbYUQQgghhBAmw9bzT1W4Jy6RcqieKtzcIi5tiuTSpitVuN79Q/Hq3wnnYC9KMwuu23oA9O0bmqr1QHWVhoqCUioKSvT/8ksoLyihIr+45meJYVl5ze3qiqpGP68QQgghbk+StBVCCCGEEEKYJJWFGZ53dMTzjitVuCmHzpJ66NpVuFFfbcLS2Y52ob433XpAp9VSWVRWK9lanl985fZVidnLSdiqkvJm2w9CCCGEaHskaSuEEEIIIYRoFWw9nQl5cCAhD9auwk05FENhQqZhXHluEcn7zxi1zZP/3YZCwZXEbEEJlYWl6LT1T3LWWAqVEktHG9QONijNzciNTW6W5xFCCCFE6yZJWyGEEEIIIUSrc3UVbth1qnAbkrz3VKPisLC3xtLBBrWj/p+lgz4hq3awqUnO2uofr0nUmttYolAoAMiJTeb3pz5t1PMLIYQQ4vYkSVshhBBCCCFEq1erCreiivO/RXDkk59uaBtmVupaCVZDMvbqJKyjrSExa2FnhdJM1UyvSAghhBBtmSRthRBCCCGEELcVldoc1y7+Ro296+9/wb1nIGp7a1Rq82aOrDZLBxuUFmYNTpZm6WBzC6MSQgghhCmQpK0QQgghhBCizbL3c8Xa1aFFntvGw4nxq+dQXlAC6CdAy83Lw9nJCYVSCegTu3+eKE0IIYQQtz9J2gohhBBCCCFEC7HxcDIkZbVaLZpMC5zd3FDWJG2FEEII0TbJJwEhhBBCCCHEbedy64HrkdYDQgghhDBVUmkrhBBCCCGEuO1I6wEhhBBCtGaStBVCCCGEEELclqT1gBBCCCFaK/m0IoQQQgghhBBCCCGEECZEkrZCCCGEEEIIIYQQQghhQiRpK4QQQgghhBBCCCGEECZEkrZCCCGEEEIIIYQQQghhQiRpK4QQQgghhBBCCCGEECZEkrZCCCGEEEIIIYQQQghhQiRpK4QQQgghhBBCCCGEECZEkrZCCCGEEEIIIYQQQghhQiRpK4QQQgghhBBCCCGEECZEkrZCCCGEEEIIIYQQQghhQiRpK4QQQgghhBBCCCGEECZEkrZCCCGEEEIIIYQQQghhQiRpK4QQQgghhBBCCCGEECZEkrZCCCGEEEIIIYQQQghhQsxaOoDWSqfTAVBYWNjCkTQ/rVZLUVERlpaWKJWmmec39RhNPT6QGJuKxNh4ph4fSIxNRWJsPFOPDyTGpiIxNp6pxwcSY1Mx9RhNPT6QGJuKxNh4ph4fSIxNpTXE2FQu5xIv5xavRZK2N6moqAgAX1/fFo5ECCGEEEIIIYQQQgjRmhQVFeHg4HDN5QpdQ2ldUS+tVktqaip2dnYoFIqWDqdZFRYW4uvrS1JSEvb29i0dTr1MPUZTjw8kxqYiMTaeqccHEmNTkRgbz9TjA4mxqUiMjWfq8YHE2FRMPUZTjw8kxqYiMTaeqccHEmNTaQ0xNhWdTkdRURFeXl7XrSqWStubpFQq8fHxaekwbil7e3uT/8Mx9RhNPT6QGJuKxNh4ph4fSIxNRWJsPFOPDyTGpiIxNp6pxwcSY1Mx9RhNPT6QGJuKxNh4ph4fSIxNpTXE2BSuV2F72e3dJEIIIYQQQgghhBBCCCFaGUnaCiGEEEIIIYQQQgghhAmRpK1okFqt5u9//ztqtbqlQ7kmU4/R1OMDibGpSIyNZ+rxgcTYVCTGxjP1+EBibCoSY+OZenwgMTYVU4/R1OMDibGpSIyNZ+rxgcTYVFpDjLeaTEQmhBBCCCGEEEIIIYQQJkQqbYUQQgghhBBCCCGEEMKESNJWCCGEEEIIIYQQQgghTIgkbYUQQgghhBBCCCGEEMKESNJWCCGEEEIIIYQQQgghTIgkbYUQQhhNq9W2dAhCCCGEuAY5TgshhBC3D0naimYlHxzbhoqKipYOoUEZGRmkpqa2dBjXlJiYyMmTJ1s6jOs6e/YsixYtaukwrqu6upqqqqqWDkPcAjqdrqVDuC3Icfr2J8fopiHH6caTY3TbIsfpxpNjdNsgx+nGaw3H6JslSVvRLAoKCgBQKpUme7BJTU3l119/Zc2aNRw7dqylw6lXXFwcn376KW+++SYbNmxo6XDqdebMGYYNG8a+fftaOpRrOn78OH379uXs2bMtHUq9Tp48yd13381XX31Fbm5uS4dTr1OnTtGzZ09ee+01Dh8+3NLh1Cs2Npbnn3+e+++/n3feecck92V8fDxfffUVixcvZtOmTS0dTh0XL17kgw8+4K233uKHH36gpKSkpUOq4/L/q0KhMNkvhElJSaxYsYLPPvuMHTt2tHQ49ZLjdOPJMbppmPoxGuQ43RTkGN005DjdNEz9OC3H6KYhx+mmYerH6dZwjG4UnRBN7PTp0zoHBwfdP/7xD8Nj1dXVLRhRXSdPntQFBQXpwsLCdH5+fjo/Pz/dxo0bWzqsWk6cOKHz8fHRDRs2TDdw4ECdQqHQ/frrry0dVh1PPvmkTqFQ6IKCgnQHDhxo6XDqiIqK0tnY2Oheeumllg6lXufPn9e5urrqZs+erSsvL2/pcOoVFRWls7S01E2ZMkU3ZMgQ3dy5c3U6nWn9XZ86dUrXrl073SOPPKJ77rnndObm5roFCxa0dFi1nDx5Uufm5qYbOnSobsiQITqlUql74okndIcPH27p0HQ6nX4fOjo66gYPHqy76667dCqVSjdx4kTdli1bWjo0g9OnT+vMzMxq/T1rtdqWC6geJ0+e1Pn7++sGDhyoCw0N1Zmbm+u+/fbblg6rFjlON54co5uGqR+jdTo5TjcFOUY3DTlONw1TP07LMbppyHG6aZj6cbo1HKMbS5K2okklJSXpevXqpevYsaPO2dm51gcyUznYXLhwQeft7a0LDw/X5eXl6U6ePKmbOXOm7qGHHtIVFxebxAeL2NhYnY/P/7N353E21v//x5/nzGob+xqGtKBQEU0JExEqIWXJEh8qtJFkyVopfdrTJh/6FFpkS1LKlhApu49KlsQYYcbWzDDn/fvDb66vY8zinDNz3jPzuN9u3T7OdV3nXI9zzsxc83m5XKeyGTZsmElOTjZHjhwxbdq0MZMmTQp2Wjr/+c9/zNChQ02fPn1M6dKlzYoVK4Kd5NiyZYspVqyYeeqpp4wxxpw5c8b88ssv5ocffjBbtmwJct1Zr7zyiunatasxxpjTp0+bt99+2wwdOtS89dZbZseOHUGuM+bnn382xYoVMyNGjDDGGDNkyBBTtmxZk5CQYIyx4xfxo0ePmhtuuMEMGzbMWTZq1CgzaNAgc/r06SCW/Z+///7b1KtXz3kdjTFm4cKFxu12mzvuuMMsWbIkiHXGnDp1yrRp08YMHDjQWfbjjz+a+vXrm1tvvdXMnTs3iHVn/fXXX6Zhw4bmuuuuM0WKFDGPPfaYs86Gr0NjjPnjjz9MdHS0GTp0qPnnn39MfHy8GTVqlLnuuutMXFycFZ0cp/3HMTow8sIx2hiO0/7iGB0YHKcDw/bjNMfowOA4HRh54Tht+zE6EBjaImBSU1PNq6++ajp06GCWLFlinn/+eRMVFWXVwSY5Odk8/vjjplOnTiYlJcVZPmXKFFOpUiVz7NixINadlZycbLp27Wp69uxpzpw54yzv2LGj6d69u+ndu7d54403zJEjR4JY+X8+/vhjc+ONN5pTp06Z22+/3ZQrV85s27bNjBw50nz88cdB60pKSjLXXnutqVixojlw4IAxxpi77rrLXHvttaZUqVKmSJEiZuLEiUHrS3P//feb+++/3xhjzM0332yuv/5607x5c1OiRAnTunVrs3DhwqC1HTx40BQqVMg88cQTzrK9e/eaK6+80owdOzZoXefbv3+/qVevnvnqq6+cZffff79p3Lixue6668yDDz4Y1NfRmLO/4NavX99s3brVeDwek5ycbPbv32+uuuoqU6FCBdOhQ4egf0/HxMSYcePGGWP+72f1li1bTJMmTUzr1q3Nxo0bg9bm8XjMRx99ZDp16mR++OEHM2PGDBMREWEef/xxr22C6fTp02bkyJHmrrvuMqdOnXKWL1q0yFSsWNHExcUFse4sjtP+4xgdGHnlGG0Mx2l/cYwOHI7T/rH9OM0xOjA4TgdGXjlO23yMDhSGtgioX3/91cyYMcMYY8yRI0fMhAkTrDrYnD592kyaNMm8/vrrxpj/++Vh165dJjo62vz5559BazvXli1bvP6p07PPPmvcbrfp1q2b6d+/v3G5XF5/0x5Mv/76q2nWrJlz+9577zURERGmdOnS5tdffw1imTFLly41V155pencubO57rrrTMuWLc33339v1q1bZ15//XXjcrnM22+/HZS2tK+9UaNGmX79+pk5c+aYW2+91cTHxxtjzr6ujRs3NnfffXdQ+ow5+z28fPlyr2XJycmmc+fO5qabbnKWBfuX8N27d5vChQubkSNHmo0bN5rx48ebQoUKmbFjx5rXX3/dXH/99aZt27bOLxzB8MsvvxiXy2W+++47Z9nvv/9ubrvtNjN9+nTjcrnMe++9F5Q2j8djjh8/bpo2ber8XDl9+rTzi+6mTZtMpUqVzKBBg4LSl2bv3r1e/6xt+vTpJiIiwqozeT799FOvf85ozNmzzKpUqWLNGQkcp/3HMTowbD5GG8NxOlA4RvuP43Tg2H6c5hgdGBynA8Pm43ReOEYHCkNbBNy5B+NDhw6l+1vCM2fOmPnz55tDhw4FpW///v3On9Na//rrLxMdHW12797tLNu+fXtQ+s63adMm06JFC7Nw4UKnbdasWSY0NNT873//C3LdWfXq1XP++UHXrl1NkSJFTMmSJc26deuC0nPu1+DSpUtNhQoVTNOmTb3ee2OMGTx4sKlTp445fPhw0H6JXLRokXG5XObmm282//rXv7zW/fjjj8blcpn169cHpe18557VERERYaZMmRLkov8zbdo0U7hwYdOmTRtTrFgxM2vWLGfd5s2bjcvlMvPnzw9a3+nTp0337t3NZZddZt58800zc+ZMU7JkSdO/f39jjDGPPfaY6dy5szl9+nTQvhY//vhjr+t9paamOmdRfPjhh6ZkyZJm7969QWm7kDNnzqQ7k+f06dPmo48+Mps3bw5K0z///OP8Oe19PH78uKlSpYr55ZdfnHVr167N7TQvHKcDh2P0xctLx2hjOE4HAsfowOA47b+8cJzmGB1YHKcvXl46TuelY7SvQoP9QWjI2/bv36+//vpLhw8fVosWLeR2u+V2u3XmzBmFhoaqTJky6t27tyTpueeekzFGhw8f1muvvaa9e/fmauPff/+tVq1aqXz58pLkNHo8Hh07dkynTp1SeHi4XC6Xhg0bphdeeEFHjx5VVFSUXC5XrjSe/zpKUp06dfTf//5XFStWdLZ3u92qXbu2ypQpk6NdGTXeeuutcrlccrvd+ueff1SyZEkdP35cjzzyiJYtW6YlS5bopZde0g033KBVq1apYcOGud7YvHlzSVKzZs20YMECbdu2TWXLlvXaPjIyUoULF1bJkiVz/D0+v69FixaSpFatWmno0KGaOHGiSpQooZMnT6pIkSKSpJIlS+raa69V8eLFc7wto8a0r0WPxyO32y1jjKpXr67bb79dX331lbp27aqIiIhcef0u1Ni8eXO5XC717NnTec/bt2+va665Rh6PR8YYlShRQtdee62KFSsWlMZbb71VoaGhGjp0qCZNmqTRo0erQoUK6t+/v5555hlJZz8l+OjRowoNzZ3D8unTpxUWFiZJzic733333Vq5cqXuvfdezZkzR7fddpvzc6hkyZKqWLGi87WZ240XEhISok6dOkmS7r//fklSamqq3n77bf3+++9BaYyMjHT+7HK5dObMGZ04cUJnzpxR4cKFJck5vsTHx+fKz3CO04Hr4xgduEYbj9HnN3Kc9r+PY7TvOE7nTKNtx2mO0YFt5DgduEYbj9N54RidI4I2Lkaet3HjRlOlShVTu3ZtExoaaq699lrz9ttvm+PHjxtjjNc1ZA4dOmQmTJhgXC5Xrv6t0YUa33rrLacx7YyEnTt3mooVK5qjR4+aMWPGmGLFiuXap8Vm9Toak/6fEg0ZMsS0adMm164blFFjYmKiMebs37IVKlTIVKpUyfmbrOTkZHPffffl2gXAL9Q4adIkp/Hc6y6lefDBB03v3r1NcnJyjv/tYEZfiydPnjSHDh0yDzzwgAkJCTGjR482O3fuNCdOnDCjRo0ytWrVMgcPHszRtswaz/1aPPefY6X9k7fcPhMho/c57Xvhjz/+MGXKlDHffvutc5/Ro0ebyy67zPz1119BabzmmmvMe++951w/bd++fenOUujRo4cZOnSo8Xg8Of61uGXLFnPnnXearVu3plu3a9cu06dPHxMeHm7ef/99ExcXZ5KSkszQoUNNvXr1cu36X5k1nu/MmTPmww8/zPXjS3YaPR6P+fvvv02lSpXM7t27zdixY03RokVz7fuG43TO9HGMDkyjTcfojBo5TvvfxzH64nGcDgzbj9Mco3OukeN0YBptOk7nhWN0TmFoC58cOnTI1KpVywwdOtTs2rXLxMfHmy5duphGjRqZxx57zPkBeO4vj927dzdRUVHZOrjnZqMxZz/IoW7duqZTp04mPDzc/PTTT9Y1GnP2n6OMHDnSlChRItf+WVFmjY8++qg5deqUmTdvnmnbtq3XPyvKTb68jk8//bQpWbJkrnw9ZtR3/fXXm0GDBpmTJ0+aEydOmPHjx5uIiAgTHR1t6tWrZypWrGh+/vnnHO/LrPH81/DcXyCvvfZa0717d5Oampor/ycmq8a0T8p+8MEHTWhoqGnTpo1p3bq1KV++fK59bWb2Xp/bmGbnzp1m+PDhpkSJEmbbtm053rdr1y5z6aWXGpfLZa655poL/iJ44MABM27cOBMWFmZq1Khh6tWrZ8qUKZNrX4vZaTxXamqq6dOnj4mKisqV1/BiG0+dOmWuvvpq07JlSyuPLxynA9NnDMdoXxptOEZn1shxOnB9HKOzh+N07jcG4zjNMTr3G43hOO1Low3H6bxwjM5JDG3hk82bN5tq1ap5fUppcnKyGTVqlGnYsKEZMWKEc80gj8djPvzwQ1O+fPlcvZ7IxTRu2bLFuFwuU6hQIbNhwwYrG3/66Sdz3333merVq+fqD/TMGhs0aOB8OvG5f5uZ2y7mdVy7dq3p1KmTqVy5cq69jlm9hk8//bRJSkoyxhizYcMG8/nnn5vZs2ebPXv25EpfVo3nv4ZpXnvtNfPbb79Z1ZiSkmKOHDliJk2aZDp16mSGDx+ea39Dnd3GtNfx0KFD5sEHHzRXXnllrvxCkZSUZMaMGWPat29v1q1bZxo2bGhq1aqV4evz888/m5kzZ5oZM2aYXbt25XifL43GGLNw4UJTvXr1XDvr5GIaPR6P2bNnj3G5XCYiIiJXP9mb43Tu9nGMzpjtx+isGjlOB66PY3TmOE7nfmOwjtMco3O/keN0xmw/TueFY3ROYmgLn+zYscNUr17dfPHFF8aYsxeVT/vfIUOGmGuuucasWLHC2f6PP/4wu3fvtrbx6NGj5oknnsi1v/31pXHfvn1m/vz55o8//rCqsW7duub77783xgTvE2Ev5nX8888/zWeffWZ+//13a/rq1auX7tOfc9vFvIZp62xrrFevnlm5cqWzfTC+Hi/2Z+POnTvNvn37cqUtNTXVfP755+azzz4zxpz9uZfR/5EJ1vfyxTSm+euvv3L1U8d9aXzxxRdz7cyYNBync7ePY7TvjcE+RmenkeO0/30co7PGcTp4jbl9nOYYnfuNHKd9bwz2cTovHKNzEkNb+CQpKck0aNDA3H777c4/w0r75vF4PKZOnTqmR48ezm3bG9O2t7Gxe/fuud51rot9HYPB9tcxv7yGeaGR75fMnfvPZo0x5u+//3b+j8yvv/5qjDnb+8MPPwTlZ6IxF9d4/lllueViGlNSUoJyHOQ4nTt9/MzJGq9jYNjeyPscGBynA8P24zTH6Nxr5OdO1mx/HfPCa5iT3MH+IDTkPR6PRxEREZo6dapWrFihhx56SJIUGhoqY4xcLpfuvPNOxcfHS1Kufqq8L43m/38ia0REhJWNhw4dytUuXxrT3mubG4P1Ouan1zAvNPL9krmQkBBJ//dJ1KVLl9aXX36pYsWKqV27dtq6dasefvhhPf744zpx4oT1jSdPnrS68dFHH9Xx48dz/TjIcTr3+viZE5hGXse83cj7HDgcp3O3MRjHaY7RudvIz53ANPL/pYOHoS0umtvtVmpqqq6++mp98MEHmjlzpnr06KGDBw862+zatUslS5ZUamqq9Y0ej8f6xrzwOtKYN/toLFiNab9Yp/0fAGOMypQpo4ULF6pEiRKqW7euPvjgA02aNEmlS5em0c/Gt99+W6VKlcr1vrzwtWj7cTq/vYY00liQ+/JKo5S/joF5oTEYx+m88LVo+zH6YhvzwutIY97syw0uk/YTDchA2t9gpDlz5oxCQ0N14sQJJScna8OGDeratauio6NVqlQplS5dWvPmzdPq1atVp04dGmksUI2299FYcBrP70tNTVVISIiOHTsmj8ejEiVKeG3fu3dvzZ8/XytWrFDt2rVzvI/GwPF4PHK7/+/v4W37WswLjbb30UijTY229+XVxrxwfKHx4tn++yKNNNrUaHtfMHCmLTKU9jcVaXN9Y4zzTbN7925dccUVWrdunZo3b66tW7eqTZs2uuSSS1SuXDmtXbs2V75paKTRlkbb+2gsOI0Z9YWEhGj37t2qVauWVq9e7WxvjNEbb7yhadOmafHixbnyf2BoDIy///5b0v+dhZDWbcvXYl5otL2PRhptarS9L6835oXjC43Zt3PnTh09ejTdX/za9LVII422NNreF1TZuO4tCqAdO3aYxx57zHTo0MGMHTvW61MW9+7da8qUKWP69OljPB6PczHotIukp6am0khjgWq0vY/GgtOYnb5//etfXh9q4fF4zNKlS81vv/2W4300BraxWLFipm/fvs6ytK85G74W80Kj7X000mhTo+19+akxLxxfaMzchg0bjMvlMlOmTEm3zpavRRpptKXR9r5g40xbpLN582bdeOONOnr0qDwej7766ivNnDlTxhidPn1a8+bN03333afJkyfL5XI5F3pPkxsXS6eRRlsabe+jseA0Zrfvvffe82pxuVxq1qyZLrvsshztozGwtm3bpkKFCmnz5s164IEHJJ394JWUlBTNnz9f3bt317vvvhu075e80Gh7H4002tRoe19+anznnXesP77QmLGNGzfqpptu0pNPPqnevXunWz937tyg/05LI422NNreZ4XgzIphq507d5ro6GgzYsQIZ1mfPn3MI4884rVd2t9wBAONgUGj/2zvM4bGQLG90fY+Y2gMtIULF5orrrjCPP/886ZOnTrmgQcecNb9+eefQSz7P7Y32t5nDI2BQqP/bO8zhsZAodF327dvN6GhoWbcuHHGmLNnAX733Xfm3XffNT/88IOJj493ltNIY0FvtL3PFqHBHhrDHqmpqVq8eLGaN2+uwYMHOxeBLlSokLZs2aKmTZsqOjpaDz74oG688cZ0F4mmkcaC1Gh7H40Fp9H2PhpzRp06dVS/fn3961//Unh4uKZNm6ZBgwYpMTFRDRs2VO/evRUWFha0vrzQaHsfjTTa1Gh7H400BrvR4/Ho008/VWpqqu6++25J0q233qrDhw9r9+7dKl26tKpXr66XX35ZdevWzdU2Gmm0rdH2Pqvk3nwYecEff/xhtmzZ4tweO3asiYyMNM8995wZNWqUuffee82ll17qdX0/GmksqI2299FYcBpt76Mx8E6ePGnq1q1rfvnlF3Py5Enz3nvvmdKlSxuXy2U2bdpkjAn+GcG2N9reRyONNjXa3kcjjTY0xsXFmX79+pmIiAhz9dVXmw4dOpgNGzaYlJQUM3v2bNOyZUvTqVMnc/z48Vxvo5FG2xpt77MFQ1ukk3ZR56SkJNOmTRuzYMECZ933339vypUrZ7755ptg5RljaAwUGv1ne58xNAaK7Y229xlDY6CkpKSYM2fOmJYtW5rvv//eGGPMvffea6Kioszll1+e7nIOwWB7o+19xtAYKDTm/z5jaAwUGv0XHx9v+vfvbxo0aGC2bdvmte6VV14xFSpUMPv27QtS3Vk0BgaN/rO9zwZcHqGA279/v37++WelpKQoOjpa9evXl8vlUmpqqiIiIvTFF1/I7XbL4/HI7XarVKlSKl++vEqVKkUjjQWu0fY+GgtOo+19NOZMY7Vq1XTdddc5/+Szfv36+v333/Xee+9pxYoV+uKLL7R582Y9//zzCg0N1UsvvURjHuijkUabGm3vo5FGmxrP7atataoaNGigsmXLauTIkdqzZ49q1Kgh6ewlmEJCQnTZZZepZMmSCg8Pz/E2Gmm0rdH2PmsFe2qM4Nm0aZO59NJLTcOGDU2ZMmVMgwYNzGeffea1TdoZR2meeuopc/3115tDhw7RSGOBarS9j8aC02h7H4251zhmzBjjcrlM9erVzfr1640xxhw9etS89dZbZufOnTTmgT4aabSp0fY+Gmm0qfFCfZ9++qmz/vzfIYwx5tFHHzW33nqrOXHiRI730UijTY2299mMoW0B9fvvv5vKlSubJ5980iQkJJiffvrJ9OzZ0/Tu3ducOXMm3TfNnj17zJAhQ0zJkiXNxo0baaSxQDXa3kdjwWm0vY/G3Gk8ffq0McaY06dPm/79+5u1a9caY/7vF97c+pRd2xtt76ORRpsabe+jkUabGn35PeKJJ54wpUqVcq65SyONBaXR9j7bMbQtgJKTk82gQYPMPffcY5KTk53lU6ZMMaVLlzZ///231/br1q0z/fv3N/Xq1TMbNmygkcYC1Wh7H40Fp9H2PhqD1xgMtjfa3mcMjYFCo/9s7zOGxkCh0X8X2/fjjz+a3r17m5o1a5pffvmFRhoLVKPtfXkB17QtgDwejypXrqxatWopPDxcxhi5XC7deOONKlq0qE6fPu21fYMGDfTPP/9o5MiRqlixIo00FqhG2/toLDiNtvfRGLzGtPu43e5c6csLjbb30UijTY2299FIo02NF9vXsGFDHT9+XOPGjdMll1xCI40FqtH2vjwhNybDsM8ff/zh/DntdPQDBw6Yyy67zOzdu9dZ99NPP+V6WxoaA4NG/9neZwyNgWJ7o+19xtAYKNlt/Pnnn3O9LY3tjbb3GUNjoNDoP9v7jKExUGj0X376PYLGzNHoP9v7bJd7f2WGoDpw4IDWrl2rRYsWyePxqHr16pLOfjKfy+WSJCUmJuro0aPOfUaNGqVbb71Vhw8fljGGRhoLTKPtfTQWnEbb+2gMfmPz5s1pzCN9NNJoU6PtfTTSaFNjfv49gkYaC1pfnpMbk2EE18aNG010dLS54oorTPHixU3NmjXNjBkzzOHDh40x//e3HTt27DBly5Y1R44cMePHjzeFChXKtb/toJFGWxpt76Ox4DTa3kcjjTY12t5HI402NdreRyONNjXa3kcjjTY12t6XFzG0zefi4+NNzZo1zfDhw83OnTvNX3/9Ze69915Tq1YtM3r0aBMfH+9se/DgQXPttdeae++914SHh+faNw2NNNrSaHsfjQWn0fY+Gmm0qdH2PhpptKnR9j4aabSp0fY+Gmm0qdH2vryKoW0+t3XrVlOtWrV03wRDhw41derUMRMnTjQnT540xhizbds243K5TKFChXL1k/popNGWRtv7aCw4jbb30UijTY2299FIo02NtvfRSKNNjbb30UijTY229+VVDG3zuQ0bNpjKlSubFStWGGOMOXXqlLPukUceMdWrVzcbN240xpy9GPSAAQPM9u3baaSxQDba3kdjwWm0vY9GGm1qtL2PRhptarS9j0YabWq0vY9GGm1qtL0vr2JoWwBcf/31JjY21rmdlJTk/LlBgwamc+fOzu1//vknV9vS0BgYNPrP9j5jaAwU2xtt7zOGxkCh0X+29xlDY6DQ6D/b+4yhMVBo9J/tfcbQGCg0+s/2vrzIHewPQkNgnTx5UsePH9exY8ecZe+++662bt2qrl27SpIiIiJ05swZSVKTJk108uRJZ9vIyEgaaSwwjbb30VhwGm3vo5FGmxpt76ORRpsabe+jkUabGm3vo5FGmxpt78svGNrmI9u2bVOHDh3UtGlT1apVS9OnT5ck1apVS6+99poWL16sTp066fTp03K7z7718fHxKlKkiM6cOSNjDI00FphG2/toLDiNtvfRSKNNjbb30UijTY2299FIo02NtvfRSKNNjbb35Su5cTovct7WrVtN6dKlzeOPP26mT59uBg0aZMLCwszPP/9sjDHm5MmTZv78+aZy5cqmZs2a5q677jL33HOPKVKkiNm8eTONNBaoRtv7aCw4jbb30UijTY2299FIo02NtvfRSKNNjbb30UijTY229+U3LmMYced1R44cUZcuXVSzZk299tprzvLY2FjVqVNHr7/+urPs+PHjeuaZZ3TkyBFFRkbqoYceUu3atWmkscA02t5HY8FptL2PRhptarS9j0YabWq0vY9GGm1qtL2PRhptarS9Lz8KDXYA/Hf69GklJCTo7rvvliR5PB653W5Vr15dR44ckSSZsx86p2LFiumFF17w2o5GGgtSo+19NBacRtv7aKTRpkbb+2ik0aZG2/topNGmRtv7aKTRpkbb+/IjXrV8oHz58vroo4908803S5JSU1MlSZdcconzjeFyueR2u70uEu1yuWikscA12t5HY8FptL2PRhptarS9j0YabWq0vY9GGm1qtL2PRhptarS9Lz9iaJtPXH755ZLO/g1GWFiYpLN/wxEfH+9sM2HCBL3//vvOp/fl9jcOjTTa0mh7H40Fp9H2PhpptKnR9j4aabSp0fY+Gmm0qdH2PhpptKnR9r78hssj5DNut1vGGOebIu1vO0aNGqVnnnlGv/zyi0JDg/u20xgYNOb/vrQmGv1ne6PtfWlNNPqPxvzfl9ZEo/9ozP99aU00+o/G/N+X1kSj/2jM/335BWfa5kNpny0XGhqqKlWq6N///rcmTpyon376SfXq1Qty3Vk0BgaN/rO9T6IxUGxvtL1PojFQaPSf7X0SjYFCo/9s75NoDBQa/Wd7n0RjoNDoP9v78gPG3vlQ2t9whIWFafLkyYqKitLKlSt13XXXBbns/9AYGDT6z/Y+icZAsb3R9j6JxkCh0X+290k0BgqN/rO9T6IxUGj0n+19Eo2BQqP/bO/LFwzyrXXr1hmXy2W2bt0a7JQM0RgYNPrP9j5jaAwU2xtt7zOGxkCh0X+29xlDY6DQ6D/b+4yhMVBo9J/tfcbQGCg0+s/2vrzMZcz/P58Z+dLJkydVpEiRYGdkisbAoNF/tvdJNAaK7Y2290k0BgqN/rO9T6IxUGj0n+19Eo2BQqP/bO+TaAwUGv1ne19exdAWAAAAAAAAACzCB5EBAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAFnK5XBo4cGCwMwAAABAEDG0BAABQ4EybNk0ul0sul0srV65Mt94YoypVqsjlcun222/PsY5Vq1ZpzJgxSkhIyLF9AAAAIO9haAsAAIACKzIyUjNmzEi3fPny5dq3b58iIiJydP+rVq3S2LFjGdoCAADAC0NbAAAAFFht2rTRZ599pjNnzngtnzFjhurXr68KFSoEqQwAAAAFGUNbAAAAFFhdunTR4cOHtXjxYmdZSkqKZs2apa5du6bb/uTJkxo8eLCqVKmiiIgIXXnllfr3v/8tY4zXdmnXo507d66uvvpqRURE6KqrrtKiRYucbcaMGaMhQ4ZIkqpXr+5crmH37t1ej5XZYwAAACB/YmgLAACAAqtatWqKiYnRzJkznWVfffWVEhMT1blzZ69tjTG688479corr+i2227Tyy+/rCuvvFJDhgzRoEGD0j32ypUr1b9/f3Xu3FkTJ05UUlKSOnbsqMOHD0uSOnTooC5dukiSXnnlFX344Yf68MMPVbZs2Ww/BgAAAPKn0GAHAAAAAMHUtWtXDRs2TP/8848KFSqk6dOnq2nTpqpUqZLXdvPnz9eSJUv0zDPPaMSIEZKkAQMGqFOnTnrttdc0cOBA1ahRw9l++/bt2rZtm7MsNjZW9erV08yZMzVw4EDVrVtX1113nWbOnKm77rpL1apVS9eW1WMAAAAgf+JMWwAAABRo99xzj/755x8tWLBAx48f14IFCy54aYSFCxcqJCREjzzyiNfywYMHyxijr776ymt5ixYtvIa4devWVVRUlP74449stwXiMQAAAJD3cKYtAAAACrSyZcuqRYsWmjFjhk6dOqXU1FTdfffd6bbbs2ePKlWqpGLFinktr1WrlrP+XFWrVk33GCVLltTRo0ez3RaIxwAAAEDew9AWAAAABV7Xrl3Vt29fxcXFqXXr1ipRooTfjxkSEnLB5ed/aFlOPwYAAADyHi6PAAAAgAKvffv2crvdWrNmzQUvjSBJ0dHR2r9/v44fP+61/H//+5+z/mK5XK6LjwUAAEC+x9AWAAAABV7RokX19ttva8yYMbrjjjsuuE2bNm2UmpqqN99802v5K6+8IpfLpdatW1/0fosUKSJJSkhIuOj7AgAAIP/i8ggAAACApJ49e2a6/o477lBsbKxGjBih3bt3q169evrmm280b948PfbYY14fGJZd9evXlySNGDFCnTt3VlhYmO644w5nmAsAAICCiaEtAAAAkA1ut1vz58/XqFGj9Mknn2jq1KmqVq2aXnzxRQ0ePNinx7z++us1fvx4vfPOO1q0aJE8Ho927drF0BYAAKCAcxk+xQAAAAAAAAAArME1bQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAPKJZcuWyeVyOf/t3r07W/ebNm2a1/0Kit27d3s972XLlgU76YJ8fV9x8fLK1wQAAMj/GNoCAIAC4/zhl8vl0p133nnBbb/++ut02/bq1St3g8+RHwd3t99+u9dzioiI0JEjRwLy2Hll+JZf3tfzB/9FixZVfHy81zZbtmzx2mbatGnBiQUAAMgDQoMdAAAAEExffvml/vjjD1166aVey1977bUgFRUMcXFxWrRokdeylJQUzZgxQwMHDsyVhlKlSunFF190bteoUSNX9nuxatSo4dVZqlSpINZkz8mTJ/Xss8/yfQQAAOAjhrYAAKBA83g8evPNN/Xyyy87y3799dd0A0UE1ocffqjU1NR0y6dNm5ZrQ9uoqCg98cQTubIvf1SpUiVPdJ7v3Xff1eDBg1W1atVgp+RJJ0+eVKFCheR2848jAQAoiPgNAAAAFFhpw5D//Oc/OnnypLP8jTfekDFGkhQSEpLpY/z1118aMmSI6tSpo6JFiyoyMlLVqlXTfffdp7Vr16bbfsyYMc4/D69WrZoSExM1ZMgQRUdHKzw8XJdeeqmee+45Z/+S5HK5FBsb6/U41atXz/KyDcYYvf/++7rmmmsUGRmpcuXK6V//+peOHj2a5Wvj8Xh06aWXOvsYPnx4um2GDBnirK9du3aWj3muc/9p/BVXXOH8ef369dqyZUuG99u3b5+GDh2qa6+9VlFRUYqMjFTVqlV11113afHixZKkatWqqXr16l73i42NdVqbNWsmKeNLKHTv3j3dtuf66quvnPUhISH6888/JZ291EGfPn103XXXqWLFioqIiFDhwoV12WWX6f7779fmzZu9Hie772tWl1BITU3Vf/7zHzVv3lxlypRRWFiYSpcurdjYWE2ePFlnzpzx2v5Cz/vjjz9Wo0aNVLhwYZUsWVKdOnVynpevkpOTNWbMmGxtm9V1lTO6rML590tMTNQjjzyiihUrqkiRIoqNjXW+D//44w/dfffdKlmypIoVK6bbbrst06+1NJ988okaNGigwoULq1y5curdu7cOHjx4wW03btyo3r17q0aNGipUqJCKFi2qa6+9Vs8995zXz5g01apVc9rHjBmjlStXqkWLFipevLiKFi2qY8eOSZLmz5+v2267TeXLl1dYWJiioqJUo0YN3XXXXZowYYI8Hk92XmYAAJCXGAAAgAJi6dKlRpLz31133eX8edKkScYYYxITE02xYsWMJHPttdea6OhoZ5uePXt6Pd7y5ctNyZIlvR7z3P/cbrd56aWXvO4zevRoZ33p0qVNrVq1Lnjfp59+2rlPRo9/ftf5z69Vq1YX3L5JkyZeTVOnTvVan+bFF190llWqVMmcOXPG637nvjYTJ07M9vvw448/eu3vq6++MmXLlnVuDxo06IL3+/LLL5335kL/Pfroo+m6LvRf06ZNjTHG7Nq1y2v50qVLjTHGfPfdd17v4b59+7w6unfv7qxv2bKls3zw4MGZ7jc8PNwsXrzY7/d1165dzmOcOHHCNGnSJNPHady4sTl+/Lhzn/Ofd+PGjS94v8svv9z8888/2XpPz/8aqlChgpFkQkJCzPbt240xxmzevNlrm6lTp2Z4//Nl937169dP9zwiIyPNvHnzTKlSpdKtK126tImPj8/wtWnbtu0FX5tLL73U637GGPPWW2+Z0NDQDN+H2rVrmwMHDnjd59yv1ZiYGBMSEuJ1n6NHj6Z7jhf6L7vvEwAAyDs40xYAABRY3bp1U5kyZSRJb775piRp6tSpOn78uCTpkUceyfC+CQkJ6tChg3PWaqFChdS/f3899dRTio6OlnT2bNUnnnhCy5cvv+BjHD58WDt27FCPHj301FNPOS3S2WvqpqSkSJJefPFFPfjgg173HT58uF588UW9+OKL6ty58wUf/+uvv1bz5s319NNPq06dOs7yFStWaM2aNRm/MP9fnz59VLhwYUnS/v379eWXXzrr1q5dqz179kiSQkND1b179ywfL825Z0qWK1dOt956q+6++25n2fTp09OdHbpnzx516tTJeW9cLpfatWun0aNHq3///qpZs6az7YgRI9KdGfzggw86r9dDDz2UaV9sbKyqVasm6ex7+PHHHzvr/vnnH82dO9e5ff/99zt/LlKkiJo2baqBAwdq1KhRmjBhgp544gnVqlVL0tlr9p77NeXr+3quRx55RCtWrHBut2zZUqNHj1arVq2cZStXrsz0a3nlypW6/vrrNXLkSN10003O8t9++83ruV6MkSNHSjp7FvDTTz/t02P44pdfflHfvn31+OOPKywsTJKUlJSkdu3a6dixY+rfv7/+9a9/OdsfPnxYU6ZMyfDxvvzyS8XGxmrUqFFq3ry5s/yPP/7Q0KFDndurVq3SwIEDna/bG264QWPGjNHgwYOd7+tt27apR48eGe5r9erVioiIUL9+/TR27FjdddddCgkJ0dtvv+1sc/3112vs2LF6+umn1atXL+drCwAA5EPBnhoDAADklvPPWPziiy/M8OHDnduLFi0yl112mZFkypYta5KSkjI80/aVV17xeqyFCxc66w4ePGiKFi3qrGvXrp2z7twzbSWZV1991Vk3d+5cr3WbNm3KsP3csy0z2qZ9+/bG4/EYY4w5fPiw11l8r7/+unO/zM5y7Nu3r7P8jjvucJafe1bpucuzkpSU5HV28oABA4wxxqxYscKrYf78+V73GzRokNf66dOne61PTU31ek0yOov2XJltM2bMGK+zN9N8+umnzvKSJUuapKSkdB0//vijmTZtmnn11VfNiy++mK597969zva+vK9p2/z9999e7+k999zjdb977rnHWRcSEmL+/vvvCz7vhg0bmpSUFGOMMSkpKaZcuXLOuozOej7f+V9DmzdvNi1btjSSjMvlMuvXr8+VM22feeYZZ12XLl281r344ovOuhtuuMFZ3qFDB2f5+a9Ny5Ytne8hj8fjPCfp7JnTJ0+eNMYY0759e2d5s2bNTGpqqvOYa9eu9XrMjRs3OuvO/fkSEhJi1q9fn+65161b19lm9erV6dbv2rXLa38AACB/4ExbAABQoPXv31+hoWc/m7VPnz76/fffJUn9+vVTREREhvdbvXq18+eyZcuqdevWzu1y5cp53T5323OFhITogQcecG5feeWVXuuzc+3ZzDz00EPO9UFLlSrldSZvdh/74Ycfdv68cOFC7d+/X5I0a9YsZ/m5Z5tmZd68eV77TjubtHHjxqpcubKzfOrUqV73W7lypfPnWrVqqWvXrl7r3W63c3ZsIPTq1ct57davX6/ffvtNkjRz5kxnmy5dunh9jSxevFjVq1dXo0aN1KtXLz322GMaMmSI14fcSWevyxsIa9eu9fowt549e3qtP/d2amrqBa+xLEn/+te/nLNSw8LCvK4H7M/X4HPPPSeXyyVjzAWviZwT7rvvPufP53893HPPPc6fa9So4fw5s+d43333OV8HLpdL3bp1c9alpKQ41yn+4YcfnOXLli1TSEiIc63ahg0bej3mqlWrLriv1q1b67rrrku3/Oabb3b+fOutt6ply5YaMGCAJk2apM2bN6tatWp8WBkAAPkQR3cAAFCgXXLJJerYsaOksx8qJp0dXPXv3z/T+x05csT5c/ny5dOtP3dZRkOh8uXLKzIy0rl9/pDY3w8XOn9ode7jZ/ex69Sp43wYV2pqqqZOnaoff/zRuTRC2bJldfvtt2e76dxhbJUqVZx/ju9yuXTvvfc667788ksdPnzYuX3u633+h4zlhOjoaN1yyy3O7RkzZigxMVELFy50lvXu3dv58/79+3XXXXdp7969WT52cnJyQBrPfU2k9F+H59/O6OswEF8nF1K/fn116NBB0tlLdZx7GYesmHM+iO9iXq9KlSo5fw4PD89wXdpf1EiZP8dy5cp53T7/NU1ISJCU/r3IzKFDhy64/NxLfJzrueeec/4S6MSJE1q8eLHeeustDRw4UHXr1lWzZs0u+CFnAAAgbwvNehMAAID87dFHH9Unn3zi3O7YsaPXgOdCSpUq5fz5Qp8kf+6ykiVLXvAx0s5uTJN2Rl+gBOrxH374YS1btkyS9J///MdrmHrfffel209G9u/fr8WLFzu3//zzzwzPEExJSdH06dOda7Ge+3rv2rXrYp+CT+6//3599913ks6eYVu1alVngFi3bl3Vr1/f2faLL77QqVOnnNsvvfSS+vTpo+LFi2vbtm266qqrAt537msipf86PP92ML4Ox48fr7lz5yo1NVXjx4/PcLvzvw7++ecf53rKaWc5Z0dmX4vnDmqzKz4+3uv2+a9piRIlJJ19L9K2bdy4sdq1a5fhY954440XXF6kSJELLo+KitLChQu1b98+rVmzRr/++qu2bdumOXPm6NSpU1q+fLkmTpyosWPHZvdpAQCAPIAzbQEAQIEXExOj66+/3rmd2Yc2pTl38HLo0CF99dVXzu34+Hiv2xkNaS7G+cOocweEOa1du3aqWrWqpLMfwHTuByOde7ZpVj788EOvf86flXM/sKxx48bOn7dv3+714WDS2TMzzz3LNRCvV4cOHVS8eHFJ0o4dO7yGjudfEuLcQXba+rT7fvrppxnuw5/Ohg0bKiQkxLn9wQcfeK0/93ZISEi6f6afG2rVquV8SF1cXFyG26UNP9OkfVCex+PRhAkTcqwvKx999JFz1q8xRtOnT3fWhYeHOx/wd+73eFxcnPr166cnnnjC678BAwaoXLlyF/3zYMuWLTp9+rQqV66su+++W8OHD9dHH33k9YFqP//8sz9PEwAAWIgzbQEAACT997//1f/+9z+FhYUpJiYmy+179uyp8ePHO8O6jh07qnfv3oqKitKMGTN04sQJSWfPWnzsscf87rvkkku8bg8YMECtWrVSaGio7rzzTl1xxRV+7yMjISEheuihhzRs2DBJUlJSkiSpQYMGuvrqq7P9OOcOYcuVK6fY2Nh02/zxxx9at26dJOmXX37Rpk2bVLduXT3yyCN6++239c8//0iSunbtqk8++UTXXHONjh49qmXLlqlZs2Z69dVXJZ29bENYWJhOnz4tSRoxYoQ2btyosLAwNWvWTA0aNMiyt1ChQurcubPeffddSf93hm9YWJjXtU2l9Ncjbtu2rVq3bq1NmzZ5Xf/3fP68r6VLl1avXr00ZcoUSWeHwwkJCYqJidGaNWv09ddfO9v26NFDpUuXzvI554QxY8ZoxowZSklJyXCb+vXrO9e/lc4OzFu2bKkdO3Zo06ZNuZWazjfffKPmzZurSZMmWrlypXPmtXT2azDtbODBgwdr3rx5Msbo999/19VXX60OHTqofPnySkxM1ObNm7V8+XKdPHlSPXr0uKiGJ554QmvXrlXz5s1VpUoVlS1bVvv37/e61Mj5Q28AAJD3MbQFAADQ2etJZnRNyQspUaKEZs+erXbt2ikhIUH//POPJk2a5LWN2+3WxIkT1bRpU7/7qlWrpmuvvVa//PKLpLMfdpR2yYJq1arl6NBWkvr27auxY8c6A1vp4j6AbM2aNfrf//7n3H7kkUc0YsSIdNvt3LlTl112mXN76tSpeuWVVxQdHa1Zs2apc+fOOn78uIwxmjt3rubOnetsm3btXensWZC333675syZI0nasGGDNmzYIEl68cUXszW0lc6eSZw2tE1zxx13qGzZsl7L7rzzTtWpU8f5YKrVq1c7H0DXs2fPdGfBpvH3fX3ttdf022+/OdeL/eabb/TNN994bXPTTTfp9ddfz8azzRnR0dF64IEH9MYbb2S4zSWXXKJu3brpo48+kiQlJibqs88+kyS1adPG61rCualZs2ZaunSpli5d6rW8WrVqeuGFF5zbjRs31ptvvqlHH31UZ86c0Z9//qnXXnstYB1Hjx7NcPgfGRmZrX8dAAAA8hYujwAAAOCjJk2aaMuWLRo8eLCuuuoqFS5cWOHh4apataq6deumVatWafDgwQHb3+zZs9W+fXuVKlUq4Ne/zUrp0qXVtWtX53ZkZKTX7ayce5at2+1Wz549L7hdjRo11KRJE+f29OnTnbNl27Rpo61bt2rIkCGqW7euihYtqrCwMFWqVElt27ZVmzZtvB5r8uTJ6tmzp8qXL5/htXOz0rBhw3TXo73QsDosLExLlixRr169VLp0aUVEROjqq6/We++9pzFjxmS6D3/e1yJFiui7777T+++/r9jYWJUqVUqhoaEqWbKkmjZtqnfffVfLli1T0aJFL+pxA23EiBEZXrM1zfvvv68nnnhCl1xyicLDw3XFFVdo4sSJmjdvXi5Vpjd69Gh98MEHuvbaaxUZGanSpUurZ8+eWrVqVboPKevfv79++eUX9evXT1dccYUKFy6s0NBQlS9fXk2bNtXTTz+tjRs3XnTDkCFD9Oijj+qGG25wXpuIiAhdeuml6tmzp9auXet1eRcAAJA/uMy5H80KAAAAZOD55593LpHQuXNnzZw5M8hFAAAAQP7E5REAAACQobi4OG3fvl179uzRv//9b2f5wIEDg1gFAAAA5G8MbQEAAJChRYsWpbscQKdOnXTTTTcFqQgAAADI/7imLQAAALLkdrtVtWpVDR06NMMP1QIAAAAQGFzTFgAAAAAAAAAswpm2AAAAAAAAAGARhrYAAAAAAAAAYBE+iMxHHo9H+/fvV7FixeRyuYKdAwAAAAAAAMByxhgdP35clSpVktud8fm0DG19tH//flWpUiXYGQAAAAAAAADymD///FOVK1fOcD1DWx8VK1ZM0tkXOCoqKsg1AAAAAAAAAGx37NgxValSxZktZoShrY/SLokQFRXF0BYAAAAAAABAtmV1uVU+iAwAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALBIa7AAAAAAAAHBxJq8aHdT9971xbFD3DwD5HWfaAgAAAAAAAIBFGNoCAAAAAAAAgEWsGtq+/fbbqlu3rqKiohQVFaWYmBh99dVXzvpmzZrJ5XJ5/ffggw96PcbevXvVtm1bFS5cWOXKldOQIUN05swZZ/20adNUokQJr/ts375dVapUUadOnZSSkpKjzxEAAAAAAAAAMmPVNW0rV66s559/XpdffrmMMfrggw/Url07/fLLL7rqqqskSX379tW4ceOc+xQuXNj5c2pqqtq2basKFSpo1apVOnDggHr06KGwsDA999xzF9znunXr1Lp1a7Vv317vvvuu3G6r5tgAAAAAAAAAChirJpR33HGH2rRpo8svv1xXXHGFnn32WRUtWlRr1qxxtilcuLAqVKjg/BcVFeWs++abb7Rt2zZ99NFHuuaaa9S6dWuNHz9ekyZNuuAZtEuWLNEtt9yiPn36aPLkyQxsAQAAAAAAAASdtVPK1NRUffzxxzp58qRiYmKc5dOnT1eZMmV09dVXa9iwYTp16pSzbvXq1apTp47Kly/vLGvVqpWOHTumrVu3ej3+nDlz1LZtW40cOVIvvPBCzj8hAAAAAAAAAMgGqy6PIEmbN29WTEyMkpKSVLRoUc2ZM0e1a9eWJHXt2lXR0dGqVKmSNm3apKFDh2rHjh2aPXu2JCkuLs5rYCvJuR0XF+csO3HihDp16qThw4dr6NCh2epKTk5WcnKyc/vYsWOSJI/HI4/H4/sTBgAAAADgYpng7p7/HwwAvsnuz0/rhrZXXnmlNmzYoMTERM2aNUs9e/bU8uXLVbt2bfXr18/Zrk6dOqpYsaKaN2+unTt3qkaNGtneR6FChdS4cWNNnjxZXbp0Ua1atbK8z4QJEzR27Nh0yw8dOqSkpKRs7xsAAAAAAH+FpxQP6v7j4+ODun8AyKuOHz+ere2sG9qGh4frsssukyTVr19f69at02uvvaZ333033baNGjWSJP3++++qUaOGKlSooLVr13ptc/DgQUlShQoVnGUhISGaO3euOnTooNjYWC1dujTLwe2wYcM0aNAg5/axY8dUpUoVlS1b1uu6ugAAAAAA5LSUnYlB3X+5cuWCun8AyKsiIyOztZ11Q9vzeTwer8sSnGvDhg2SpIoVK0qSYmJi9Oyzzyo+Pt45gCxevFhRUVHOJRbSREREaPbs2br77rsVGxurJUuWpNvm/O0jIiLSLXe73XyAGQAAAAAgd7mCu3v+fzAA+Ca7Pz+t+ik7bNgwrVixQrt379bmzZs1bNgwLVu2TN26ddPOnTs1fvx4rV+/Xrt379b8+fPVo0cPNWnSRHXr1pUktWzZUrVr11b37t21ceNGff311xo5cqQGDBhwwYFrRESEPv/8czVq1EixsbHpPqwMAAAAAAAAAHKbVUPb+Ph49ejRQ1deeaWaN2+udevW6euvv9att96q8PBwffvtt2rZsqVq1qypwYMHq2PHjvriiy+c+4eEhGjBggUKCQlRTEyM7rvvPvXo0UPjxo3LcJ/h4eGaNWuWbrzxRsXGxmrLli258VQBAAAAAAAA4IJcxpggf+Zk3nTs2DEVL15ciYmJXNMWAAAAAJCrJq8aHdT9970x/Qd1AwCylt2ZolVn2gIAAAAAAABAQcfQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACwSGuwAAAAAAEDOmLxqdFD33/fGsUHdPwAAeRVn2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEVCgx0AAAAAAACQmyavGh20ffe9cWzQ9g0g7+BMWwAAAAAAAACwCENbAAAAAAAAALAIQ1sAAAAAAAAAsAhDWwAAAAAAAACwCENbAAAAAAAAALAIQ1sAAAAAAAAAsAhDWwAAAAAAAACwCENbAAAAAAAAALBIaLADACCvmrxqdFD33/fGsUHdPwAAAAAAyBmcaQsAAAAAAAAAFmFoCwAAAAAAAAAWseryCG+//bbefvtt7d69W5J01VVXadSoUWrdurUkKSkpSYMHD9bHH3+s5ORktWrVSm+99ZbKly/vPMbevXv10EMPaenSpSpatKh69uypCRMmKDT07FOdNm2aHnvsMSUkJDj32b59u1q2bKkbbrhB06dPV3h4eK49ZwDIKVy+AQAAAACAvMmqM20rV66s559/XuvXr9dPP/2kW265Re3atdPWrVslSY8//ri++OILffbZZ1q+fLn279+vDh06OPdPTU1V27ZtlZKSolWrVumDDz7QtGnTNGrUqAz3uW7dOt1888267bbb9MknnzCwBQAAAAAAABBUVg1t77jjDrVp00aXX365rrjiCj377LMqWrSo1qxZo8TERE2ZMkUvv/yybrnlFtWvX19Tp07VqlWrtGbNGknSN998o23btumjjz7SNddco9atW2v8+PGaNGmSUlJS0u1vyZIluuWWW9SnTx9NnjxZbrdVLwcAAAAAAACAAsjaKWVqaqo+/vhjnTx5UjExMVq/fr1Onz6tFi1aONvUrFlTVatW1erVqyVJq1evVp06dbwul9CqVSsdO3bMOVs3zZw5c9S2bVuNHDlSL7zwQu48KQAAAAAAAADIglXXtJWkzZs3KyYmRklJSSpatKjmzJmj2rVra8OGDQoPD1eJEiW8ti9fvrzi4uIkSXFxcV4D27T1aevSnDhxQp06ddLw4cM1dOjQbHUlJycrOTnZuX3s2DFJksfjkcfjuejnCSAfMMHdfZY/e2zvAwAAOY/fB/Iv3lv/BPH1y/OvHQC/ZPdngHVD2yuvvFIbNmxQYmKiZs2apZ49e2r58uUB3UehQoXUuHFjTZ48WV26dFGtWrWyvM+ECRM0dmz6D9U5dOiQkpKSAtoHIG8ITyke1P3Hx8dnut72PgAAkPP4fSD/4r31TzBfv7z+2gHwz/Hjx7O1nXVD2/DwcF122WWSpPr162vdunV67bXXdO+99yolJUUJCQleZ9sePHhQFSpUkCRVqFBBa9eu9Xq8gwcPOuvShISEaO7cuerQoYNiY2O1dOnSLAe3w4YN06BBg5zbx44dU5UqVVS2bFlFRUX59ZwB5E0pOxODuv9y5cplut72PgAAkPP4fSD/4r31TzBfv7z+2gHwT2RkZLa2s25oez6Px6Pk5GTVr19fYWFh+u6779SxY0dJ0o4dO7R3717FxMRIkmJiYvTss88qPj7e+SG4ePFiRUVFqXbt2l6PGxERodmzZ+vuu+9WbGyslixZkm6b87ePiIhIt9ztdvMBZkBB5Qru7rP82WN7HwAAyHn8PpB/8d76J4ivX55/7QD4Jbs/A6wa2g4bNkytW7dW1apVdfz4cc2YMUPLli3T119/reLFi6tPnz4aNGiQSpUqpaioKD388MOKiYnRDTfcIElq2bKlateure7du2vixImKi4vTyJEjNWDAgAsOXCMiIvT555+rU6dOzuD2qquuyu2nDQAAAAAAAAAOq4a28fHx6tGjhw4cOKDixYurbt26+vrrr3XrrbdKkl555RW53W517NhRycnJatWqld566y3n/iEhIVqwYIEeeughxcTEqEiRIurZs6fGjRuX4T7Dw8M1a9Ys3XPPPc7g9uqrr87x5woAAAAAAAAAF2LV0HbKlCmZro+MjNSkSZM0adKkDLeJjo7WwoULM1zfq1cv9erVy2tZWFiY5syZc1GtAAAAAAAAAJATrBraAgAAAMD5Jq8aHbR9971xbND2DQAACi6ufg0AAAAAAAAAFuFMWwAAAAAAACCHBfNfjkj865G8hjNtAQAAAAAAAMAinGkLAAAAAAgKzjoDAODCONMWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACwSGuwAAAAAAAAAAME1edXooO6/741jg7p/23CmLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIRr2gIAAAAAgIDi2pgA4B/OtAUAAAAAAAAAi3CmLQAAAAAAAPKFYJ7lzRneCCSGtgCAoOCXKQBAfsA/AQcAADmByyMAAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARRjaAgAAAAAAAIBFGNoCAAAAAAAAgEUY2gIAAAAAAACARUKDHQAAmZm8anTQ9t33xrFB2zcAAAAAACi4ONMWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAswtAWAAAAAAAAACzC0BYAAAAAAAAALMLQFgAAAAAAAAAsEhrsAAAAAAAAbDN51eig7r/vjWODun8AQHBxpi0AAAAAAAAAWMSqM20nTJig2bNn63//+58KFSqkG2+8US+88IKuvPJKZ5tmzZpp+fLlXvd74IEH9M477zi39+7dq4ceekhLly5V0aJF1bNnT02YMEGhoWef7rRp0/TYY48pISHBuc/27dvVsmVL3XDDDZo+fbrCw8Nz9skCAACch7O6AAAAAEiWDW2XL1+uAQMG6Prrr9eZM2c0fPhwtWzZUtu2bVORIkWc7fr27atx48Y5twsXLuz8OTU1VW3btlWFChW0atUqHThwQD169FBYWJiee+65C+533bp1at26tdq3b693331XbjcnIJ+P/xMJAAAAAAAA5A6rhraLFi3yuj1t2jSVK1dO69evV5MmTZzlhQsXVoUKFS74GN988422bdumb7/9VuXLl9c111yj8ePHa+jQoRozZky6M2iXLFmidu3aqX///nrhhRcC/6QAAAgw/iINQKDxcwUAAMAuVp9SmpiYKEkqVaqU1/Lp06erTJkyuvrqqzVs2DCdOnXKWbd69WrVqVNH5cuXd5a1atVKx44d09atW70eZ86cOWrbtq1GjhzJwBYAAAAAAACAFaw60/ZcHo9Hjz32mG666SZdffXVzvKuXbsqOjpalSpV0qZNmzR06FDt2LFDs2fPliTFxcV5DWwlObfj4uKcZSdOnFCnTp00fPhwDR06NMue5ORkJScnO7ePHTvmdHo8Ht+faF5hgrv7AvEa48KC+LWX5ded7d8X9GUoz/9Msf29he94bxEstn/t2XzM4LXLVJ7us7lNoi8LNvdxvM1hNr+3fF9kqqB8b2T3eVo7tB0wYIC2bNmilStXei3v16+f8+c6deqoYsWKat68uXbu3KkaNWpk+/ELFSqkxo0ba/LkyerSpYtq1aqV6fYTJkzQ2LHp/9nWoUOHlJSUlO395lXhKcWDuv/4+Pig7h/BE8yvvay+7mz/vqAvY3n9Z4rt7y18x3ubf329fUZQ99+qVtdM19v+tWfzMYPXLnN5uc/mNom+rNjcx/E2Z9n83vJ9kbmC8r1x/PjxbG1n5dB24MCBWrBggVasWKHKlStnum2jRo0kSb///rtq1KihChUqaO3atV7bHDx4UJK8roMbEhKiuXPnqkOHDoqNjdXSpUszHdwOGzZMgwYNcm4fO3ZMVapUUdmyZRUVFXXRzzGvSdmZGNT9lytXLqj7R/AE82svq687278v6MtYXv+ZYvt7C9/x3uZftr+39GXM5jaJPn9l1mdzm0RfVmzuy+vH2ymrg3sd8j4xmV+H3eb3lu+LzOX1743sioyMzNZ2Vg1tjTF6+OGHNWfOHC1btkzVq1fP8j4bNmyQJFWsWFGSFBMTo2effVbx8fHOm7148WJFRUWpdu3aXveNiIjQ7Nmzdffddys2NlZLlixJt82520ZERKRb7na75XZbfWngwHAFd/cF4jXGhQXxay/Lrzvbvy/oy1Ce/5li+3sL3/He+iWYH6aV5Qdp2f7e0pchm9sk+vyVaZ/NbRJ9WbC5L68fb3lvM2Zzm5QP+vKJ7D5Pq4a2AwYM0IwZMzRv3jwVK1bMuQZt8eLFVahQIe3cuVMzZsxQmzZtVLp0aW3atEmPP/64mjRporp160qSWrZsqdq1a6t79+6aOHGi4uLiNHLkSA0YMOCCQ9eIiAh9/vnn6tSpkzO4veqqq3L1eQMAAAAAAEjB/UtIKRt/EQkgV1g1wn777beVmJioZs2aqWLFis5/n3zyiSQpPDxc3377rVq2bKmaNWtq8ODB6tixo7744gvnMUJCQrRgwQKFhIQoJiZG9913n3r06KFx48ZluN/w8HDNmjVLN954o2JjY7Vly5Ycf64AAAAAAAAAcCFWnWlrTOYfU1elShUtX748y8eJjo7WwoULM1zfq1cv9erVy2tZWFiY5syZk61OAAAAAAAAAMgpVp1pCwAAAAAAAAAFHUNbAAAAAAAAALAIQ1sAAAAAAAAAsAhDWwAAAAAAAACwiFUfRAYAgA0mrxod1P33vXFsUPcPAAAAAAguzrQFAAAAAAAAAIswtAUAAAAAAAAAizC0BQAAAAAAAACLMLQFAAAAAAAAAIswtAUAAAAAAAAAizC0BQAAAAAAAACLMLQFAAAAAAAAAIuE+nrH7du3a+rUqfrjjz909OhRGWO81rtcLn333Xd+BwIAAAAAAABAQeLT0PbDDz/U/fffr7CwMF155ZUqWbJkum3OH+ICAAAAAAAAALLm09B2zJgxuvbaa/XVV1+pTJkygW4CAAAAAAAAgALLp2va7t+/X71792ZgCwAAAAAAAAAB5tOZtnXr1tX+/fsD3QIAAJCjJq8aHdT9971xbFD3DwAAACBv8OlM25dffllTpkzRqlWrAt0DAAAAAAAAAAWaT2favvDCCypevLhuvvlm1a5dW1WrVlVISIjXNi6XS/PmzQtIJAAAAAAAAAAUFD4NbTdt2iSXy6WqVavqxIkT2rZtW7ptXC6X33EAAAAAAAAAUND4NLTdvXt3gDMAAAAAAAAAAJKP17QFAAAAAAAAAOQMn860TbN8+XJ9+eWX2rNnjyQpOjpabdu2VdOmTQMSBwAA8p7Jq0YHdf99bxwb1P0DAAAAgL98GtqmpKSoS5cumjt3rowxKlGihCQpISFBL730ktq3b6+ZM2cqLCwskK0AAAAAAAAAkO/5dHmEsWPHas6cORo8eLAOHDigI0eO6MiRI4qLi9MTTzyh2bNna9y4cYFuBQAAAAAAAIB8z6eh7YwZM9SzZ09NnDhR5cuXd5aXK1dOL7zwgnr06KEPP/wwYJEAAAAAAAAAUFD4NLQ9cOCAGjVqlOH6Ro0aKS4uzucoAAAAAAAAACiofBraVq5cWcuWLctw/fLly1W5cmVfmwAAAAAAAACgwPLpg8h69uyp0aNHq0SJEnr88cd12WWXyeVy6bffftOrr76qzz77TGPH8snNAAAA+cnkVaODuv++N/L7JQAAAAoGn4a2w4cP186dO/Xee+9p8uTJcrvPnrDr8XhkjFHPnj01fPjwgIYCAAAAAAAAQEHg09A2JCRE06ZN06BBg7Rw4ULt2bNHkhQdHa02bdqobt26AY0EAAAAAAAAgILCp6Ftmrp16zKgBQAAAAAAAIAA8umDyAAAAAAAAAAAOSNbZ9q63W653W6dOnVK4eHhcrvdcrlcmd7H5XLpzJkzAYkEAAAAAAAAgIIiW0PbUaNGyeVyKTQ01Os2gLyPTwIHAAAAAACwS7aGtmPGjMn0NgAAAAAAAAAgMHy6pu24ceO0ZcuWDNdv3bpV48aN8zkKAAAAAAAAAAoqn4a2Y8aM0aZNmzJcv2XLFo0dyz95BgAAAAAAAICL5dPQNitHjhxReHh4Tjw0AAAAAAAAAORr2bqmrSStWLFCy5Ytc27Pnj1bv//+e7rtEhIS9Mknn6hOnToBCQQAAAAAAACAgiTbQ9ulS5c6lzxwuVyaPXu2Zs+efcFta9eurTfeeCMwhQAAAAAAAABQgGR7aPvkk09q4MCBMsaoXLlyeuedd9SxY0evbVwulwoXLqzIyMiAhwIAAAAAAABAQZDtoW2hQoVUqFAhSdKuXbtUtmxZFS5cOMfCAAAAAAAAAKAgyvbQ9lzR0dGB7gAAAAAAAAAAyMehrSRt2rRJb7zxhn7++WclJibK4/F4rXe5XNq5c6ffgQAAAAAAAABQkLh9udOyZcvUsGFDLViwQJUqVdIff/yhSy+9VJUqVdKePXtUtGhRNWnSJNCtAAAAAAAAAJDv+TS0HTVqlC699FLt2LFDU6dOlSQNHz5cK1eu1KpVq7Rv3z7dc889AQ0FAAAAAAAAgILAp6Htzz//rD59+igqKkohISGSpNTUVElSo0aN9MADD+jpp58OXCUAAAAAAAAAFBA+DW1DQ0NVrFgxSVKJEiUUFham+Ph4Z/2ll16qbdu2BaYQAAAAAAAAAAoQn4a2l112mX777TdJZz9wrGbNmpozZ46z/ssvv1SFChUCUwgAAAAAAAAABYhPQ9s2bdpo5syZOnPmjCRp0KBBmj17ti6//HJdfvnlmj9/vh544IGAhgIAAAAAAABAQRDqy52efvppPfroo871bHv27KmQkBB9/vnnCgkJ0YgRI9SrV69AdgIAAAAAAABAgeDTmbZhYWEqXbq0XC6Xs+y+++7TnDlzNGvWLJ8HthMmTND111+vYsWKqVy5crrrrru0Y8cOr22SkpI0YMAAlS5dWkWLFlXHjh118OBBr2327t2rtm3bqnDhwipXrpyGDBninBUsSdOmTVOJEiW87rN9+3ZVqVJFnTp1UkpKik/9AAAAAAAAAOAvn4a2Tz75pH755ZdAt2j58uUaMGCA1qxZo8WLF+v06dNq2bKlTp486Wzz+OOP64svvtBnn32m5cuXa//+/erQoYOzPjU1VW3btlVKSopWrVqlDz74QNOmTdOoUaMy3O+6det0880367bbbtMnn3yi8PDwgD83AAAAAAAAAMgOn4a2b7zxhho0aKDLL79cTz/9tDZv3hyQmEWLFqlXr1666qqrVK9ePU2bNk179+7V+vXrJUmJiYmaMmWKXn75Zd1yyy2qX7++pk6dqlWrVmnNmjWSpG+++Ubbtm3TRx99pGuuuUatW7fW+PHjNWnSpAueQbtkyRLdcsst6tOnjyZPniy326eXBAAAAAAAAAACwqdr2sbHx2vOnDn65JNPNHHiRD333HOqWbOmOnfurHvuuUdXXnllQOISExMlSaVKlZIkrV+/XqdPn1aLFi2cbWrWrKmqVatq9erVuuGGG7R69WrVqVNH5cuXd7Zp1aqVHnroIW3dulXXXnuts3zOnDnq2rWrxowZo6FDh2bakpycrOTkZOf2sWPHJEkej0cej8f/J2s7E9zdF4jXOFhsf2+D2Gdzm0SfP2xuk+jzV6Z9NrdJ9GXB5j6b2yT6/GFzm0Sfvzhm+I4+39ncJtHnD5vbpHzQl09k93n6NLQtVqyYevTooR49eighIUGff/65Pv30U40fP15jxoxRnTp11LlzZz311FO+PLyks0/gscce00033aSrr75akhQXF6fw8PB016MtX7684uLinG3OHdimrU9bl+bEiRPq1KmThg8fnuXAVjp7vd2xY8emW37o0CElJSVd1HPLi8JTigd1//Hx8UHdf35m+3sbzD6b2yT6/GFzm0SfvzLrs7lNoi8rNvfZ3CbR5w+b2yT6/MUxw3f0+c7mNok+f9jcJuX9vvzi+PHj2drOp6HtuUqUKKE+ffqoT58+Onz4sD788EONHj1aI0aM8GtoO2DAAG3ZskUrV670N/GCChUqpMaNG2vy5Mnq0qWLatWqlen2w4YN06BBg5zbx44dU5UqVVS2bFlFRUXlSKNNUnYmBnX/5cqVC+r+8zPb39tg9tncJtHnD5vbJPr8lVmfzW0SfVmxuc/mNok+f9jcJtHnL44ZvqPPdza3SfT5w+Y2Ke/35ReRkZHZ2s7voa0knT59Wl999ZU++eQTffHFFzpx4oSqVKni8+MNHDhQCxYs0IoVK1S5cmVneYUKFZSSkqKEhASvs20PHjyoChUqONusXbvW6/EOHjzorEsTEhKiuXPnqkOHDoqNjdXSpUszHdxGREQoIiIi3XK3210wroPrCu7uC8RrHCy2v7dB7LO5TaLPHza3SfT5K9M+m9sk+rJgc5/NbRJ9/rC5TaLPXxwzfEef72xuk+jzh81tUj7oyyey+zx9fjXOnDmjhQsXqmfPnipbtqzuuusuLVu2TPfff79WrlypPXv2XPRjGmM0cOBAzZkzR0uWLFH16tW91tevX19hYWH67rvvnGU7duzQ3r17FRMTI0mKiYnR5s2bvU6pXrx4saKiolS7dm2vx4uIiNDs2bN1/fXXKzY2Vtu2bbvoZgAAAAAAAAAIJJ/OtO3Tp4/mzp2ro0ePqkyZMurSpYs6d+6sJk2ayOXyfSw/YMAAzZgxQ/PmzVOxYsWca9AWL15chQoVUvHixdWnTx8NGjRIpUqVUlRUlB5++GHFxMTohhtukCS1bNlStWvXVvfu3TVx4kTFxcVp5MiRGjBgwAXPlI2IiNDnn3+uTp06KTY2VkuWLNFVV13l83MAAAAAAAAAAH/4NLSdO3eu2rdvr3vvvVe33HKLQkJCAhLz9ttvS5KaNWvmtXzq1Knq1auXJOmVV16R2+1Wx44dlZycrFatWumtt95ytg0JCdGCBQv00EMPKSYmRkWKFFHPnj01bty4DPcbHh6uWbNm6Z577nEGt2kffgYAAAAAAAAAucmnoe3BgwcVGhqQy+F6McZkuU1kZKQmTZqkSZMmZbhNdHS0Fi5cmOH6Xr16OUPgNGFhYZozZ062WwEAAAAAAAAgJ/h0TducGNgCAAAAAAAAALJ5pm316tXldrv1v//9T2FhYapevXqW1651uVzauXNnQCIBAAAAAAAAoKDI1tC2adOmcrlccrvdXrcBAAAAAAAAAIGVraHttGnTMr0NAAAAAAAAAAgMn65p+9///le7d+/OcP2ePXv03//+19cmAAAAAAAAACiwfBra3n///Vq1alWG69esWaP777/f5ygAAAAAAAAAKKh8GtoaYzJdf/LkSYWGZuvKCwAAAAAAAACAc2R7srpp0yZt2LDBuf3999/rzJkz6bZLSEjQO++8oyuuuCIggQAAAAAAAABQkGR7aDtnzhyNHTtWkuRyufTuu+/q3XffveC2JUqU4Jq2AAAAAAAAAOCDbA9t+/Xrp9tvv13GGDVs2FDjxo1T69atvbZxuVwqUqSIatSoweURAAAAAAAAAMAH2Z6sVqxYURUrVpQkLV26VLVr11bZsmVzLAwAAAAAAAAACiKfPoisTp06OnDgQIbrN2/erKNHj/ocBQAAAAAAAAAFlU9D28cff1z9+vXLcP0DDzygJ554wucoAAAAAAAAACiofBraLlmyRHfeeWeG6++44w59++23PkcBAAAAAAAAQEHl09D20KFDKlOmTIbrS5curfj4eJ+jAAAAAAAAAKCg8mloW7FiRf3yyy8Zrl+/fj0fUgYAAAAAAAAAPvBpaHvXXXdpypQpmj9/frp18+bN09SpU9W+fXu/4wAAAAAAAACgoAn15U5jxozRt99+q/bt26tevXq6+uqrJUlbtmzRxo0bVatWLY0dOzagoQAAAAAAAABQEPh0pm3x4sW1Zs0ajRw5UqdPn9asWbM0a9YsnT59Wk8//bR+/PFHlShRIsCpAAAAAAAAAJD/+XSmrSQVKVJEY8eOzfCM2qNHj6pkyZI+hwEAAAAAAABAQeTz0PZCkpOTNX/+fE2fPl2LFi1SUlJSIB8eyNDkVaODtu++N3IpEAAAAAAAAASO30NbY4y+++47TZ8+XXPmzNGxY8dUtmxZde3aNRB9AAAAAAAAAFCg+Dy0Xb9+vaZPn66PP/5YcXFxcrlc6ty5swYOHKgbbrhBLpcrkJ0AAAAAAAAAUCBc1ND2jz/+0PTp0zV9+nT99ttvuuSSS9StWzc1bNhQ9957rzp27KiYmJicagUAAAAAAACAfC/bQ9uYmBitXbtWZcqU0d133633339fjRs3liTt3LkzxwIBAAAAAAAAoCDJ9tD2xx9/VPXq1fXyyy+rbdu2Cg0N6GeYAQAAAAAAAAAkubO74ZtvvqmKFSuqffv2qlChgh544AEtXbpUxpic7AMAAAAAAACAAiXbp8v2799f/fv3165duzR9+nTNmDFDkydPVoUKFRQbGyuXy8WHjwEXMHnV6KDuv++NY4O6fwAAAAAAAFycbJ9pm6Z69eoaOXKktm3bpnXr1qlz585atmyZjDHq37+/+vXrpwULFigpKSknegEAAAAAAAAgX7vooe256tevr5dffll//vmnvvnmG7Vq1UqffPKJ7rzzTpUpUyZQjQAAAAAAAABQYPg1tHUexO1WixYtNG3aNB08eFAzZ85U8+bNA/HQAAAAAAAAAFCgBGRoe67IyEjde++9mjdvXqAfGgAAAAAAAADyvYAPbQEAAAAAAAAAvmNoCwAAAAAAAAAWYWgLAAAAAAAAABZhaAsAAAAAAAAAFmFoCwAAAAAAAAAWCc3ORitWrPDpwZs0aeLT/QAAAAAAAACgoMrW0LZZs2ZyuVzZflBjjFwul1JTU30OAwAAAAAAAICCKFtD26VLl+Z0BwAAAAAAAABA2RzaNm3aNKc7AAAAAAAAAADig8gAAAAAAAAAwCrZOtP2QpKSkvT555/r559/VmJiojwej9d6l8ulKVOm+B0IAAAAAAAAAAWJT0PbPXv2KDY2Vrt371aJEiWUmJioUqVKKSEhQampqSpTpoyKFi0a6FYAAAAAAAAAyPd8ujzCkCFDlJiYqDVr1ujXX3+VMUaffPKJTpw4oRdeeEGFChXS119/HehWAAAAAAAAAMj3fBraLlmyRP3791fDhg3ldp99CGOMIiIiNGTIEDVv3lyPPfZYIDsBAAAAAAAAoEDwaWh76tQpVatWTZIUFRUll8ulxMREZ31MTIxWrlwZkEAAAAAAAAAAKEh8GtpWrVpV+/btkySFhobqkksu0Zo1a5z127ZtU2RkZGAKAQAAAAAAAKAA8emDyG655RbNmzdPo0ePliT16tVLEyZM0NGjR+XxePThhx+qR48eAQ0FAAAAAAAAgILAp6HtU089pXXr1ik5OVkREREaPny49u/fr1mzZikkJERdu3bVSy+9FOhWAAAAAAAAAMj3fBraVq1aVVWrVnVuR0ZG6v3339f7778fsDAAAAAAAAAAKIh8uqZt79699eOPP2a4fu3aterdu7fPUQAAAAAAAABQUPk0tJ02bZp27tyZ4fpdu3bpgw8+uOjHXbFihe644w5VqlRJLpdLc+fO9Vrfq1cvuVwur/9uu+02r22OHDmibt26KSoqSiVKlFCfPn104sQJZ/2yZcvkcrmUkJDgLNu/f7/q1KmjJk2aKDEx8aK7AQAAAAAAACBQfBraZmX//v0qVKjQRd/v5MmTqlevniZNmpThNrfddpsOHDjg/Ddz5kyv9d26ddPWrVu1ePFiLViwQCtWrFC/fv0yfLydO3eqcePGio6O1tdff63ixYtfdDcAAAAAAAAABEq2r2k7b948zZs3z7n93nvv6dtvv023XUJCgr799ltdf/31Fx3TunVrtW7dOtNtIiIiVKFChQuu2759uxYtWqR169apQYMGkqQ33nhDbdq00b///W9VqlTJa/tNmzapVatWuuWWW/TBBx8oNNSnS/wCAAAAAAAAQMBke0q5bds2ffbZZ5Ikl8ulH3/8UevXr/faxuVyqUiRImrSpIlefvnlwJb+f8uWLVO5cuVUsmRJ3XLLLXrmmWdUunRpSdLq1atVokQJZ2ArSS1atJDb7daPP/6o9u3bO8tXrVqlbt26qVu3bnrjjTfkcrky3W9ycrKSk5Od28eOHZMkeTweeTyeQD5FO5ng7j7L1ziIfTa3SfT5w+Y2iT5/2Nwm0eevTPtsbpPoy4LNfTa3SfT5w+Y2iT5/cczwHX2+s7lNos8fNrdJ+aAvn8ju88z20HbYsGEaNmyYJMntdmvKlCnq2rWrb3U+uu2229ShQwdVr15dO3fu1PDhw9W6dWutXr1aISEhiouLU7ly5bzuExoaqlKlSikuLs5refv27XXvvffqzTffzNa+J0yYoLFjx6ZbfujQISUlJfn+pPKI8JTgXjYiPj4+0/XB7LO5TaLPHza3SfT5w+Y2iT5/ZdZnc5tEX1Zs7rO5TaLPHza3SfT5i2OG7+jznc1tEn3+sLlNyvt9+cXx48eztZ1P1wMI1uS7c+fOzp/r1KmjunXrqkaNGlq2bJmaN29+UY/Vrl07zZkzR99//71uvvnmLLcfNmyYBg0a5Nw+duyYqlSporJlyyoqKuqi9p0XpewM7ge0nT+MP18w+2xuk+jzh81tEn3+sLlNos9fmfXZ3CbRlxWb+2xuk+jzh81tEn3+4pjhO/p8Z3ObRJ8/bG6T8n5ffhEZGZmt7fy6iOuuXbv01Vdfac+ePZKk6OhotW7dWtWrV/fnYbPt0ksvVZkyZfT777+refPmqlChQrqp/JkzZ3TkyJF018F999139eSTT6p169ZauHChmjRpkum+IiIiFBERkW652+2W250jn+dml8yvHpHjsnyNg9hnc5tEnz9sbpPo84fNbRJ9/sq0z+Y2ib4s2Nxnc5tEnz9sbpPo8xfHDN/R5zub2yT6/GFzm5QP+vKJ7D5Pn4e2gwcP1muvvZburFu3263HHntM//73v3196Gzbt2+fDh8+rIoVK0qSYmJilJCQoPXr16t+/fqSpCVLlsjj8ahRo0Ze93W5XHrvvffkdrvVpk0bffnll2ratGmONwMAAAAAAABAZnwaYb/00kt65ZVX1KFDB61evVoJCQlKSEjQ6tWrdffdd+uVV17RK6+8ctGPe+LECW3YsEEbNmyQdPZM3g0bNmjv3r06ceKEhgwZojVr1mj37t367rvv1K5dO1122WVq1aqVJKlWrVq67bbb1LdvX61du1Y//PCDBg4cqM6dO6tSpUrp9udyufTOO++oR48eatOmjZYtW+bLywEAAAAAAAAAAePTmbaTJ0/WnXfeqU8//dRreaNGjfTxxx8rKSlJ7777rh5//PGLetyffvpJsbGxzu20a8j27NlTb7/9tjZt2qQPPvhACQkJqlSpklq2bKnx48d7XbZg+vTpGjhwoJo3by63262OHTvq9ddfz3CfLpdLkyZNktvtVtu2bbVgwQKvBgAAAAAAAADITT4NbXfv3q1HH300w/WtWrXSokWLLvpxmzVrJmNMhuu//vrrLB+jVKlSmjFjxkXtw+Vy6c0339Sbb76Z/VgAAAAAAAAAyAE+XR6hXLly2rhxY4brN27cqLJly/ocBQAAAAAAAAAFVbaHtitWrNChQ4ckSZ06ddL777+v559/XidPnnS2OXnypF544QW9//77uvfeewNfCwAAAAAAAAD5XLaHtrGxsVq8eLEkafz48WratKmGDx+ukiVLqlq1aqpWrZpKliypYcOGqWnTpho3blyORQMAAAAAAABAfpXta9qeex3YwoUL67vvvtO8efP01Vdfac+ePZKk2267TW3atNEdd9whl8sV+FoAAAAAAAAAyOd8+iCyNO3atVO7du0C1QIAAAAAAAAABd5FfRAZZ88CAAAAAAAAQM66qKHtfffdp5CQkGz9Fxrq10m8AAAAAAAAAFAgXdRktUWLFrriiityqgUAAAAAAAAACryLGtr27NlTXbt2zakWAAAAAAAAACjwLuryCAAAAAAAAACAnMXQFgAAAAAAAAAswtAWAAAAAAAAACyS7WvaejyenOwAAAAAAAAAAIgzbQEAAAAAAADAKgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCJWDW1XrFihO+64Q5UqVZLL5dLcuXO91htjNGrUKFWsWFGFChVSixYt9Ntvv3ltc+TIEXXr1k1RUVEqUaKE+vTpoxMnTjjrly1bJpfLpYSEBGfZ/v37VadOHTVp0kSJiYk5+RQBAAAAAAAAIFNWDW1PnjypevXqadKkSRdcP3HiRL3++ut655139OOPP6pIkSJq1aqVkpKSnG26deumrVu3avHixVqwYIFWrFihfv36ZbjPnTt3qnHjxoqOjtbXX3+t4sWLB/x5AQAAAAAAAEB2hQY74FytW7dW69atL7jOGKNXX31VI0eOVLt27SRJ//3vf1W+fHnNnTtXnTt31vbt27Vo0SKtW7dODRo0kCS98cYbatOmjf7973+rUqVKXo+5adMmtWrVSrfccos++OADhYZa9XIAAAAAAAAAKIDyzJRy165diouLU4sWLZxlxYsXV6NGjbR69Wp17txZq1evVokSJZyBrSS1aNFCbrdbP/74o9q3b+8sX7Vqlbp166Zu3brpjTfekMvlynT/ycnJSk5Odm4fO3ZMkuTxeOTxeAL1NO1lgrv7LF/jIPbZ3CbR5w+b2yT6/GFzm0SfvzLts7lNoi8LNvfZ3CbR5w+b2yT6/MUxw3f0+c7mNok+f9jcJuWDvnwiu88zzwxt4+LiJEnly5f3Wl6+fHlnXVxcnMqVK+e1PjQ0VKVKlXK2SdO+fXvde++9evPNN7O1/wkTJmjs2LHplh86dMjr8gz5VXhKcC8bER8fn+n6YPbZ3CbR5w+b2yT6/GFzm0SfvzLrs7lNoi8rNvfZ3CbR5w+b2yT6/MUxw3f0+c7mNok+f9jcJuX9vvzi+PHj2douzwxtA61du3aaM2eOvv/+e918881Zbj9s2DANGjTIuX3s2DFVqVJFZcuWVVRUVE6mWiFlZ3A/oO38Yfz5gtlnc5tEnz9sbpPo84fNbRJ9/sqsz+Y2ib6s2Nxnc5tEnz9sbpPo8xfHDN/R5zub2yT6/GFzm5T3+/KLyMjIbG2XZ4a2FSpUkCQdPHhQFStWdJYfPHhQ11xzjbPN+VP5M2fO6MiRI87907z77rt68skn1bp1ay1cuFBNmjTJdP8RERGKiIhIt9ztdsvtturz3HJG5lePyHFZvsZB7LO5TaLPHza3SfT5w+Y2iT5/Zdpnc5tEXxZs7rO5TaLPHza3SfT5i2OG7+jznc1tEn3+sLlNygd9+UR2n2eeeTWqV6+uChUq6LvvvnOWHTt2TD/++KNiYmIkSTExMUpISND69eudbZYsWSKPx6NGjRp5PZ7L5dJ7772nbt26qU2bNlq+fHnuPBEAAAAAAAAAyIRVZ9qeOHFCv//+u3N7165d2rBhg0qVKqWqVavqscce0zPPPKPLL79c1atX19NPP61KlSrprrvukiTVqlVLt912m/r27at33nlHp0+f1sCBA9W5c2dVqlQp3f5cLpfeeecdhYSEqE2bNvryyy/VrFmzXHq2AAAAAAAAAJCeVUPbn376SbGxsc7ttGvI9uzZU9OmTdOTTz6pkydPql+/fkpISFDjxo21aNEir2tBTJ8+XQMHDlTz5s3ldrvVsWNHvf766xnu0+VyadKkSXK73Wrbtq0WLFjg1QAAAAAAAAAAucmqoW2zZs1kjMlwvcvl0rhx4zRu3LgMtylVqpRmzJhxUftwuVx688039eabb158NAAAAAAAAAAEUJ65pi0AAAAAAAAAFAQMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCIMbQEAAAAAAADAIgxtAQAAAAAAAMAiDG0BAAAAAAAAwCJ5amg7ZswYuVwur/9q1qzprE9KStKAAQNUunRpFS1aVB07dtTBgwed9bt375bL5dKGDRucZcePH1dsbKxq166tffv25ebTAQAAAAAAAIB08tTQVpKuuuoqHThwwPlv5cqVzrrHH39cX3zxhT777DMtX75c+/fvV4cOHTJ8rEOHDik2NlYnT57U999/r8qVK+fGUwAAAAAAAACADIUGO+BihYaGqkKFCumWJyYmasqUKZoxY4ZuueUWSdLUqVNVq1YtrVmzRjfccIPX9n/++aduvfVWXXLJJZo3b56KFi2aK/0AAAAAAAAAkJk8N7T97bffVKlSJUVGRiomJkYTJkxQ1apVtX79ep0+fVotWrRwtq1Zs6aqVq2q1atXew1td+zYoSFDhqhBgwaaOXOmIiIistxvcnKykpOTndvHjh2TJHk8Hnk8ngA+Q0uZ4O4+y9c4iH02t0n0+cPmNok+f9jcJtHnr0z7bG6T6MuCzX02t0n0+cPmNok+f3HM8B19vrO5TaLPHza3SfmgL5/I7vPMU0PbRo0aadq0abryyit14MABjR07VjfffLO2bNmiuLg4hYeHq0SJEl73KV++vOLi4ryW9ejRQzfddJM+++wzhYSEZGvfEyZM0NixY9MtP3TokJKSknx+TnlFeErxoO4/Pj4+0/XB7LO5TaLPHza3SfT5w+Y2iT5/ZdZnc5tEX1Zs7rO5TaLPHza3SfT5i2OG7+jznc1tEn3+sLlNyvt9+cXx48eztV2eGtq2bt3a+XPdunXVqFEjRUdH69NPP1WhQoWy/Th33nmn5s6dq9mzZ6tTp07Zus+wYcM0aNAg5/axY8dUpUoVlS1bVlFRUdl/EnlUys7EoO6/XLlyma4PZp/NbRJ9/rC5TaLPHza3SfT5K7M+m9sk+rJic5/NbRJ9/rC5TaLPXxwzfEef72xuk+jzh81tUt7vyy8iIyOztV2eGtqer0SJErriiiv0+++/69Zbb1VKSooSEhK8zrY9ePBgumvgjhgxQnXr1lXXrl1ljNE999yT5b4iIiIueBkFt9sttzvPfZ7bxXMFd/dZvsZB7LO5TaLPHza3SfT5w+Y2iT5/Zdpnc5tEXxZs7rO5TaLPHza3SfT5i2OG7+jznc1tEn3+sLlNygd9+UR2n2eefjVOnDihnTt3qmLFiqpfv77CwsL03XffOet37NihvXv3KiYmJt19n376aY0ZM0bdunXTJ598kpvZAAAAAAAAAJChPHWm7RNPPKE77rhD0dHR2r9/v0aPHq2QkBB16dJFxYsXV58+fTRo0CCVKlVKUVFRevjhhxUTE+P1IWTnGjFihEJCQtStWzd5PB516dIll58RAAAAAAAAAHjLU0Pbffv2qUuXLjp8+LDKli2rxo0ba82aNSpbtqwk6ZVXXpHb7VbHjh2VnJysVq1a6a233sr0MZ966im53W51795dxhh17do1N54KAAAAAAAAAFxQnhrafvzxx5muj4yM1KRJkzRp0qQLrq9WrZqMMemWP/nkk3ryyScD0ggAAAAAAAAA/sjT17QFAAAAAAAAgPyGoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFiEoS0AAAAAAAAAWIShLQAAAAAAAABYhKEtAAAAAAAAAFgk3w5tJ02apGrVqikyMlKNGjXS2rVrnXXVqlXTq6++6tw2xuiJJ55QVFSUli1blvuxAAAAAAAAAPD/5cuh7SeffKJBgwZp9OjR+vnnn1WvXj21atVK8fHx6bZNTU1Vnz599N///ldLly5Vs2bNcj8YAAAAAAAAAP6/fDm0ffnll9W3b1/df//9ql27tt555x0VLlxY//nPf7y2S05OVqdOnfTtt9/q+++/V/369YNUDAAAAAAAAABnhQY7INBSUlK0fv16DRs2zFnmdrvVokULrV692ll24sQJtW3bVvv27dMPP/ygKlWqZPq4ycnJSk5Odm4nJiZKkhISEuTxeAL8LOzzz4nkrDfKQQkJCZmuD2afzW36f+3da3hU5bnG8XtNEgLGxISAnA0RKsQK4RDYlaqAHCoIVpEIskUuwFZKKWIQU4QGUOuBHhAqUqHu6kbAqm3lUODSiltEujkUQkJAlEA4Q4AECIGEZObeH7IzEoMIGTLrzZv790WyZjH+r8lMnuGdlbWgvkCY3AaoLxAmtwHqC9Tl+kxuA9T3XUzuM7kNUF8gTG4D1BcozYyqU1/VmdwGqC8QJrcBNb/PFmfOnAFQdrrWy3H4XXvUMIcPH0azZs2wfv163H777f7tTz/9ND799FNs2LABLVu2xJEjRxAZGYmdO3eiYcOG33m/06dPx4wZM6ozXURERERERERERGqBAwcOoHnz5t96u3VH2l6pvn374p///CdeeOEFzJo16zv3nzx5MlJSUvxf+3w+5OXlITY2Fo7jVGdqjXfmzBm0aNECBw4cQFRUlNs5FZjcBqgvECa3AeoLlMl9JrcB6guEyW2A+gJhchugvkCZ3GdyG6C+QJjcBqgvECa3AeoLlMl9JrcB5veZhCQKCgrQtGnTy+5n3aJtgwYNEBISgmPHjlXYfuzYMTRu3Nj/da9evfCLX/wCP/7xj+Hz+TB79uzL3m94eDjCw8MrbIuOjr5m3bVBVFSUsS9ck9sA9QXC5DZAfYEyuc/kNkB9gTC5DVBfIExuA9QXKJP7TG4D1BcIk9sA9QXC5DZAfYEyuc/kNsD8PlPccMMN37mPdRciq1OnDjp37oyPP/7Yv83n8+Hjjz+ucLoEoOxo2+XLl2PBggUYP358sFNFREREREREREREKrHuSFsASElJwYgRI5CUlISuXbvilVdeQWFhIUaOHFlp3969e2PFihUYOHAgfD4fXn31VReKRURERERERERERMpYuWg7ZMgQHD9+HGlpaTh69Cg6dOiA1atXo1GjRpfc/+6778Y//vEPDBgwACTx6quv6jy111B4eDimTZtW6fQSJjC5DVBfIExuA9QXKJP7TG4D1BcIk9sA9QXC5DZAfYEyuc/kNkB9gTC5DVBfIExuA9QXKJP7TG4DzO+riRySdDtCRERERERERERERMpYd05bERERERERERERkZpMi7YiIiIiIiIiIiIiBtGirYiIiIiIiIiIiIhBtGgrIiIiIiIiIiIiYhAt2oqIyDXl8/ncThAREakVNHNFRETspUVbMZreiNqpuLjY7YTLOnbsGA4fPux2xrfav38/MjIy3M64pC+++AKzZ892O+Nbeb1elJSUuJ0h1YSk2wk1mmaunUyeuZq3gTF55mre2k8zt+o0b+1k8rwFzJ65ps9bN2nRVox0+vRpAIDH4zFyqB0+fBhLly7FX/7yF2zZssXtnEr27t2LWbNm4ZlnnsHy5cvdzqlgx44duPvuu7Fu3Tq3Uy5p69at6Nq1K7744gu3Uy4pIyMD3bt3x4IFC5CXl+d2TgWZmZno0KEDJk6ciA0bNridU8muXbswbtw43HvvvZgxY4Zxj19OTg4WLFiAOXPmYNWqVW7nVJCdnY2XX34ZU6ZMwbvvvovCwkK3kyoo/146jmPkPyIPHDiAt956C6+88grWrFnjdk4lJs9czdvAmDxzNW8DY/LM1bwNjGZu1WneBsbkmat5GxiTZ67p89Z1FDFMVlYWb7jhBv7617/2b/N6vS4WVZSRkcFWrVoxKSmJN910E2+66SauWLHC7Sy/bdu2sXnz5rz77rvZrVs3Oo7DpUuXup3lN3LkSDqOw1atWnH9+vVu51SQnp7OiIgIPvHEE26nXNJXX33Fhg0b8qmnnmJRUZHbORWkp6ezbt26fPTRR9mjRw9OnTqVpDmv3czMTDZo0IAPPfQQx44dy7CwML744otuZ/llZGTwxhtvZM+ePdmjRw96PB4OHz6cGzZscDuNmZmZjI6O5l133cU77riDISEhTE5O5ocffuh2GsmymREaGlrhdevz+dwL+oaMjAzGxcWxW7duTEhIYFhYGBctWuR2lp/JM1fzNnCmzlzN28CYPHM1bwOjmVt1mreBMXnmat4GxuSZa/q8NYEWbcUoBw4cYMeOHXnLLbewfv36Fd7kmTDUdu/ezWbNmjE1NZX5+fnMyMjgmDFj+OCDD/Ls2bOuv2nZtWsXmzdvzsmTJ7O4uJh5eXns378/586d62rXxf7rv/6LqampHD16NGNjY7l27Vq3k0iS27dvZ2RkJH/5y1+SJEtLS7l161Z+/vnn3L59u8t1ZWbNmsVhw4aRJEtKSjhv3jympqbytdde465du1zr2rJlCyMjIzllyhSS5KRJk9iwYUOeOnWKpPtv5vPz8/mDH/yAkydP9m9LS0tjSkoKS0pKXCwrc+LECSYmJvofP5JcuXIlPR4PBw4cyDVr1rjWdu7cOfbv35/jxo3zb9uwYQM7d+7MPn368IMPPnCtjSQPHTrErl27slOnToyIiOCECRP8t7n9vCPJPXv2MC4ujqmpqTx//jxzc3OZlpbGTp068ejRo643mjxzNW+vDRNnruZtYEyeuZq3gdHMrTrN28CYPHM1bwNj+sw1ed6aQou2Ygyv18tXXnmFgwYN4po1a/jSSy8xKirKmKFWXFzMJ598ksnJybxw4YJ/+xtvvMGmTZvyzJkzrrWRZX3Dhg3jiBEjWFpa6t/+4IMPcvjw4Rw1ahT/8Ic/MC8vz8VK8p133mG3bt147tw5DhgwgDfeeCN37NjBqVOn8p133nGlqaioiB07dmSTJk145MgRkuT999/Pjh07sn79+oyIiODMmTNdabvYyJEjOXLkSJLknXfeyS5durBXr16Mjo5mv379uHLlyqA3HTt2jPXq1eNTTz3l37Z//362adOGM2bMCHrPpRw+fJiJiYlctWqVf9vIkSN5xx13sFOnThwzZowrj1253bt3s3PnzszKyqLP52NxcTEPHz7M73//+2zcuDEHDRrk6uv29ttv57PPPkvy65/B27dv51133cV+/fpx27ZtrnT5fD6+/fbbTE5O5ueff87FixczPDycTz75ZIV93FJSUsKpU6fy/vvv57lz5/zbV69ezSZNmvDo0aOutZFmz1zN22vHtJmreRsY02eu5m3gNHOvnuZtYEyeuZq3gakJM9fUeWsSLdqKUb788ksuXryYJJmXl8cXX3zRmKFWUlLCuXPncs6cOSS/fmOyd+9exsXF8cCBA650XWz79u0Vfn3q17/+NT0eD//zP/+TY8eOpeM4FT69d8OXX37JHj16+L8eMmQIw8PDGRsbyy+//NK1rk8++YRt2rTh0KFD2alTJ/bt25efffYZN23axDlz5tBxHM6bN8+VtvLnWlpaGn/605/y73//O/v06cPc3FySZY/pHXfcwcGDBwe9LS8vj59++mmFbcXFxRw6dCh/+MMf+re5uXiWk5PD6667jlOnTuW2bdv43HPPsV69epwxYwbnzJnDLl268N577/W/mQm2rVu30nEcfvzxx/5tu3fv5j333MNFixbRcRzOnz8/6F0+n48FBQXs3r27/+dGSUmJ/01zRkYGmzZtypSUlKC3ldu/f3+FX49btGgRw8PDjTn65913363wa5Bk2ZFoLVq0MOLoBlNnrubttWPizNW8rTrTZ67mbdVp5gZG87bqTJ+5mreBMXXmmj5vTaJFWzHOxcP++PHjlT6NLC0t5bJly3j8+PGgtx0+fLhS56FDhxgXF8ecnBz/tp07dwa97ZsyMjLYu3dvrly50t/1/vvvMzQ0lF988YWrbYmJif5fdxg2bBgjIiIYExPDTZs2Bb3l4ufbJ598wsaNG7N79+4VvtckOXHiRLZr144nT5507Q3p6tWr6TgO77zzTj722GMVbtuwYQMdx+G///1vV9rKXXxUSHh4ON944w1Xe8q9+eabvO6669i/f39GRkby/fff99+WmZlJx3G4bNkyV9pKSko4fPhwtm7dmq+++iqXLFnCmJgYjh07liQ5YcIEDh06lCUlJa489955550K5w7zer3+IzEWLlzImJgY7t+/P+hdl1JaWlrp6J+SkhK+/fbbzMzMDHrP+fPn/X8u/94VFBSwRYsW3Lp1q/+2jRs3BjvNz9SZq3l77ZgyczVvrz0TZ67mbWA0c6tG8zYwNWXmat5euZoyc2vKvHVTqNsXQpPa7fDhwzh06BBOnjyJ3r17w+PxwOPxoLS0FKGhoWjQoAFGjRoFAHjhhRdAEidPnsTs2bOxf//+oPWdOHECP/rRj9CoUSMA8Pf5fD6cOXMG586dQ506deA4DiZPnoyXX34Z+fn5iIqKguM41d73zccPANq1a4f//u//RpMmTfz7ezwe3HrrrWjQoEG1NV2qrU+fPnAcBx6PB+fPn0dMTAwKCgowfvx4/M///A/WrFmD3/3ud/jBD36A9evXo2vXrkHt69WrFwCgR48eWLFiBXbs2IGGDRtW2L9u3bq47rrrEBMTU63f00v19e7dGwDwox/9CKmpqZg5cyaio6NRWFiIiIgIAEBMTAw6duyIG264Ieht5c87n88Hj8cDkoiPj8eAAQOwatUqDBs2DOHh4UF53L7Z16tXLziOgxEjRvi/zw888AA6dOgAn88HkoiOjkbHjh0RGRkZ9L4+ffogNDQUqampmDt3LqZNm4bGjRtj7NixeP755wGUXWk4Pz8foaHVP7JLSkoQFhYGAP4rQg8ePBjr1q3DkCFD8Pe//x333HOP/+dMTEwMmjRp4n8eBrPvUkJCQpCcnAwAGDlyJADA6/Vi3rx52L17d9D76tat6/+z4zgoLS3F2bNnUVpaiuuuuw4A/DMjNze32n82mzxzNW+vXZ9pM1fz9tr2mTRzNW8Do5l77do0b6vWZ+LM1by9dn2mzVzT562xXFsullpv27ZtbNGiBW+99VaGhoayY8eOnDdvHgsKCkiywnlrjh8/zhdffJGO4wTt06pL9b322mv+vvKjG7Kzs9mkSRPm5+dz+vTpjIyMDMoVaL/r8SMr/4rSpEmT2L9//2o/N9G3tZ0+fZpk2Sd69erVY9OmTf2fnBUXF/ORRx4JygnHL9U3d+5cf9/F53MqN2bMGI4aNYrFxcXV/inktz33CgsLefz4cT7++OMMCQnhtGnTmJ2dzbNnzzItLY0JCQk8duxY0Nsuft5d/Ktd5b82F8yjGr7te1v+nN+zZw8bNGjAf/7zn/6/M23aNLZu3ZqHDh0Kel+HDh04f/58/znYDh48WOloh0cffZSpqan0+XzV+tzbvn0777vvPmZlZVW6be/evRw9ejTr1KnDP/3pTzx69CiLioqYmprKxMTEoJxL7HJ931RaWsqFCxcGdWZcSZ/P5+OJEyfYtGlT5uTkcMaMGbz++uuD8hoxeeZq3lZPnwkzV/P22veZMnM1bwOjmVu9bZq3V9dnyszVvL32fabMXNPnrcm0aCuuOH78OBMSEpiamsq9e/cyNzeXDz/8MP/jP/6DEyZM8P/QvfjN6PDhwxkVFXVFbx6C1UeWXRSiffv2TE5OZp06dbh582aj+siyX3mZOnUqo6Ojq/3XlS7X9sQTT/DcuXNcunQp77333gq/rhQsVXnsfvWrXzEmJsbV516XLl2YkpLCwsJCnj17ls899xzDw8MZFxfHxMRENmnShFu2bHGl7ZuP3cVvRjt27Mjhw4fT6/VW+z+Avquv/MraY8aMYWhoKPv3789+/fqxUaNGQXkuXu57e3FfuezsbD7zzDOMjo7mjh07qrVt7969vPnmm+k4Djt06HDJN5ZHjhzhs88+y7CwMLZq1YqJiYls0KBBtT/vrrTvYl6vl6NHj2ZUVFS1P3ZX23fu3Dnedttt7Nu3r3Ezw42Zq3lbfX1uz1zN2+rpM2Hmat4GRjM3OG2at1XvI4M/czVvq6/P7Zlr+rw1nRZtxRWZmZls2bJlhSugFhcXMy0tjV27duWUKVP85yby+XxcuHAhGzVqFLTzmVxN3/bt2+k4DuvVq8f09HTj+jZv3sxHHnmE8fHxQRkgl2tLSkryX9344k9Mg+lqHruNGzcyOTmZzZs3D9rw/a7H71e/+hWLiopIkunp6fzrX//Kv/3tb9y3b5+rbd987MrNnj2bX331VbW3XWnfhQsXmJeXx7lz5zI5OZnPPPNMUD75vtK+8sfv+PHjHDNmDNu0aVPtb1aKioo4ffp0PvDAA9y0aRO7du3KhISEb31ctmzZwiVLlnDx4sXcu3dvtbZVpY8kV65cyfj4+KAcsXI1fT6fj/v27aPjOAwPDw/aVcBNnrmat9XX5/bM1bytvj63Z67mbdVp5ganTfM28L5gz1zN2+rrc3vmmj5vTadFW3HFrl27GB8fz+XLl5MsO2l9+X8nTZrEDh06cO3atf799+zZw5ycHCP78vPz+dRTTwXlk/mq9B08eJDLli3jnj17jGhr3749P/vsM5LuXGH2ah67AwcO8L333uPu3buN6UtMTKx05WhT2i5+7MpvM6kvMTGR69at8+8f7Off1f7cy87O5sGDB6u9y+v18q9//Svfe+89kmU/077tH0JuvGavpq/coUOHgnZ18qr0/eY3vwnKETXlTJ65mrfV2+fmzNW8rd4+N2eu5m3VaeYGt03ztup9wZ65mrfV2+fmzDV93ppOi7biiqKiIiYlJXHAgAH+X+sqf/H6fD62a9eOjz76qP9rk/vK9zetb/jw4UFtupq2ix87E/vceuyutM+tx8/ktivt0/f20i7+9VqSPHHihP8fQl9++SXJstbPP/886D/vrrbvm0eemdZ34cKFoM81k2eu5m3195k8M/TY1cw+fW8Do5kbnDbN28D6yvc3qU0/VwLr0/pAzeRx+0JoUvv4fD6Eh4fjz3/+M9auXYuf/exnAIDQ0FCQhOM4uO+++5CbmwsAQbvifFX6+P9Xeg0PDzeu7/jx40Frutq28u+tqX1uPHZX0+fG42dy29X06Xt7aSEhIQC+vnp1bGws/vGPfyAyMhI//vGPkZWVhV/84hd48skncfbsWaP7CgsLje174oknUFBQENS5ZvLM1bwNTp/JM0OPXc3r0/c2cJq51d+meRtYX7Bnrn6uBKdP6wM1kxZtJeg8Hg+8Xi9uu+02vPXWW1iyZAkeffRRHDt2zL/P3r17ERMTA6/Xa3Sfz+czui/Yj5/Jbeqzt019gSt/c17+DwiSaNCgAVauXIno6Gi0b98eb731FubOnYvY2Fj1VbFv3rx5qF+/flDbTH7uad7a22dym/rsbasJfYA9M82NPs3b4PQFe+ba9Nipr+a01RQOy3/yiVST8k9QypWWliI0NBRnz55FcXEx0tPTMWzYMMTFxaF+/fqIjY3F0qVL8a9//Qvt2rVTn8F9Jrepz9429V3bNq/Xi5CQEJw5cwY+nw/R0dEV9h81ahSWLVuGtWvX4tZbb63WNvUFzufzweP5+jN5k557Jrepz9429dnbVhP7TJ8ZJvWZ3AaY/V7P9D6T29Rnb1tNpSNtpdqUf1JS/rkASf+LNicnB7fccgs2bdqEXr16ISsrC/3790ezZs1w4403YuPGjdX+olWfnW3qs7dNfdXTFhISgpycHCQkJOBf//qXf3+S+MMf/oA333wTH330UbX/A0h9gTlx4gSAr49oKG824blncpv67G1Tn71tNbnP9JlhQp/JbQCQnZ2N/Pz8Sh/gmvLcM7nP5Db12dtW413BeW9FrtquXbs4YcIEDho0iDNmzKhwZcf9+/ezQYMGHD16NH0+n/9k1OUnY/d6veozuM/kNvXZ26a+6m977LHHKlwUw+fz8ZNPPuFXX31VrW3quzZ9kZGR/MlPfuLfVv4cM+G5Z2qb+uxtU5+9bbb0mT4z3OozuY0k09PT6TgO33jjjUq3mfDcM7nP5Db12dtmAx1pK9dcZmYmunXrhvz8fPh8PqxatQpLliwBSZSUlGDp0qV45JFHsGDBAjiO4z+hfLnqPim7+uxsU5+9beoLTtv8+fMrdDiOgx49eqB169bV1qa+a2PHjh2oV68eMjMz8fjjjwMou1jLhQsXsGzZMgwfPhyvv/66K68Nk9vUZ2+b+uxts6Xvj3/8o9Ezw60+k9u2bduGH/7wh3j66acxatSoSrd/8MEHrr4XNbnP5Db12dtmDXfWisVW2dnZjIuL45QpU/zbRo8ezfHjx1fYr/wTlmBTX9WZ3EaqLxAmt5HqC4TJbaT6rpWVK1fylltu4UsvvcR27drx8ccf99924MABF8vMbiPVFwiT20j1BcLkNlJ9gTK5z9S2nTt3MjQ0lM8++yzJsqMDP/74Y77++uv8/PPPmZub69+uvprTpj5722wS6vaisdjD6/Xio48+Qq9evTBx4kT/Sajr1auH7du3o3v37oiLi8OYMWPQrVu3SiepVp+5fSa3qc/eNvXZ26a+a6tdu3bo3LkzHnvsMdSpUwdvvvkmUlJScPr0aXTt2hWjRo1CWFiY2tRXa9rUZ2+b+uzuM7HN5/Ph3XffhdfrxeDBgwEAffr0wcmTJ5GTk4PY2FjEx8fj97//Pdq3bx/UNtP7TG5Tn71t1gne+rDUBnv27OH27dv9X8+YMYN169blCy+8wLS0NA4ZMoQ333xzhfMBqq9m9Jncpj5729Rnb5v6rp3CwkK2b9+eW7duZWFhIefPn8/Y2Fg6jsOMjAyS7h0NbHKb+uxtU5+9beqzu8/UtqNHj/KnP/0pw8PDedttt3HQoEFMT0/nhQsX+Le//Y19+/ZlcnIyCwoKgt5mep/Jbeqzt80mWrSVa678pNJFRUXs378/V6xY4b/ts88+44033sgPP/zQrTz1WdpGqs/WNlJ9traR6gvUhQsXWFpayr59+/Kzzz4jSQ4ZMoRRUVH83ve+V+lUDmpTn+1t6rO3TX1295ncRpK5ubkcO3Ysk5KSuGPHjgq3zZo1i40bN+bBgwddqjO7z+Q2Un22ttlCp0eQgBw+fBhbtmzBhQsXEBcXh86dO8NxHHi9XoSHh2P58uXweDzw+XzweDyoX78+GjVqhPr166vP8D6T29Rnb5v67G1T37Xta9myJTp16uT/NdHOnTtj9+7dmD9/PtauXYvly5cjMzMTL730EkJDQ/G73/2u1rapz9429dnbpj67+0xu+2bfTTfdhKSkJDRs2BBTp07Fvn370KpVKwBlp1IKCQlB69atERMTgzp16lR7m+l9Jrepz942q7m9aiw1V0ZGBm+++WZ27dqVDRo0YFJSEt97770K+5QfoVTul7/8Jbt06cLjx4+rz+A+k9vUZ2+b+uxtU1/1902fPp2O4zA+Pp7//ve/SZL5+fl87bXXmJ2dXWvb1Gdvm/rsbVOf3X0mt31b37vvvuu//ZvvBUjyiSeeYJ8+fXj27Nla3Wdym/rsbbOdFm2lSnbv3s3mzZvz6aef5qlTp7h582aOGDGCo0aNYmlpaaUX7b59+zhp0iTGxMRw27Zt6jO4z+Q29dnbpj5729RXvX0lJSUkyZKSEo4dO5YbN24k+fWb5+q+Yq/Jbeqzt0199rapz+4+k9u+q+/b3g889dRTrF+/vv+cu7W1z+Q29dnbVhto0VauWnFxMVNSUvjQQw+xuLjYv/2NN95gbGwsT5w4UWH/TZs2cezYsUxMTGR6err6DO4zuU199rapz9429QW/L5hMbiPVZ2sbqT5b20j1BcrkPpPbyKvv27BhA0eNGsW2bdty69attbrP5Db12dtWW+ictnLVfD4fmjdvjoSEBNSpUwck4TgOunXrhuuvvx4lJSUV9k9KSsL58+cxdepUNGnSRH0G95ncpj5729Rnb5v6gt9X/nc8Hk+tblOfvW3qs7dNfXb3mdxWlb6uXbuioKAAzz77LJo1a1ar+0xuU5+9bbVGMFaGxT579uzx/7n8cPgjR46wdevW3L9/v/+2zZs3B72NVJ+tbaT6bG0j1WdrG6m+QF1p35YtW9T2Deqzs41Un61tpPoCZXKfyW2kPe8H9F60MvXZ2VYbBOdjK6nxjhw5go0bN2L16tXw+XyIj48HUHZlQMdxAACnT59Gfn6+/++kpaWhT58+OHnyJEiqz9A+k9vUZ2+b+uxtU597fb169TL2uReMNvXZ26Y+e9vUZ3efyW2B9Jn+fkDvRdVna1utFIyVYanZtm3bxri4ON5yyy284YYb2LZtWy5evJgnT54k+fWnLbt27WLDhg2Zl5fH5557jvXq1QvKpy3qs7NNffa2qc/eNvXZ3Wdym/rsbVOfvW3qs7vP5Db12dumPnvbaist2spl5ebmsm3btnzmmWeYnZ3NQ4cOcciQIUxISOC0adOYm5vr3/fYsWPs2LEjhwwZwjp16gTlRas+O9vUZ2+b+uxtU5/dfSa3qc/eNvXZ26Y+u/tMblOfvW3qs7etNtOirVxWVlYWW7ZsWelFmJqaynbt2nHmzJksLCwkSe7YsYOO47BevXpBu1Kg+uxsU5+9beqzt019dveZ3KY+e9vUZ2+b+uzuM7lNffa2qc/ettpMi7ZyWenp6WzevDnXrl1Lkjx37pz/tvHjxzM+Pp7btm0jWXYy6p///OfcuXOn+mpAn8lt6rO3TX32tqnP7j6T29Rnb5v67G1Tn919Jrepz9429dnbVptp0Va+U5cuXdizZ0//10VFRf4/JyUlcejQof6vz58/H9Q2Un22tpHqs7WNVJ+tbaT6AmVyn8ltpPpsbSPVZ2sbqb5AmdxnchupPlvbSPXZ2lZbedy+EJqYpbCwEAUFBThz5ox/2+uvv46srCwMGzYMABAeHo7S0lIAwF133YXCwkL/vnXr1lWfoX0mt6nP3jb12dumPrv7TG5Tn71t6rO3TX1295ncpj5729Rnb5t8TYu24rdjxw4MGjQI3bt3R0JCAhYtWgQASEhIwOzZs/HRRx8hOTkZJSUl8HjKnjq5ubmIiIhAaWkpSKrP0D6T29Rnb5v67G1Tn919Jrepz9429dnbpj67+0xuU5+9beqzt02+IRiH84r5srKyGBsbyyeffJKLFi1iSkoKw8LCuGXLFpJkYWEhly1bxubNm7Nt27a8//77+dBDDzEiIoKZmZnqM7jP5Db12dumPnvb1Gd3n8lt6rO3TX32tqnP7j6T29Rnb5v67G2TyhxSS+S1XV5eHh5++GG0bdsWs2fP9m/v2bMn2rVrhzlz5vi3FRQU4Pnnn0deXh7q1q2Ln/3sZ7j11lvVZ2ifyW3qs7dNffa2qc/uPpPb1Gdvm/rsbVOf3X0mt6nP3jb12dsmlxbqdoC4r6SkBKdOncLgwYMBAD6fDx6PB/Hx8cjLywMAsOyidYiMjMTLL79cYT/1mdtncpv67G1Tn71t6rO7z+Q29dnbpj5729Rnd5/Jbeqzt0199rbJpelRFzRq1Ahvv/027rzzTgCA1+sFADRr1sz/wnQcBx6Pp8JJqh3HUZ/hfSa3qc/eNvXZ26Y+u/tMblOfvW3qs7dNfXb3mdymPnvb1Gdvm1yaFm0FAPC9730PQNknKGFhYQDKPmHJzc317/Piiy/iT3/6k//qgcF84arPzjb12dumPnvb1Gd3n8lt6rO3TX32tqnP7j6T29Rnb5v67G2TynR6BKnA4/GApP9FWf5pS1paGp5//nls3boVoaHuPW3UZ2eb+uxtU5+9beqzu8/kNvXZ26Y+e9vUZ3efyW3qs7dNffa2ydd0pK1UUn5tutDQULRo0QK//e1vMXPmTGzevBmJiYku16nP1jZAfba2AeqztQ1QX6BM7jO5DVCfrW2A+mxtA9QXKJP7TG4D1GdrG6A+W9ukjJbNpZLyT1jCwsKwYMECREVFYd26dejUqZPLZWXUV3UmtwHqC4TJbYD6AmFyG6C+QJncZ3IboL5AmNwGqC8QJrcB6guUyX0mtwHqC4TJbYD6AmFym/w/inyLTZs20XEcZmVluZ1ySeqrOpPbSPUFwuQ2Un2BMLmNVF+gTO4zuY1UXyBMbiPVFwiT20j1BcrkPpPbSPUFwuQ2Un2BMLmttnPI/z8eWuQSCgsLERER4XbGt1Jf1ZncBqgvECa3AeoLhMltgPoCZXKfyW2A+gJhchugvkCY3AaoL1Am95ncBqgvECa3AeoLhMlttZkWbUVEREREREREREQMoguRiYiIiIiIiIiIiBhEi7YiIiIiIiIiIiIiBtGirYiIiIiIiIiIiIhBtGgrIiIiIiIiIiIiYhAt2oqIiIiIiIiIiIgYRIu2IiIiIiIiIiIiIgbRoq2IiIiIiIEcx8G4cePczhARERERF2jRVkRERERqnTfffBOO48BxHKxbt67S7STRokULOI6DAQMGVFvH+vXrMX36dJw6dara/h8iIiIiUvNo0VZEREREaq26deti8eLFlbZ/+umnOHjwIMLDw6v1/79+/XrMmDFDi7YiIiIiUoEWbUVERESk1urfvz/ee+89lJaWVti+ePFidO7cGY0bN3apTERERERqMy3aioiIiEit9fDDD+PkyZP46KOP/NsuXLiA999/H8OGDau0f2FhISZOnIgWLVogPDwcbdq0wW9/+1uQrLBf+floP/jgA9x2220IDw/H97//faxevdq/z/Tp0zFp0iQAQHx8vP90DTk5ORXu63L3ISIiIiJ20qKtiIiIiNRaLVu2xO23344lS5b4t61atQqnT5/G0KFDK+xLEvfddx9mzZqFe+65B7///e/Rpk0bTJo0CSkpKZXue926dRg7diyGDh2KmTNnoqioCA8++CBOnjwJABg0aBAefvhhAMCsWbOwcOFCLFy4EA0bNrzi+xARERERO4W6HSAiIiIi4qZhw4Zh8uTJOH/+POrVq4dFixahe/fuaNq0aYX9li1bhjVr1uD555/HlClTAAA///nPkZycjNmzZ2PcuHFo1aqVf/+dO3dix44d/m09e/ZEYmIilixZgnHjxqF9+/bo1KkTlixZgvvvvx8tW7as1PZd9yEiIiIidtKRtiIiIiJSqz300EM4f/48VqxYgYKCAqxYseKSp0ZYuXIlQkJCMH78+ArbJ06cCJJYtWpVhe29e/eusIjbvn17REVFYc+ePVfcdi3uQ0RERERqHh1pKyIiIiK1WsOGDdG7d28sXrwY586dg9frxeDBgyvtt2/fPjRt2hSRkZEVtickJPhvv9hNN91U6T5iYmKQn59/xW3X4j5EREREpObRoq2IiIiI1HrDhg3DT37yExw9ehT9+vVDdHR0wPcZEhJyye3fvGhZdd+HiIiIiNQ8Oj2CiIiIiNR6DzzwADweD/73f//3kqdGAIC4uDgcPnwYBQUFFbZ/8cUX/tuvluM4Vx8rIiIiItbToq2IiIiI1HrXX3895s2bh+nTp2PgwIGX3Kd///7wer149dVXK2yfNWsWHMdBv379rvr/GxERAQA4derUVf9dEREREbGXTo8gIiIiIgJgxIgRl7194MCB6NmzJ6ZMmYKcnBwkJibiww8/xNKlSzFhwoQKFwy7Up07dwYATJkyBUOHDkVYWBgGDhzoX8wVERERkdpJi7YiIiIiIlfA4/Fg2bJlSEtLw1/+8hf8+c9/RsuWLfGb3/wGEydOrNJ9dunSBc899xz++Mc/YvXq1fD5fNi7d68WbUVERERqOYe6ioGIiIiIiIiIiIiIMXROWxERERERERERERGDaNFWRERERERERERExCBatBURERERERERERExiBZtRURERERERERERAyiRVsRERERERERERERg2jRVkRERERERERERMQgWrQVERERERERERERMYgWbUVEREREREREREQMokVbEREREREREREREYNo0VZERERERERERETEIFq0FRERERERERERETGIFm1FREREREREREREDKJFWxERERERERERERGD/B9EE56Fl22XbAAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1400x1200 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create the conversion rate chart\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))\n", "\n", "# Chart 1: Conversion Rates\n", "months = monthly_data['date_period'].dt.strftime('%Y-%m')\n", "x_pos = np.arange(len(months))\n", "\n", "ax1.plot(x_pos, monthly_data['online_rate'] * 100, marker='o', linewidth=2, \n", "         label='Online Conversion Rate', color='#2E86AB')\n", "ax1.plot(x_pos, monthly_data['offline_rate'] * 100, marker='s', linewidth=2, \n", "         label='Offline Conversion Rate', color='#A23B72')\n", "ax1.plot(x_pos, monthly_data['total_rate'] * 100, marker='^', linewidth=2, \n", "         label='Total Conversion Rate', color='#F18F01')\n", "\n", "ax1.set_xlabel('Month', fontsize=12)\n", "ax1.set_ylabel('Conversion Rate (%)', fontsize=12)\n", "ax1.set_title('Monthly Conversion Rates (MFC + EPPI Combined)', fontsize=14, fontweight='bold')\n", "ax1.set_xticks(x_pos)\n", "ax1.set_xticklabels(months, rotation=45)\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Add value labels for all three rates\n", "for i, (online, offline, total) in enumerate(zip(monthly_data['online_rate'] * 100, \n", "                                                monthly_data['offline_rate'] * 100, \n", "                                                monthly_data['total_rate'] * 100)):\n", "    ax1.annotate(f'{online:.1f}%', (i, online), textcoords=\"offset points\", \n", "                xytext=(0,5), ha='center', fontsize=8)\n", "    ax1.annotate(f'{offline:.1f}%', (i, offline), textcoords=\"offset points\", \n", "                xytext=(0,5), ha='center', fontsize=8)\n", "    ax1.annotate(f'{total:.1f}%', (i, total), textcoords=\"offset points\", \n", "                xytext=(0,5), ha='center', fontsize=8, color='#F18F01')\n", "\n", "# Chart 2: Activation Numbers\n", "ax2.bar(x_pos, monthly_data['Activation'], color='#70AD47', alpha=0.7)\n", "ax2.set_xlabel('Month', fontsize=12)\n", "ax2.set_ylabel('Total Activation', fontsize=12)\n", "ax2.set_title('Monthly Activation Numbers', fontsize=14, fontweight='bold')\n", "ax2.set_xticks(x_pos)\n", "ax2.set_xticklabels(months, rotation=45)\n", "ax2.grid(True, alpha=0.3, axis='y')\n", "\n", "# Format y-axis\n", "ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1000:.0f}K'))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 25, "id": "bfeefe2c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jan-Jul Activation Summary by Year:\n", "==================================================\n", "2023: 1,692,852 activations\n", "   Online: 11.31%, Offline: 6.18%, Total: 17.49%\n", "2024: 1,797,910 activations\n", "   Online: 6.63%, Offline: 3.79%, Total: 10.42%\n", "2025: 1,664,062 activations\n", "   Online: 8.22%, Offline: 7.11%, Total: 15.33%\n", "\n", "Total activation across all years (Jan-Jul): 5,154,824\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Detailed Monthly Breakdown (Jan-Jul):\n", "============================================================\n", "\n", "2023:\n", "------------------------------\n", "January: 253,169\n", "February: 178,747\n", "March: 173,238\n", "April: 217,666\n", "May: 260,582\n", "June: 305,695\n", "July: 303,755\n", "Total: 1,692,852\n", "\n", "2024:\n", "------------------------------\n", "January: 253,727\n", "February: 222,742\n", "March: 247,863\n", "April: 249,961\n", "May: 251,617\n", "June: 294,614\n", "July: 277,386\n", "Total: 1,797,910\n", "\n", "2025:\n", "------------------------------\n", "January: 313,227\n", "February: 246,971\n", "March: 248,876\n", "April: 278,425\n", "May: 293,372\n", "June: 283,191\n", "Total: 1,664,062\n"]}], "source": ["# Extract year and month from date_period\n", "monthly_data['year'] = monthly_data['date_period'].dt.year\n", "monthly_data['month'] = monthly_data['date_period'].dt.month\n", "\n", "# Filter data for Jan-Jul (months 1-7) for each year\n", "jan_jul_data = monthly_data[monthly_data['month'].between(1, 7)]\n", "\n", "# Group by year and sum activation\n", "yearly_activation = jan_jul_data.groupby('year').agg({\n", "    'Activation': 'sum',\n", "    'online_users': 'sum',\n", "    'offline_users': 'sum'\n", "}).reset_index()\n", "\n", "# Calculate conversion rates for each year period\n", "yearly_activation['online_rate'] = yearly_activation['online_users'] / yearly_activation['Activation']\n", "yearly_activation['offline_rate'] = yearly_activation['offline_users'] / yearly_activation['Activation']\n", "yearly_activation['total_rate'] = yearly_activation['online_rate'] + yearly_activation['offline_rate']\n", "\n", "print(\"Jan-Jul Activation Summary by Year:\")\n", "print(\"=\" * 50)\n", "for _, row in yearly_activation.iterrows():\n", "    print(f\"{int(row['year'])}: {row['Activation']:,.0f} activations\")\n", "    print(f\"   Online: {row['online_rate']:.2%}, Offline: {row['offline_rate']:.2%}, Total: {row['total_rate']:.2%}\")\n", "\n", "print(f\"\\nTotal activation across all years (Jan-Jul): {yearly_activation['Activation'].sum():,.0f}\")\n", "\n", "# Create visualization\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))\n", "\n", "# Chart 1: Activation Numbers by Year (Jan-Jul)\n", "years = yearly_activation['year'].astype(str)\n", "activations = yearly_activation['Activation']\n", "\n", "bars = ax1.bar(years, activations, color=['#2E86AB', '#A23B72', '#F18F01'], alpha=0.8)\n", "ax1.set_xlabel('Year', fontsize=12, fontweight='bold')\n", "ax1.set_ylabel('Total Activation (Jan-Jul)', fontsize=12, fontweight='bold')\n", "ax1.set_title('Activation Numbers by Year (January - July)', fontsize=14, fontweight='bold')\n", "ax1.grid(True, alpha=0.3, axis='y')\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, activations):\n", "    height = bar.get_height()\n", "    ax1.annotate(f'{value:,.0f}', \n", "                xy=(bar.get_x() + bar.get_width() / 2, height),\n", "                xytext=(0, 3),\n", "                textcoords=\"offset points\",\n", "                ha='center', va='bottom',\n", "                fontsize=12, fontweight='bold')\n", "\n", "# Format y-axis\n", "ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1000:.0f}K'))\n", "\n", "# Chart 2: Conversion Rates by Year (Jan-Jul)\n", "x_pos = np.arange(len(years))\n", "width = 0.25\n", "\n", "bars1 = ax2.bar(x_pos - width, yearly_activation['online_rate'] * 100, width, \n", "                label='Online Rate', color='#2E86AB', alpha=0.8)\n", "bars2 = ax2.bar(x_pos, yearly_activation['offline_rate'] * 100, width, \n", "                label='Offline Rate', color='#A23B72', alpha=0.8)\n", "bars3 = ax2.bar(x_pos + width, yearly_activation['total_rate'] * 100, width, \n", "                label='Total Rate', color='#F18F01', alpha=0.8)\n", "\n", "ax2.set_xlabel('Year', fontsize=12, fontweight='bold')\n", "ax2.set_ylabel('Conversion Rate (%)', fontsize=12, fontweight='bold')\n", "ax2.set_title('Conversion Rates by Year (January - July)', fontsize=14, fontweight='bold')\n", "ax2.set_xticks(x_pos)\n", "ax2.set_xticklabels(years)\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3, axis='y')\n", "\n", "# Add value labels on bars\n", "for bars in [bars1, bars2, bars3]:\n", "    for bar in bars:\n", "        height = bar.get_height()\n", "        ax2.annotate(f'{height:.1f}%', \n", "                    xy=(bar.get_x() + bar.get_width() / 2, height),\n", "                    xytext=(0, 3),\n", "                    textcoords=\"offset points\",\n", "                    ha='center', va='bottom',\n", "                    fontsize=10, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Detailed monthly breakdown for Jan-Jul\n", "print(\"\\nDetailed Monthly Breakdown (Jan-Jul):\")\n", "print(\"=\" * 60)\n", "\n", "for year in [2023, 2024, 2025]:\n", "    year_data = jan_jul_data[jan_jul_data['year'] == year]\n", "    if len(year_data) > 0:\n", "        print(f\"\\n{year}:\")\n", "        print(\"-\" * 30)\n", "        total_activation = 0\n", "        for _, row in year_data.iterrows():\n", "            month_name = row['date_period'].strftime('%B')\n", "            activation = row['Activation']\n", "            total_activation += activation\n", "            print(f\"{month_name}: {activation:,.0f}\")\n", "        print(f\"Total: {total_activation:,.0f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "43740b0a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}